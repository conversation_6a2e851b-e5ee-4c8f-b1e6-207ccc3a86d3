import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/card/card_model.dart';
import 'package:rxdart/rxdart.dart';

class StatementPdfBloc extends BaseBloc {
  StatementPdfBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _pdfPath = BehaviorSubject<String?>();

  Stream<String?> get pdfPathStream => _pdfPath.stream;

  String? cifNumber;

  @override
  void init() async {
    super.init();
    cifNumber = await preferences.cifNumber;
  }

  @override
  dispose() {
    _pdfPath.close();
    super.dispose();
  }

  @override
  reload() {
    //setData(_transactionModel);
  }

  refreshView() {
    _pdfPath.safeAdd(_pdfPath.valueOrNull);
  }

  addPath(String? path) {
    _pdfPath.safeAdd(path);
  }

  Future<Uint8List?> getPdfAccountData({
    String? account,
    String? from,
    String? to,
  }) {
    Completer<Uint8List?> completer = Completer();

    ExportPdfTransactionRequest request =
        ExportPdfTransactionRequest((builder) {
      builder
        ..accountNo = account
        ..dateFrom = from
        ..dateTo = to;
    });
    repository.bankApi!
        .getTransactionManagementApi()
        .exportTransactionToPDF(exportPdfTransactionRequest: request)
        .then((res) {
      if (res.data != null) {
        String jsonData = new String.fromCharCodes(res.data!);
        if (jsonData.contains("\"success\":false,\"code\"")) {
          final data = jsonDecode(utf8.decode(res.data!));
          setMsgError = data['message'];
          completer.complete(null);
        } else {
          completer.complete(res.data);
        }
      }
    }).catchError((err) {
      completer.complete(null);
      handlerApiError(err);
    }).whenComplete(() {
      completeLoading();
    });
    return completer.future;
  }

  Future<Uint8List?> getPdfCardData({
    CardModel? card,
    String? from,
    String? to,
    String? statementPeriod,
  }) async {
    if (cifNumber == null) {
      cifNumber = await preferences.cifNumber;
    }
    Completer<Uint8List?> completer = Completer();
    final request = ExportPdfCardRequest((builder) {
      builder.cifNo = cifNumber;
      builder.accountNo = card?.accountNo;
      builder.cardNo = card?.cardNo;
      builder.refCardId = card?.refCardId;
      builder.productTypeCode = card?.productTypeCode;
      builder.period = statementPeriod;
      builder.brand = card?.brand ?? "DEBIT";
    });
    repository.bankApi!
        .getCardApi()
        .exportPdf(exportPdfCardRequest: request)
        .then((res) {
      if (res.data != null) {
        String jsonData = new String.fromCharCodes(res.data!);
        if (jsonData.contains("\"success\":false,\"code\"")) {
          final data = jsonDecode(utf8.decode(res.data!));
          setMsgError = data['message'];
          completer.complete(null);
        } else {
          completer.complete(res.data);
        }
      }
    }).catchError((err) {
      completer.complete(null);
      handlerApiError(err);
    }).whenComplete(() {
      completeLoading();
    });
    return completer.future;
  }
}
