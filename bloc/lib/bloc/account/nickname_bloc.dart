import 'package:collection/collection.dart';
import 'package:common/ks_common.dart' as common;
import 'package:common/model/strapi/strapi_models.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/nickname/nickname_config_model.dart';
import 'package:ksb_bloc/bloc/model/nickname/nickname_model.dart';
import 'package:ksb_common/model/enum_type.dart';
import 'package:rxdart/rxdart.dart';

enum AccountModuleType { MY_SHOP, NICKNAME }

class NicknameBloc extends BaseBloc {
  NicknameBloc(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);
  final _checkNickname = BehaviorSubject<bool>();

  Stream<bool> get checkNickname => _checkNickname.stream;

  final _registerStatus = BehaviorSubject<bool>();

  Stream<bool> get registerStream => _registerStatus.stream;

  final _nicknames = BehaviorSubject<common.LoadingWidgetModel>();

  Stream<common.LoadingWidgetModel> get nicknamesStream => _nicknames.stream;

  final _detailNickName = BehaviorSubject<DetailNickname>();

  Stream<DetailNickname> get detailNickNameStream => _detailNickName.stream;

  final _updateStatus = BehaviorSubject<bool>();

  Stream<bool> get updateStatusStream => _updateStatus.stream;

  final _editNickname = BehaviorSubject<bool>();

  Stream<bool> get editNicknameStream => _editNickname.stream;

  int pageIndex = 0;
  List<AccountModel> accounts = [];

  final _accounts = BehaviorSubject<List<AccountModel>>();

  Stream<List<AccountModel>> get accountsStream => _accounts.stream;

  final _numberNickName = BehaviorSubject<int>();

  Stream<int> get numberNickNameStream => _numberNickName.stream;

  int get numberNickName => _numberNickName.valueOrNull ?? 0;

  final _terms = BehaviorSubject<StrapiTerms>();

  Stream<StrapiTerms> get termsStream => _terms.stream;

  /// Account nick name displayed
  final _currentAccount = BehaviorSubject<AccountModel>();

  Stream<AccountModel> get currentAccountStream => _currentAccount.stream;
  String nameCustomer = '';

  final _linkAccounts = BehaviorSubject<List<AccountModel?>>();

  Stream<List<AccountModel?>> get linkAccountsStream => _linkAccounts.stream;

  final _nickNameError = BehaviorSubject<String>();

  Stream<String> get nickNameErrorStream => _nickNameError.stream;

  updateLinkAccounts(List<AccountModel?>? data) {
    _linkAccounts.safeAdd(data ?? []);
  }

  updateNickNameError(String? errorMessage) {
    _nickNameError.safeAdd(errorMessage ?? '');
  }

  void checkNicknameExist(String nickname, String accountNo) async {
    showScreenLoading();
    showLoading();
    try {
      var res = await repository.nicknameApi!
          .getAPINickNameApi()
          .checkNickname(nickname: nickname, accountNo: accountNo);
      if (res.data != null) {
        _checkNickname.safeAdd(true);
      }
    } catch (e) {
      _checkNickname.safeAdd(false);
      handlerApiError(e);
    } finally {
      completeScreenLoading();
      completeLoading();
    }
  }

  void registerNickname({
    String? nickname,
    ProfileInfo? profile,
    String? accountNo,
    String? referralCode,
    String? sourceAccount,
    String? accountName,
    String? phoneNumber,
    int? accountType,
    int? accountStatus,
    num? createFee,
    num? accountBalance,
  }) async {
    if (profile != null) {
      try {
        RegisterNickNameRequest request = RegisterNickNameRequest((builder) {
          builder.nickname = nickname;
          builder.accountBalance = accountBalance?.toInt();
          builder.sourceAccount = sourceAccount;
          builder.accountName = accountName;
          builder.accountNo = accountNo;
          builder.idCardNo = profile.idCardNo;
          builder.phoneNumber = profile.phoneNumber;
          builder.accountType = accountType;
          builder.accountStatus = accountStatus;
          builder.createFee = createFee?.toInt();
          builder.referralCode = referralCode;
        });
        var res = await repository.nicknameApi!
            .getAPINickNameApi()
            .registerNickName(registerNickNameRequest: request);
        if (res.data!.success!) {
          _registerStatus.safeAdd(true);
        } else {
          _registerStatus.safeAdd(false);
        }
      } catch (e) {
        _registerStatus.safeAdd(false);
        handlerApiError(e);
      } finally {
        completeScreenLoading();
      }
    } else {
      completeScreenLoading();
    }
  }

  Future<ProfileInfo?> getProfile() async {
    try {
      final result = await preferences.getProfileInfo();
      if (result == null || result.verifyEmail == null) {
        return result;
      } else {
        return null;
      }
    } catch (e) {
      handlerApiError(e);
      return null;
    }
  }

  Future<bool> updateStatusNickname(
      String nickname, NicknameStatus status) async {
    showLoading();
    try {
      var res = await repository.nicknameApi!
          .getAPINickNameApi()
          .updateStatusNickName(nickname: nickname, status: status.name);
      if (res.data?.success == true) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      handlerApiError(e);
      return false;
    } finally {
      completeLoading();
    }
  }

  void editNickname({
    required String oldNickname,
    required String newNickname,
    required String newSourceAccount,
  }) async {
    showScreenLoading();
    try {
      await Future.delayed(Duration(seconds: 1));
      var res = await repository.nicknameApi!.getAPINickNameApi().editNickName(
          editInForNickNameRequest: EditInForNickNameRequest(
        (b) {
          b.oldNickname = oldNickname;
          b.newNickname = newNickname;
          b.newSourceAccount = newSourceAccount;
        },
      ));
      if (res.data?.success == true) {
        _editNickname.safeAdd(true);
      } else {
        _editNickname.safeAdd(false);
      }
    } catch (e) {
      _editNickname.safeAdd(false);
      handlerApiError(e);
    } finally {
      completeScreenLoading();
    }
  }

  Future<StrapiTerms?> getTermsAndConditions() {
    showLoading();
    return repository.strApi!.getTermsAndConditions(id: '19').then((value) {
      safeAddData(_terms, value);
      return value;
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(() => completeLoading());
  }

  Future<NicknameConfigModel?> getNickNameConfig() async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    try {
      final result = await repository.nicknameApi!
          .getValidateConfigControllerApi()
          .getAllConfigShowApp();
      return NicknameConfigModel.fromGetAllValidateConfigShowAppResponse(
          result.data?.data);
    } catch (error) {
      handlerApiError(error);
    } finally {
      completeLoading();
    }
    return null;
  }

  Future<void> getAccounts({AccountModel? account}) async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    repository.bankApi!
        .getAccountControllerApi()
        .getPaymentAccountsByModule(moduleType: AccountModuleType.NICKNAME.name)
        .then((value) {
      final listNickNames = value.data?.data?.accounts;
      nameCustomer = value.data?.data?.customerName ?? '';
      if (listNickNames?.isNotEmpty == true) {
        _numberNickName.safeAdd(0);
        List<AccountModel> lst = <AccountModel>[];
        for (var item in listNickNames!) {
          if (item.nickName?.isNotEmpty == true) {
            _numberNickName.safeAdd(numberNickName + 1);
          }
          lst.add(AccountModel.fromPaymentAccountResponseV2(
            item,
            customerName: value.data?.data?.customerName,
          ));
        }
        final currentAcc = lst.firstWhereOrNull(
          (element) => element.accountNumber == account?.accountNumber,
        );
        _currentAccount.safeAdd(currentAcc ?? lst.first);
        _accounts.safeAdd(lst);
      } else {
        _accounts.safeAdd([]);
        _numberNickName.safeAdd(0);
      }
    }).catchError((error) {
      _numberNickName.safeAdd(0);
      handlerApiError(error);
    }).whenComplete(() => completeScreenLoading(isSubmit: true));
    completeScreenLoading();
  }

  Future<bool> hasEToken() async {
    final pinToken = await preferences.token ?? "";
    final secretKey = await preferences.secretKey ?? "";
    if (pinToken.isNotEmpty && secretKey.isNotEmpty) {
      return true;
    }
    return false;
  }

  @override
  void reload() {
    getAccounts();
    super.reload();
  }

  void onChangeIndexAcc(int index) {
    _currentAccount.safeAdd(accounts[index]);
  }

  @override
  void dispose() {
    _checkNickname.close();
    _currentAccount.close();
    _registerStatus.close();
    _nicknames.close();
    _editNickname.close();
    _updateStatus.close();
    _accounts.close();
    _terms.close();
    _linkAccounts.close();
    _numberNickName.close();
    _nickNameError.close();
    super.dispose();
  }
}
