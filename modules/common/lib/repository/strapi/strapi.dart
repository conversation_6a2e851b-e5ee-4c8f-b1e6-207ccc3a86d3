import 'dart:convert';
import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/model/detail_tip_model/detail_tip_model.dart';
import 'package:common/model/strapi/notification/promotion_model.dart';
import 'package:common/model/strapi/strapi_models.dart';
import 'package:common/model/strapi/umee/umee_group.dart';
import 'package:common/model/tip_model/tip_model.dart';
import 'package:common/repository/strapi/chatbot_message.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';

import 'model/faqs/strapi_faqs_model.dart';
import 'network_model.dart';

//https://crm.dev-ksapi.ssf.vn/swagger
//https://crm.stg-ksapi.ssf.vn/swagger
//https://ksg-cms-api-dev.ksb.rest/swagger/v1.0.0
class StrApi {
  late Dio dio;
  static const MODULE_FINANCE = 1;
  static const MODULE_FINANCE_CODE = "FINANCE";
  static const MODULE_INVEST_CODE = "INVEST";
  static const MODULE_BOND_CODE = "BOND";
  static const MODULE_RESIDENCE_CODE = "RESIDENCE";
  static const MODULE_KSCD_CODE = "KSCD";
  static const MODULE_UMEE_CODE = "UMEE";
  static const MODULE_UMEE_INVEST_CODE = "UMEE_INVEST";
  static const MODULE_UMEE_PREMIER = "UMEE_HOME_PREMIER";
  static const MODULE_KSHC_CODE = "KSHC";
  static const MODULE_KSHP_CODE = "KSHP";
  static const MODULE_UMEE_LOAN = "UMEE_LOAN";
  static const MODULE_UMEE_HOME_PLUS = "UMEE_HOME_PLUS";
  static const MODULE_UMEE_SSG = "UMEE_SSG";

  static const MODULE_UMEE_SECURITIES = 'UMEE_SECURITIES';
  static const MODULE_UMEE_SAVING = 'UMEE_SAVING';
  static const MODULE_UMEE_BOND = 'UMEE_BOND';
  static const MODULE_UMEE_CD = 'UMEE_CD';
  static const MODULE_UMEE_GOLD = 'UMEE_GOLD';
  static const MODULE_UMEE_HOME = 'UMEE_HOME';
  static const MODULE_UMEE_SHARE = 'UMEE_SHARE';
  static const MODULE_UMEE_FX = 'UMEE_FX';

  static const MODULE_INVEST = 2;
  static const MODULE_BOND = 3;
  static const MODULE_RESIDENT = 4;
  static const MODULE_KS_CD = 5; //chứng chỉ tiền gửi
  static const MODULE_KS_HC = 6; //KS Home Cop
  static const MODULE_KS_HP = 7; //KS HomePlus
  static const MODULE_KS_PREMIER = 8; //KSPremier
  static const MODULE_KS_VILLAS = 9; //Villas
  static const SUB_MODULE_DEPOSIT = 10; //Deposit
  static const QUERY_MODULE = "module.id";
  static const QUERY_GROUP = "group";
  static const GROUP_PAYMENT = 'PAYMENT';
  static const GROUP_HOME = 'HOME';
  static const QUERY_CODE = "module.code";
  static const GROUP_FINANCE = 'FINANCE';
  static const QUERY_MODULE_CODE = "_where[module.code]";
  static const QUERY_PAGE_CODE = "_where[page.code]";
  static const QUERY_CATEGORY_CODE = "_where[category.code]";

  static const PAGE_FINANCE_HOME = 'FINANCE_HOME';
  static const PAGE_FINANCE_DISCOVER = 'FINANCE_DISCOVER';
  static const PAGE_RESIDENCE_HOME = 'RESIDENCE_HOME';
  static const PAGE_INVEST_HOME = 'INVEST_HOME';
  static const PAGE_BOND_HOME = 'BOND_HOME';
  static const PAGE_KSCD_HOME = 'KSCD_HOME';
  static const PAGE_KSHC_HOME = 'KSHC_HOME';
  static const PAGE_KSHP_HOME = 'KSHP_HOME';
  static const PAGE_UMEE_HOME = 'UMEE_HOME_OFFER_1';
  static const PAGE_CODE_UMEE_HOME = 'UMEE_HOME';
  static const PAGE_CODE_UMEE_INVEST = 'UMEE_INVEST_HOME';
  static const PAGE_CODE_UMEE_PREMIER_HOME = 'UMEE_HOME_PREMIER_HOME';
  static const PAGE_CODE_UMEE_LOAN_HOME = 'UMEE_LOAN_HOME';
  static const PAGE_CODE_UMEE_HOME_PLUS_HOME = 'UMEE_HOME_PLUS_HOME';

  static const RESIDENCE_HOME_BANNER = 'RESIDENCE_HOME_BANNER';
  static const RESIDENCE_HOME_DEAL = 'RESIDENCE_HOME_DEAL';
  static const RESIDENCE_HOME_OFFER = 'RESIDENCE_HOME_OFFER';
  static const RESIDENCE_HOME_FAQ = 'RESIDENCE_HOME_FAQ';
  static const RESIDENCE_HOME_NEWS = 'RESIDENCE_HOME_NEWS';

  static const KSHC_HOME_BANNER = 'KSHC_HOME_BANNER';
  static const KSHC_HOME_OFFER = 'KSHC_HOME_OFFER';
  static const KSHC_HOME_FAQ = 'KSHC_HOME_FAQ';

  static const PREMIER_MODULE_CODE = 'PREMIER';
  static const PREMIER_PAGE_CODE = 'PREMIER_HOME';

  static const SSG_MODULE_CODE = 'SSG';
  static const SSG_PAGE_CODE = 'SSG_HOME';

  static const KLB_PAGE_CODE = 'KLB_HOME';

  static const HOME_VILLAS_MODULE_CODE = 'KSVL';
  static const HOME_VILLAS_PAGE_CODE = 'KSVL_HOME';
  static const DEPOSIT = 'SUNSHINE_HOME_DEPOSIT';

  static const KLB_NEWS = 'KLB_NEWS';

  //priority
  static const KLB_CUSTOMER_PRIORITY = 'KLB_CUSTOMER_PRIORITY';
  static const KLB_HOME_PRIORITY = 'KLB_HOME_PRIORITY';
  static const KLB_REQUIREMENT_PRIORITY = 'KLB_REQUIREMENT_PRIORITY';

  StrApi(
    Dio dio, {
    List<Interceptor>? interceptors,
  }) {
    this.dio = dio;
    if (interceptors != null) {
      this.dio.interceptors.addAll(interceptors);
    }
  }

  Future<StrapiToken?> getCustomTokenKeycloak(String ksBankToken) {
    return dio
        .get(
          '/auth/keycloak/callback',
          queryParameters: {
            "access_token": ksBankToken,
          },
        )
        .then((res) => res.data)
        .then((data) => data != null ? StrapiToken.fromMap(data) : null);
  }

  Future<StrapiToken?> getCustomToken(String ksBankToken) async {
    return dio
        .get(
          '/auth/is4/callback',
          queryParameters: {
            "access_token": ksBankToken,
          },
        )
        .then((res) => res.data)
        .then((data) => data == null ? null : StrapiToken.fromMap(data));
  }

  Future<List<StrapiSupportGroup>> getSupportGroups() {
    return dio.get('/supports-groups').then((res) => res.data).then((data) {
      return List<StrapiSupportGroup>.from(
          data?.map((item) => StrapiSupportGroup.fromMap(item)) ?? []);
    });
  }

  Future<List<NetworkModel>> getNetworks({String? keyword}) {
    Map<String, dynamic>? param;
    if (keyword != null && keyword.length > 0) {
      param = {
        "name_contains": keyword,
        "address.full_address_contains": keyword
      };
    }
    return dio
        .get('/networks', queryParameters: param)
        .then((res) => res.data)
        .then((data) {
      return List<NetworkModel>.from(
          data?.map((item) => NetworkModel.fromJson(item)) ?? []);
    });
  }

  Future<List<StrapiBanner>> getBanners({Map<String, dynamic>? param}) {
    return dio
        .get('/banners', queryParameters: param)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiBanner>.from(
            data?.map((item) => StrapiBanner.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiBanner>> getBannersByFilters({
    int? moduleId,
    int? start,
    int? limit,
    String? pageCode,
    String? group,
    String? moduleCode,
  }) {
    final Map<String, dynamic> params = {
      "_sort": "sort_order:ASC,published_at:DESC",
    };
    if (moduleId != null) {
      params['_where[module.id]'] = moduleId;
    }
    if (moduleCode != null) {
      params['_where[module.code]'] = moduleCode;
    }
    if (pageCode != null && pageCode.isNotEmpty == true) {
      params['_where[page.code]'] = pageCode;
    }
    if (start != null && start >= 0) {
      params['_start'] = start;
    }
    if (limit != null && limit >= 0) {
      params['_limit'] = limit;
    }
    if (group != null && group.isNotEmpty) {
      params[StrApi.QUERY_GROUP] = group;
    }
    return dio
        .get('/banners', queryParameters: params)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiBanner>.from(
            data?.map((item) => StrapiBanner.fromMap(item)) ?? []);
      },
    );
  }

  Future requestSupport(StrapiSupportRequest request) {
    Iterable<File> files = request.files?.keys ?? [];
    final attachments = files
        .map(
          (e) => MultipartFile.fromFileSync(
            e.path,
            contentType: MediaType('image', 'png'),
          ),
        )
        .toList();
    final data = FormData.fromMap(
      {
        "data": jsonEncode({
          "title": request.title,
          "group": request.group,
          "content": request.content,
          "sender_by": request.senderBy,
          "status": "NEW",
          "module": request.module
        }),
        if (attachments.length > 0) 'files.attachments': attachments
      },
    );
    return dio.post('/supports', data: data).then((res) => res.data);
  }

  //Xem các mã module ở đầu file
  //@ [MODULE_RESIDENT = 4]
  // @ [MODULE_FINANCE = 1]
  Future<List<StrapiFaqsModel>> getFaqs({
    int? moduleId,
    String? moduleCode,
    int? start,
    int? limit,
  }) {
    final Map<String, dynamic> params = {
      "_sort": "sort_order:ASC,published_at:DESC",
    };
    if (moduleId != null) {
      params['_where[module.id]'] = moduleId;
    }
    if (moduleCode != null) {
      params['_where[module.code]'] = moduleCode;
    }

    if (start != null && start >= 0) {
      params['_start'] = start;
    }
    if (limit != null && limit >= 0) {
      params['_limit'] = limit;
    }
    return dio
        .get('/faqs', queryParameters: params)
        .then((res) => res.data)
        .then((data) {
      return List<StrapiFaqsModel>.from(
        data?.map((item) => StrapiFaqsModel.fromJson(item)) ?? [],
      );
    });
  }

  //Xem các mã module ở đầu file
  //@ [MODULE_RESIDENT = 4]
  // @ [MODULE_FINANCE = 1]
  Future<List<StrApiFaqGroup>> getFaqGroups({
    int? moduleId,
    String? moduleCode,
    int? start,
    int? limit,
  }) {
    final Map<String, dynamic> params = {
      "_sort": "sort_order:ASC,published_at:DESC",
    };
    if (moduleId != null) {
      params['_where[module.id]'] = moduleId;
    }
    if (moduleCode != null) {
      params['_where[module.code]'] = moduleCode;
    }
    if (start != null && start >= 0) {
      params['_start'] = start;
    }
    if (limit != null && limit >= 0) {
      params['_limit'] = limit;
    }
    return dio
        .get('/faqs-groups', queryParameters: params)
        .then((res) => res.data)
        .then((data) {
      return List<StrApiFaqGroup>.from(
        data?.map((item) => StrApiFaqGroup.fromMap(item)) ?? [],
      );
    });
  }

  Future<List<StrapiPromotion>> getPromotions({Map<String, dynamic>? params}) {
    return dio
        .get('/promotions',
            queryParameters: (params ?? {})
              ..addAll({"_sort": "published_at:desc"}))
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiPromotion>.from(
            data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiPromotion>> getArticles({String? categoryCode}) {
    final Map<String, dynamic> params = {"_sort": "published_at:desc"};
    if (categoryCode?.isNotEmpty == true) {
      params["_where[category.code]"] = categoryCode;
    }
    return dio
        .get('/articles', queryParameters: params)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiPromotion>.from(
            data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<Group>> getCategoryArticles() {
    return dio.get('/articles-categories').then((res) => res.data).then(
      (data) {
        return List<Group>.from(data?.map((item) => Group.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiPromotion>> getPromotionRelated(
      {int groupId = 0, int? id}) {
    final params = {
      "group": groupId,
      "id_ne": id,
      "_limit": 10,
      "_sort": "published_at:desc"
    };
    return dio
        .get('/promotions', queryParameters: params)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiPromotion>.from(
            data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiPromotion>> getArticleRelated(
      {int categoryId = 0, int? id}) {
    final params = {
      "category": categoryId,
      "id_ne": id,
      "_limit": 10,
      "_sort": "published_at:desc"
    };
    return dio
        .get('/articles', queryParameters: params)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiPromotion>.from(
            data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiPromotion>> getArticleCategory({int? categoryId}) {
    final params = {"category": categoryId, "_sort": "published_at:desc"};
    return dio
        .get('/articles', queryParameters: params)
        .then((res) => res.data)
        .then(
      (data) {
        return List<StrapiPromotion>.from(
            data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
      },
    );
  }

  Future<List<StrapiTerms>?> getTermConditions(
      {int? moduleId, String? code}) async {
    try {
      final params = code == null
          ? {"module.id": moduleId, "_sort": "published_at:desc"}
          : {"code": code};
      return dio
          .get('/terms-and-conditions', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<StrapiTerms>.from(data?.map((item) =>
                  StrapiTerms.fromJson(Map<String, dynamic>.from(item))) ??
              []);
        },
      );
    } catch (error) {
      logger.e(error);
      return null;
    }
  }

  Future<StrapiTerms> getTermsAndConditions({String? id}) {
    final _path = '/terms-and-conditions';
    return dio
        .get(
          _path,
          queryParameters: {"code": id},
        )
        .then((res) => res.data)
        .then(
          (data) {
            if (data is Iterable && data.isNotEmpty) {
              return StrapiTerms.fromJson(data.first);
            }
            return StrapiTerms();
          },
        );
  }

  Future<List<ChatBotMessage>?> getChatBotMessages({String? payload}) async {
    try {
      final params = (payload != null) && (payload.length > 0)
          ? {"payload": payload}
          : null;
      return dio
          .get('/chat-bots', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<ChatBotMessage>.from(
              data?.map((item) => ChatBotMessage.fromMap(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return null;
    }
  }

  Future<StrapiBanner?> getPopupAds({Map<String, dynamic>? param}) {
    return dio
        .get('/banners/getRandomPopup', queryParameters: param)
        .then((res) => res.data)
        .then((data) => data == null ? null : StrapiBanner.fromMap(data));
  }

  Future<List<StrapiArticlesCategories>?> getComponents(
      {String? moduleCode, String? pageCode, String? categoryCode}) {
    try {
      final params = {
        "is_active": true,
        "_sort": "sort_order:ASC",
      };

      if (moduleCode != null)
        params.addAll({StrApi.QUERY_MODULE_CODE: moduleCode});
      if (pageCode != null) params.addAll({StrApi.QUERY_PAGE_CODE: pageCode});
      if (categoryCode != null)
        params.addAll({StrApi.QUERY_CATEGORY_CODE: categoryCode});

      return dio
          .get('/articles-categories', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<StrapiArticlesCategories>.from(
              data?.map((item) => StrapiArticlesCategories.fromMap(item)) ??
                  []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<StrapiPromotion>?> getArticlesInComponent(
      {String? code, num? limit}) {
    try {
      final params = {
        "category.code": code,
        "_sort": "sort_order:ASC,published_at:DESC",
        "_limit": limit
      };

      return dio
          .get('/articles', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<StrapiPromotion>.from(
              data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<StrapiPromotion>?> getArticlesDiscover(
      {String? module, String? code, num? limit}) {
    try {
      final params = {
        "_where[module.code]": module,
        "_where[category.code]": code,
        "_sort": "sort_order:ASC,published_at:DESC",
        "_start": 0,
        "_limit": limit
      };

      return dio
          .get('/articles', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<StrapiPromotion>.from(
              data?.map((item) => StrapiPromotion.fromMap(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<StrapiPromotion?> getArticleDetail({String? id}) {
    try {
      return dio.get('/articles/${id ?? ''}').then((res) => res.data).then(
        (data) {
          return StrapiPromotion.fromMap(data);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value(null);
    }
  }

  Future<List<PromotionModel>?> getPromotion({num? start}) {
    try {
      final params = {
        "_where[0][group.code]": "PG_01",
        "_start": start,
        "_limit": 100
      };

      return dio
          .get('/promotions', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<PromotionModel>.from(
              data?.map((item) => PromotionModel.fromJson(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<UmeeGroup>?> getUmeProducts({String? sort}) {
    try {
      return dio
          .get(
            '/product-types',
            queryParameters: {"_sort": sort ?? 'sort_order'},
          )
          .then((res) => res.data)
          .then((data) {
            return List<UmeeGroup>.from(
                data?.map((item) => UmeeGroup.fromJson(item)) ?? []);
          });
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<PromotionModel>?> getNotificationSystem(
      {num? start = 0, String? groupCode, num? limit = 100}) {
    try {
      final params = {
        "_where[0][group.code]": groupCode ?? "PG_01",
        "_start": start,
        "_limit": limit
      };

      return dio
          .get('/notifications', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<PromotionModel>.from(
              data?.map((item) => PromotionModel.fromJson(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<TipModel>?> getListTipGroup() {
    try {
      final params = {"_sort": "sort_order"};

      return dio
          .get('/tip-groups', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<TipModel>.from(
              data?.map((item) => TipModel.fromJson(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<DetailTipModel>?> getDetailTip({required int id}) {
    try {
      final params = {"_where[tag]": id, "_sort": "sort_order"};

      return dio
          .get('/tips', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<DetailTipModel>.from(
              data?.map((item) => DetailTipModel.fromJson(item)) ?? []);
        },
      );
    } catch (error) {
      logger.e(error);
      return Future.value([]);
    }
  }

  Future<List<InvestPolicyModel>> getInvestPolicy() {
    try {
      final params = {"_sort": "is_active:desc,last_update_at:desc"};
      return dio
          .get('/product-policies', queryParameters: params)
          .then((res) => res.data)
          .then(
        (data) {
          return List<InvestPolicyModel>.from(
              data?.map((item) => InvestPolicyModel.fromMap(item)) ?? []);
        },
      );
    } catch (error) {
      return Future.value([]);
    }
  }
}
