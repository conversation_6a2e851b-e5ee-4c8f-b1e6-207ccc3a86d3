import 'dart:convert';

import 'package:common/model/strapi/strapi_models.dart';
import 'package:equatable/equatable.dart';

class StrapiFaqsModel {
  int? id;
  StrApiFaqGroup? group;
  String? question;
  String? answer;
  int? sortOrder;
  String? publishedAt;
  String? createdAt;
  String? updatedAt;

  StrapiFaqsModel({
    this.id,
    this.group,
    this.question,
    this.answer,
    this.sortOrder,
    this.publishedAt,
    this.createdAt,
    this.updatedAt,
  });

  StrapiFaqsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    group =
        json['group'] != null ? StrApiFaqGroup.fromMap(json['group']) : null;
    question = json['question'];
    answer = json['answer'];
    sortOrder = json['sort_order'];
    publishedAt = json['published_at'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    // data['group'] = this.group?.toMap();
    data['question'] = this.question;
    data['answer'] = this.answer;
    data['sort_order'] = this.sortOrder;
    data['published_at'] = this.publishedAt;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class StrApiFaqGroup extends Equatable {
  StrApiFaqGroup({
    this.id,
    this.name,
    this.group,
    this.question,
    this.answer,
    this.sortOrder,
    this.publishedAt,
    this.createdAt,
    this.updatedAt,
    this.module,
  });

  int? id;
  String? name;
  Group? group;
  String? question;
  String? answer;
  int? sortOrder;
  DateTime? publishedAt;
  DateTime? createdAt;
  DateTime? updatedAt;
  Module? module;

  factory StrApiFaqGroup.fromJson(String str) =>
      StrApiFaqGroup.fromMap(jsonDecode(str));

  factory StrApiFaqGroup.fromMap(Map<String, dynamic> json) => StrApiFaqGroup(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        group: json["group"] == null ? null : Group.fromMap(json["group"]),
        question: json["question"] == null ? null : json["question"],
        answer: json["answer"] == null ? null : json["answer"],
        sortOrder: json["sort_order"] == null ? null : json["sort_order"],
        publishedAt: json["published_at"] == null
            ? null
            : DateTime.parse(json["published_at"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        // module: json["module"] == null ? null : Module.fromJson(json["module"]),
      );

  @override
  List<Object?> get props => [id];
}
