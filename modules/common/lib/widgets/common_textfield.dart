import 'dart:math';

import 'package:common/utils/extension/string.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class CommonTextField extends StatefulWidget {
  final TextEditingController? textController;
  final FocusNode? focusNode;
  final Function(String)? onSubmitted;
  final Function(String)? checkNull;
  final Function(String)? onChanged;
  final Function(String)? onCheckError;
  final Function(String)? onBlur;
  final VoidCallback? onTap;
  final String? labelText;
  final String? value;
  final Color? labelColor;
  final TextStyle? labelStyle;
  final String? hintText;
  final TextStyle? hintStyle;
  final TextStyle? valueStyle;
  final Widget? suffix;
  final String? suffixText;
  final bool enabled;
  final bool obscureText;
  final bool suffixEnabled;
  final bool suffixAlwaysOn;
  final GlobalKey<FormState>? keyForm;
  final FormFieldValidator<String>? validator;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization? textCapitalization;
  final Color? focusColor;
  final Color? unFocusColor;
  final Color? cursorColor;
  final bool readOnly;
  final BoxConstraints? suffixIconConstraints;
  final BoxConstraints? prefixIconConstraints;
  final bool autofocus;
  final Color? backgroundColor;
  final Widget? prefixIcon;
  final int? maxLines;
  final int? minLines;
  final int? maxLinesError;
  final int? maxLength;
  final String counterText;
  final TextStyle? counterStyle;
  final EdgeInsets? padding;
  final bool alignLabelWithHint;
  final TextStyle? suffixStyle;
  final double? borderRadius;
  final EdgeInsets? contentPadding;
  final TextInputAction? textInputAction;
  final bool validateOnChange;

  const CommonTextField({
    Key? key,
    this.textController,
    this.focusNode,
    this.value,
    this.onSubmitted,
    this.onChanged,
    this.onCheckError,
    this.labelText,
    this.labelColor,
    this.labelStyle,
    this.hintText,
    this.hintStyle,
    this.valueStyle,
    this.suffix,
    this.suffixText,
    this.suffixEnabled = false,
    this.suffixAlwaysOn = false,
    this.enabled = true,
    this.obscureText = false,
    this.textInputType = TextInputType.text,
    this.keyForm,
    this.validator,
    this.checkNull,
    this.inputFormatters,
    this.textCapitalization = TextCapitalization.none,
    this.focusColor,
    this.unFocusColor,
    this.cursorColor,
    this.readOnly = false,
    this.autofocus = false,
    this.backgroundColor,
    this.suffixIconConstraints =
        const BoxConstraints.tightFor(width: 50, height: 40),
    this.prefixIconConstraints =
        const BoxConstraints.tightFor(width: 50, height: 40),
    this.prefixIcon,
    this.maxLines,
    this.maxLength,
    this.minLines,
    this.onTap,
    this.counterText = "",
    this.alignLabelWithHint = false,
    this.maxLinesError = 1,
    this.onBlur,
    this.padding,
    this.suffixStyle,
    this.borderRadius,
    this.contentPadding,
    this.textInputAction,
    this.counterStyle,
    this.validateOnChange = false,
  }) : super(key: key);

  @override
  _CommonTextFieldState createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  late TextEditingController? _textController;
  late FocusNode _focusNode;
  Color? _borderColor;
  String? _errorText;

  @override
  void initState() {
    _textController =
        widget.textController ?? TextEditingController(text: widget.value);
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_focusChange);
    super.initState();
  }

  _focusChange() {
    if (_focusNode.hasFocus == true) {
      widget.onTap?.call();
    } else {
      widget.onBlur?.call(_textController?.text ?? '');
    }
    if (!_focusNode.hasFocus) {
      _validate();
    } else if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(CommonTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.textController != null) {
      _textController = widget.textController;
    } else if (!widget.value.isNullOrEmpty && _textController != null) {
      _textController!.text = widget.value!;
    }
  }

  _validate() {
    if (mounted) {
      setState(() {
        final value = _textController?.text ?? '';
        if (value.trim().isNotEmpty) {
          _errorText = widget.onCheckError?.call(value);
        } else {
          _errorText = widget.checkNull?.call(value);
        }
        getBorderColor();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (widget.focusNode == null) {
      _focusNode.removeListener(_focusChange);
      _focusNode.dispose();
    }

    if (widget.textController == null) {
      _textController?.dispose();
    }
  }

  void getBorderColor() {
    if (_errorText != null && _errorText!.isNotEmpty) {
      _borderColor = Colors.red;
    } else if (_focusNode.hasFocus) {
      _borderColor = widget.focusColor ?? Theme.of(context).primaryColor;
    } else {
      _borderColor = widget.unFocusColor ?? Theme.of(context).dividerColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    getBorderColor();
    final showSuffixIcon =
        widget.suffixAlwaysOn || _focusNode.hasFocus && widget.suffixEnabled;
    return Container(
      padding: widget.padding ?? EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              border: Border.all(
                color: _borderColor ?? Theme.of(context).dividerColor,
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius ?? 4),
            ),
            child: Form(
              key: widget.keyForm,
              child: TextFormField(
                textInputAction: widget.textInputAction,
                maxLength: widget.maxLength,
                maxLines: widget.maxLines ?? 1,
                minLines: widget.minLines,
                controller: _textController,
                focusNode: _focusNode,
                autofocus: widget.autofocus,
                onChanged: (value) {
                  widget.onChanged?.call(value);
                  if (widget.validateOnChange) {
                    _validate();
                  }
                },
                textCapitalization:
                    widget.textCapitalization ?? TextCapitalization.none,
                inputFormatters: widget.inputFormatters,
                validator: widget.validator ??
                    (value) {
                      _validate();
                    },
                cursorColor: widget.cursorColor,
                keyboardType: widget.textInputType,
                enabled: widget.enabled,
                readOnly: widget.readOnly,
                obscureText: widget.obscureText,
                onFieldSubmitted: widget.onSubmitted,
                style: widget.valueStyle ??
                    StyleApp.textFieldStyle(
                      context,
                      color: StyleApp.bodyText2(context)?.color,
                    ).copyWith(height: 1.5),
                decoration: InputDecoration(
                  contentPadding: widget.contentPadding ??
                      EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                  labelText: widget.labelText,
                  labelStyle: _focusNode.hasFocus != true
                      ? (widget.labelStyle ??
                          widget.valueStyle ??
                          StyleApp.textFieldStyle(
                            context,
                            color: StyleApp.bodyText1(context)!.color,
                          ))
                      : StyleApp.bodyText1(context)?.copyWith(
                          color: widget.labelColor ??
                              Theme.of(context).primaryColor,
                        ),
                  hintText: widget.hintText,
                  hintStyle: widget.hintStyle ??
                      StyleApp.textFieldStyle(context,
                          color: StyleApp.bodyText1(context)!
                              .color
                              ?.withOpacity(0.2)),
                  border: InputBorder.none,
                  suffixIcon: showSuffixIcon ? widget.suffix : null,
                  suffixText: widget.suffixText,
                  suffixStyle: widget.suffixStyle,
                  suffixIconConstraints: widget.suffixIconConstraints,
                  prefixIconConstraints: widget.prefixIconConstraints,
                  prefixIcon: widget.prefixIcon,
                  counterText: widget.counterText,
                  counterStyle: widget.counterStyle,
                  alignLabelWithHint: widget.alignLabelWithHint,
                ),
              ),
            ),
          ),
          if (_errorText != null) SizedBox(height: 4),
          if (_errorText != null)
            Padding(
              padding: EdgeInsets.only(left: 0, bottom: 8.0),
              child: Text(
                _errorText!,
                maxLines: widget.maxLinesError,
                overflow: TextOverflow.ellipsis,
                style: StyleApp.bodyStyle(context, color: Colors.red),
              ),
            ),
        ],
      ),
    );
  }
}

///
/// An implementation of [NumberInputFormatter] automatically inserts thousands
/// separators to numeric input. For example, a input of `1234` should be
/// formatted to `1,234`.
///
class ThousandsFormatter extends NumberInputFormatter {
  static final NumberFormat _formatter =
      NumberFormat("###,###,###.##", localeFormat);

  final FilteringTextInputFormatter _decimalFormatter;
  final String _decimalSeparator;
  final RegExp _decimalRegex;

  final NumberFormat? formatter;
  final bool allowFraction;

  ThousandsFormatter({this.formatter, this.allowFraction = false})
      : _decimalSeparator = (formatter ?? _formatter).symbols.DECIMAL_SEP,
        _decimalRegex = RegExp(allowFraction
            ? '[0-9]+([${(formatter ?? _formatter).symbols.DECIMAL_SEP}])?'
            : r'\d+'),
        _decimalFormatter = FilteringTextInputFormatter.allow(RegExp(
            allowFraction
                ? '[0-9]+([${(formatter ?? _formatter).symbols.DECIMAL_SEP}])?'
                : r'\d+'));

  @override
  String _formatPattern(String? digits) {
    if (digits == null || digits.isEmpty) return '';
    num number;
    if (allowFraction) {
      String decimalDigits = digits;
      if (_decimalSeparator != '.') {
        decimalDigits = digits.replaceFirst(RegExp(_decimalSeparator), '.');
      }
      number = double.tryParse(decimalDigits) ?? 0.0;
    } else {
      number = int.tryParse(digits) ?? 0;
    }
    final result = (formatter ?? _formatter).format(number);
    if (allowFraction && digits.endsWith(_decimalSeparator)) {
      return '$result$_decimalSeparator';
    }
    return result;
  }

  @override
  TextEditingValue _formatValue(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return _decimalFormatter.formatEditUpdate(oldValue, newValue);
  }

  @override
  bool _isUserInput(String s) {
    return s == _decimalSeparator || _decimalRegex.firstMatch(s) != null;
  }
}

///
/// An implementation of [NumberInputFormatter] that converts a numeric input
/// to credit card number form (4-digit grouping). For example, a input of
/// `12345678` should be formatted to `1234 5678`.
///
class CreditCardFormatter extends NumberInputFormatter {
  static final RegExp _digitOnlyRegex = RegExp(r'\d+');
  static final FilteringTextInputFormatter _digitOnlyFormatter =
      FilteringTextInputFormatter.allow(_digitOnlyRegex);

  final String separator;

  CreditCardFormatter({this.separator = ' '});

  @override
  String _formatPattern(String digits) {
    StringBuffer buffer = StringBuffer();
    int offset = 0;
    int count = min(4, digits.length);
    final length = digits.length;
    for (; count <= length; count += min(4, max(1, length - count))) {
      buffer.write(digits.substring(offset, count));
      if (count < length) {
        buffer.write(separator);
      }
      offset = count;
    }
    return buffer.toString();
  }

  @override
  TextEditingValue _formatValue(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return _digitOnlyFormatter.formatEditUpdate(oldValue, newValue);
  }

  @override
  bool _isUserInput(String s) {
    return _digitOnlyRegex.firstMatch(s) != null;
  }
}

///
/// An abstract class extends from [TextInputFormatter] and does numeric filter.
/// It has an abstract method `_format()` that lets its children override it to
/// format input displayed on [TextField]
///
abstract class NumberInputFormatter extends TextInputFormatter {
  TextEditingValue? _lastNewValue;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    /// nothing changes, nothing to do
    if (newValue.text == _lastNewValue?.text) {
      return newValue;
    }
    _lastNewValue = newValue;

    /// remove all invalid characters
    newValue = _formatValue(oldValue, newValue);

    /// current selection
    int selectionIndex = newValue.selection.end;

    /// format original string, this step would add some separator
    /// characters to original string
    final newText = _formatPattern(newValue.text);

    /// count number of inserted character in new string
    int insertCount = 0;

    /// count number of original input character in new string
    int inputCount = 0;
    for (int i = 0; i < newText.length && inputCount < selectionIndex; i++) {
      final character = newText[i];
      if (_isUserInput(character)) {
        inputCount++;
      } else {
        insertCount++;
      }
    }

    /// adjust selection according to number of inserted characters staying before
    /// selection
    selectionIndex += insertCount;
    selectionIndex = min(selectionIndex, newText.length);

    /// if selection is right after an inserted character, it should be moved
    /// backward, this adjustment prevents an issue that user cannot delete
    /// characters when cursor stands right after inserted characters
    if (selectionIndex - 1 >= 0 &&
        selectionIndex - 1 < newText.length &&
        !_isUserInput(newText[selectionIndex - 1])) {
      selectionIndex--;
    }

    return newValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: selectionIndex),
        composing: TextRange.empty);
  }

  /// check character from user input or being inserted by pattern formatter
  bool _isUserInput(String s);

  /// format user input with pattern formatter
  String _formatPattern(String digits);

  /// validate user input
  TextEditingValue _formatValue(
      TextEditingValue oldValue, TextEditingValue newValue);
}

class DateFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue prevText, TextEditingValue currText) {
    int selectionIndex;

    // Get the previous and current input strings
    String pText = prevText.text;
    String cText = currText.text.replaceAll(RegExp("[^0-9/]"), '');

    // Abbreviate lengths
    int cLen = cText.length;
    int pLen = pText.length;

    if (cLen == 1) {
      // Can only be 0, 1, 2 or 3
      if (int.parse(cText) > 3) {
        // Remove char
        cText = '';
      }
    } else if (cLen == 2 && pLen == 1) {
      // Days cannot be greater than 31
      int dd = int.parse(cText.substring(0, 2));
      if (dd == 0 || dd > 31) {
        // Remove char
        cText = cText.substring(0, 1);
      } else {
        // Add a / char
        cText += '/';
      }
    } else if (cLen == 4) {
      // Can only be 0 or 1
      if (int.parse(cText.substring(3, 4)) > 1) {
        // Remove char
        cText = cText.substring(0, 3);
      }
    } else if (cLen == 5 && pLen == 4) {
      // Month cannot be greater than 12
      int mm = int.parse(cText.substring(3, 5));
      if (mm == 0 || mm > 12) {
        // Remove char
        cText = cText.substring(0, 4);
      } else {
        // Add a / char
        cText += '/';
      }
    } else if ((cLen == 3 && pLen == 4) || (cLen == 6 && pLen == 7)) {
      // Remove / char
      cText = cText.substring(0, cText.length - 1);
    } else if (cLen == 3 && pLen == 2) {
      if (int.parse(cText.substring(2, 3)) > 1) {
        // Replace char
        cText = cText.substring(0, 2) + '/';
      } else {
        // Insert / char
        cText =
            cText.substring(0, pLen) + '/' + cText.substring(pLen, pLen + 1);
      }
    } else if (cLen == 6 && pLen == 5) {
      // Can only be 1 or 2 - if so insert a / char
      int y1 = int.parse(cText.substring(5, 6));
      if (y1 < 1 || y1 > 2) {
        // Replace char
        cText = cText.substring(0, 5) + '/';
      } else {
        // Insert / char
        cText = cText.substring(0, 5) + '/' + cText.substring(5, 6);
      }
    } else if (cLen == 7) {
      // Can only be 1 or 2
      int y1 = int.parse(cText.substring(6, 7));
      if (y1 < 1 || y1 > 2) {
        // Remove char
        cText = cText.substring(0, 6);
      }
    } else if (cLen == 8) {
      // Can only be 19 or 20
      int y2 = int.parse(cText.substring(6, 8));
      if (y2 < 19 || y2 > 20) {
        // Remove char
        cText = cText.substring(0, 7);
      }
    }

    selectionIndex = cText.length;
    return TextEditingValue(
      text: cText,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}
