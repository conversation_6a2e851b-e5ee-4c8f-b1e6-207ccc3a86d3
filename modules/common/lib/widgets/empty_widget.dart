import 'package:common/utils/extension.dart';
import 'package:common/utils/extension/string.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';

class EmptyWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final String? titleButton;
  final bool hasAction;
  final VoidCallback? onPressedAction;
  final Widget? icon;
  final Widget? icDarkTheme;
  final int? maxLine;
  final TextStyle? titleStyle;
  final TextStyle? messageStyle;
  final Color? titleColor;
  final Color? messageColor;

  EmptyWidget({
    this.title,
    this.message,
    this.titleButton,
    this.hasAction = false,
    this.maxLine = 2,
    required this.icon,
    this.icDarkTheme,
    this.onPressedAction,
    this.titleStyle,
    this.messageStyle,
    this.titleColor,
    this.messageColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 30, right: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 30),
            child: context.isDarkTheme ? (icDarkTheme ?? icon) : icon,
          ),
          if (!title.isNullOrEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 5),
              child: Text(
                title!,
                style: titleStyle ??
                    StyleApp.titleStyle(
                      context,
                      color: titleColor,
                    ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          if (!message.isNullOrEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: Text(
                message!,
                style: messageStyle ??
                    StyleApp.bodyStyle(
                      context,
                      color: messageColor,
                    ),
                textAlign: TextAlign.center,
                maxLines: maxLine,
                overflow: maxLine != null ? TextOverflow.ellipsis : null,
              ),
            ),
          if (hasAction)
            Container(
              height: 45,
              width: 211,
              child: ElevatedButton(
                child: Text(
                  titleButton ?? "",
                  textAlign: TextAlign.center,
                ),
                onPressed: onPressedAction,
              ),
            ),
        ],
      ),
    );
  }
}
