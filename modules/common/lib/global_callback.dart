import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/model/strapi/banner_model.dart';
import 'package:common/model/strapi/contact_model.dart';
import 'package:common/model/transfer/transfer_param.dart';
import 'package:flutter/material.dart';
import 'repository/strapi/strapi.dart';

typedef OnChatOpen = Future Function(ChatModel data);

typedef OnProfileOpen = void Function();

typedef OnAddBankOpen = void Function({required bool isShowIntro});

typedef OnMyBanksOpen = void Function();

typedef OnPaymentOpen = Future<bool> Function({
  dynamic orderId,
  num tranId,
  String modCd,
});

typedef OnUploadFile = Future<String?> Function({
  required String fileType,
  required File file,
  Function(String err)? onErr,
});

typedef OnOpenScanQR = Future<String> Function({
  VoidCallback? onPressHint,
  bool? myCode,
});

typedef OnLocalAuthentication = Future<bool> Function();

typedef OnOpenAddress = Future Function({
  ContactModel? currentAddress,
  String? title,
  bool? selectedPage,
});

typedef OnConfirmProInvestor = Future Function({
  String? moduleName,
  bool? isShowConfirmDialog,
});

typedef GetGlobalEnv = Environment Function();

typedef OnVerifyID = Future Function();

typedef OnGetProfile = Future<BaseProfile?> Function();

typedef OnSalerDetail = Future Function(String?);

typedef OnPaymentHome = Future<Object?> Function({
  required TransferParamModel transferParamModel,
// required String orderId,
// required String stokeType,
});

typedef GetThemeApp = BannerModel Function();

typedef OnOpenOtp = Future<String?> Function({OtpHandler? blocHandler});

typedef GetCdnUrl = String? Function(String?);

typedef OnGoSupportVietQrBanks = void Function();

typedef GetInvestPolicesWidget = Widget Function({String? module});

typedef OnOpenInvestmentContractDetail = Future<dynamic>? Function(
    {required String? ordId, required InvestmentType? investmentType});

enum InvestmentType {
  HOME_PLUS,
  INVEST,
  HOME_PREMIER,
  KS_HOME_COOP,
  KS_HOME_VILLAS,
  KS_INVEST_PLUS
}

///*viewMode:*
///   - 0, null: show overview only
///   - 1: show full info include tabs
///
///*module:*
///
/// using `module static const MODULE_xxx` in [StrApi]
typedef OnOpenOrderDetail = Future Function({
  String? module,
  String? orderId,
  int? viewMode,
});

class GlobalCallback {
  static GlobalCallback? _instance;

  OnChatOpen? onChatOpen;
  OnProfileOpen? onProfileOpen;
  OnAddBankOpen? onAddBankOpen;
  OnMyBanksOpen? onMyBanksOpen;
  OnPaymentOpen? onPaymentOpen;
  OnLocalAuthentication? onLocalAuthentication;
  OnOpenScanQR? onOpenScanQR;
  OnOpenAddress? onOpenAddress;
  OnConfirmProInvestor? onConfirmProInvestor;
  GetGlobalEnv? getGlobalEnv;
  OnSalerDetail? onSalerDetail;
  OnVerifyID? onVerifyID;
  OnGetProfile? onGetProfile;
  OnUploadFile? uploadFile;
  OnPaymentHome? onPaymentHome;
  GetThemeApp? getThemeApp;
  OnOpenOtp? openOtp;
  GetCdnUrl? getCdnUrl;
  VoidCallback? showFullScreenLoading;
  VoidCallback? hideFullScreenLoading;
  GlobalKey<NavigatorState>? navigatorKey;
  OnGoSupportVietQrBanks? onGoSupportVietQrBanks;
  GetInvestPolicesWidget? onInvestPolicy;
  OnOpenOrderDetail? onOpenOrderDetail;
  OnOpenInvestmentContractDetail? onOpenInvestmentContractDetail;
  VoidCallback? refreshToken;
  CallBackForBiology? callBackForBiology;

  GlobalCallback._internal();

  static GlobalCallback get instance {
    if (_instance == null) {
      _instance = GlobalCallback._internal();
    }
    return _instance!;
  }
}

class CallBackForBiology {
  CallBackForBiology({
    this.updateCCCD,
    this.handleError,
    this.handleNotAuth,
    this.authFaceID,
    this.authFaceIDV2,
    this.handleGoToLogin,
  });

  Function(
    BuildContext context, {
    required String phoneNumber,
    String? firstRoute,
    String? route,
  })? updateCCCD;

  Function(
    BuildContext context, {
    BaseResponseModel? status,
    Function()? updateCCCD,
    Function()? handleGoToLogin,
  })? handleError;

  Function(
    BuildContext context, {
    required VoidCallback onCancel,
    Function? onSubmit,
    String? message,
  })? handleNotAuth;

  Future<dynamic> Function(
    BuildContext context, {
    required String? transactionNo,
    required String phoneNumber,
    required String firstRoute,
    String? routeConfirm,
  })? authFaceID;

  Function(
    BuildContext context, {
    required String? transactionNo,
    required String? sessionId,
    required String? sessionToken,
    required String? region,
    required String? accessKeyId,
    required String? secretAccessKey,
    Future<bool?> Function()? onComplete,
    ValueChanged<String>? onError,
  })? authFaceIDV2;

  Function(BuildContext context)? handleGoToLogin;
}

class ChatModel {
  String? department;
  String? question;
  bool? openLiveChat;
  ChatPrivateModel? openPrivateChat;
  String? roomId;
  String? roomName;
  String? email;
  String? phone;
  String? stage;

  ChatModel({
    this.department,
    this.question,
    this.openLiveChat,
    this.openPrivateChat,
    this.roomId,
    this.roomName,
    this.email,
    this.phone,
    this.stage,
  });
}

class ChatPrivateModel {
  const ChatPrivateModel({
    this.loginName,
    this.phone,
    this.content,
    this.onSubmit,
    this.submitText,
  });

  final String? loginName;
  final String? phone;
  final Widget? content;
  final void Function()? onSubmit;
  final String? submitText;
}

class BankAccount {
  String accountNo;
  int? accountType;
  int? accountStatus;
  String accountName;

  BankAccount({
    required this.accountNo,
    this.accountType,
    this.accountStatus,
    required this.accountName,
  });

  @override
  String toString() {
    return 'BankAccount{accountNo: $accountNo, accountType: $accountType, accountStatus: $accountStatus, accountName: $accountName}';
  }
}

class BaseProfile {
  BaseProfile({
    this.userId,
    this.userName,
    this.phoneNumber,
    this.email,
    this.idCardType,
    this.avatarUrl,
    this.faceIdUrl,
    this.idCardFrontSideUrl,
    this.idCardBackSideUrl,
    this.verifiedEmail,
    this.verified,
    this.enabled,
    this.branchCode,
    this.fullAddress,
    this.firstSignUrl,
    this.secondSignUrl,
    this.nationCode,
    this.provinceCode,
    this.districtCode,
    this.communeCode,
    this.street,
    this.idCardNo,
    this.idCardIssuedDate,
    this.idCardExpiredDate,
    this.idCardIssuedBy,
    this.fullName,
    this.gender,
    this.dateOfBirth,
    this.permanentAddress,
    this.bankAccounts,
  });

  String? userId;
  String? userName;
  String? phoneNumber;
  String? email;
  String? idCardType;
  String? avatarUrl;
  String? faceIdUrl;
  String? idCardFrontSideUrl;
  String? idCardBackSideUrl;
  bool? verifiedEmail;
  bool? verified;
  bool? enabled;
  String? branchCode;
  String? fullAddress;
  String? firstSignUrl;
  String? secondSignUrl;
  String? nationCode;
  String? provinceCode;
  String? districtCode;
  String? communeCode;
  String? street;
  String? idCardNo;
  String? idCardIssuedDate;
  String? idCardExpiredDate;
  String? idCardIssuedBy;
  String? fullName;
  String? gender;
  String? dateOfBirth;
  String? permanentAddress;
  List<BankAccount>? bankAccounts;

  BaseProfile copyWith({
    String? userId,
    String? userName,
    String? phoneNumber,
    String? email,
    String? idCardType,
    String? avatarUrl,
    String? faceIdUrl,
    String? idCardFrontSideUrl,
    String? idCardBackSideUrl,
    bool? verifiedEmail,
    bool? verified,
    bool? enabled,
    String? branchCode,
    String? fullAddress,
    String? firstSignUrl,
    String? secondSignUrl,
    String? nationCode,
    String? provinceCode,
    String? districtCode,
    String? communeCode,
    String? street,
    String? idCardNo,
    String? idCardIssuedDate,
    String? idCardExpiredDate,
    String? idCardIssuedBy,
    String? fullName,
    String? gender,
    String? dateOfBirth,
    String? permanentAddress,
    List<BankAccount>? bankAccounts,
  }) {
    return BaseProfile(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      idCardType: idCardType ?? this.idCardType,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      faceIdUrl: faceIdUrl ?? this.faceIdUrl,
      idCardFrontSideUrl: idCardFrontSideUrl ?? this.idCardFrontSideUrl,
      idCardBackSideUrl: idCardBackSideUrl ?? this.idCardBackSideUrl,
      verifiedEmail: verifiedEmail ?? this.verifiedEmail,
      verified: verified ?? this.verified,
      enabled: enabled ?? this.enabled,
      branchCode: branchCode ?? this.branchCode,
      fullAddress: fullAddress ?? this.fullAddress,
      firstSignUrl: firstSignUrl ?? this.firstSignUrl,
      secondSignUrl: secondSignUrl ?? this.secondSignUrl,
      nationCode: nationCode ?? this.nationCode,
      provinceCode: provinceCode ?? this.provinceCode,
      districtCode: districtCode ?? this.districtCode,
      communeCode: communeCode ?? this.communeCode,
      street: street ?? this.street,
      idCardNo: idCardNo ?? this.idCardNo,
      idCardIssuedDate: idCardIssuedDate ?? this.idCardIssuedDate,
      idCardExpiredDate: idCardExpiredDate ?? this.idCardExpiredDate,
      idCardIssuedBy: idCardIssuedBy ?? this.idCardIssuedBy,
      fullName: fullName ?? this.fullName,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      permanentAddress: permanentAddress ?? this.permanentAddress,
      bankAccounts: bankAccounts ?? this.bankAccounts,
    );
  }
}

mixin OtpHandler {
  Future<bool?> takeTokenOtp();

  void dispose();

  Stream<LoadingWidgetModel>? get loadingScreen;
}
