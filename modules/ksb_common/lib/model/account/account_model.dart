import 'package:common/ks_common.dart';
import 'package:ksb_common/model/enum_type.dart';
import 'package:ksb_common/utils/format_util.dart';
import 'package:ksbank_api_nickname/ksbank_api_nickname.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

class AccountModel {
  String? customerName;
  String? accountName;
  String? usernamel;
  String? cifNumber;
  String? accountNumber;
  DateTime? issuedDate; //Ngày mở tài khoản
  String? accountType;
  String? branch;
  double? availableBalance; //Số dư khả dụng
  double? accountBalance; //Số dư thực tế
  double? overdraftAmount; //Số tiền được phép thấu chi
  double? remainOverdraftAmount; //Số tiền thấu chi còn lại
  int? accountStatus;
  String? openPlace;
  String? aliasname;
  String? nickname;
  String? vietQrcode;
  String? statusNickname;
  String? postCheck;
  int? currency;
  String? currencyName;
  int? accountTypeInt;
  String? miAcctTypCd;
  String? nickName;

  void setStatusNickname(NicknameStatus status) {
    statusNickname = status.name;
  }

  ///
  String getCurrency() {
    return GlobalModel.getCurrency(currency);
  }


  AccountModel({
    this.customerName,
    this.accountName,
    this.usernamel,
    this.cifNumber,
    this.accountNumber,
    this.issuedDate,
    this.accountType,
    this.branch,
    this.availableBalance,
    this.accountBalance,
    this.overdraftAmount,
    this.remainOverdraftAmount,
    this.accountStatus,
    this.openPlace,
    this.aliasname,
    this.nickname,
    this.vietQrcode,
    this.statusNickname,
    this.postCheck,
    this.currency,
    this.currencyName,
    this.accountTypeInt,
    this.miAcctTypCd,
    this.nickName,
  });

  String get issuedDateText => (issuedDate == null ? "" : issuedDate.formatDMY);

  String get accountBalanceText =>
      GlobalModel.formatMoney(accountBalance, currency: currency);

  String get availableBalanceText =>
      GlobalModel.formatMoney(availableBalance, currency: currency);

  String get overdraftAmountText =>
      GlobalModel.formatMoney(overdraftAmount, currency: currency);

  String get remainOverdraftAmountText =>
      GlobalModel.formatMoney(remainOverdraftAmount, currency: currency);

  // String get displayName => '$accountName | $accountNumber';
  String get displayName {
    if (aliasname?.isNotEmpty == true) return aliasname!;
    if (accountName?.isNotEmpty == true) return accountName!;
    return accountNumber ?? '';
  }

  String get displayNickname {
    return nickname ?? '';
  }

  bool get hasOverdraftAmount =>
      (overdraftAmount != null && overdraftAmount! > 0);

  AccountModel.fromList({required AccountItemMapper map, String? customer}) {
    accountName = map.accountName ?? "";
    cifNumber = map.cifNo ?? "";
    accountNumber = map.accountNo ?? "";
    accountBalance = toDouble(map.accountBalance);
    accountType = "${map.accountType ?? 0}";
    aliasname = map.alias ?? "";
    availableBalance = toDouble(map.availableBalance);
    overdraftAmount = toDouble(map.overdraftAmount);
    remainOverdraftAmount = toDouble(map.remainOverdraftAmount);
    issuedDate = getDateLocal(map.contractDate);
    currency = toInt(map.currency);
    currencyName = map.currencyName;
    customerName = customer;
    nickName = map.nickName ?? "";
    miAcctTypCd = map.miAcctTypCd ?? "";
  }

  AccountModel.fromDetail(AccountDetailResponseMapper map) {
    accountName = map.accountName ?? "";
    cifNumber = map.cifNo ?? "";
    accountNumber = map.accountNo ?? "";
    accountBalance = toDouble(map.accountBalance);
    aliasname = map.aliasName ?? "";
    accountType = "${map.accountType ?? 0}";
    availableBalance = toDouble(map.availableBalance);
    overdraftAmount = toDouble(map.overdraftAmount);
    remainOverdraftAmount = toDouble(map.remainOverdraftAmount);
    openPlace = map.openPlace ?? "";
    issuedDate = getDateLocal(map.contractDate);
    currency = toInt(map.currency);
    customerName = map.customerName ?? "";
  }

  AccountModel.fromGetNickNamesOfAccount(GetNickNamesOfAccount res) {
    nickname = res.nickName;
    accountNumber = res.accountNo;
    postCheck = res.postChecks;
    statusNickname = res.status;
  }

  AccountModel.fromPaymentAccountResponseV2(
    PaymentAccountResponseV2 res, {
    String? customerName,
  }) {
    customerName = customerName;
    accountNumber = res.accountNo;
    accountTypeInt = res.accountType;
    accountStatus = res.accountStatus;
    accountName = res.accountName;
    aliasname = res.alias;
    currency = res.currency;
    miAcctTypCd = res.miAcctTypCd;
    nickname = res.nickName;
    postCheck = res.postChecks;
    statusNickname = res.statusNickName;
    vietQrcode = res.vietQrCode;
    availableBalance = toDouble(res.availableBalance);
    accountBalance = toDouble(res.accountBalance);
    overdraftAmount = toDouble(res.overdraftAmount);
    remainOverdraftAmount = toDouble(res.remainOverdraftAmount);
  }

  @override
  String toString() {
    return 'AccountModel{currencyName: $currencyName, accountName: $accountName, cifNumber: $cifNumber, accountNumber: $accountNumber, issuedDate: $issuedDate, accountType: $accountType, branch: $branch, availableBalance: $availableBalance, accountBalance: $accountBalance, overdraftAmount: $overdraftAmount, remainOverdraftAmount: $remainOverdraftAmount, accountStatus: $accountStatus, openPlace: $openPlace}';
  }

  AccountModel copyWith({
    String? customerName,
    String? accountName,
    String? usernamel,
    String? cifNumber,
    String? accountNumber,
    DateTime? issuedDate,
    String? accountType,
    String? branch,
    double? availableBalance,
    double? accountBalance,
    double? overdraftAmount,
    double? remainOverdraftAmount,
    int? accountStatus,
    String? openPlace,
    String? aliasname,
    String? nickname,
    String? vietQrcode,
    String? statusNickname,
    String? postCheck,
    int? currency,
    String? currencyName,
    int? accountTypeInt,
    String? miAcctTypCd,
    String? nickName,
  }) {
    return AccountModel(
      customerName: customerName ?? this.customerName,
      accountName: accountName ?? this.accountName,
      usernamel: usernamel ?? this.usernamel,
      cifNumber: cifNumber ?? this.cifNumber,
      accountNumber: accountNumber ?? this.accountNumber,
      issuedDate: issuedDate ?? this.issuedDate,
      accountType: accountType ?? this.accountType,
      branch: branch ?? this.branch,
      availableBalance: availableBalance ?? this.availableBalance,
      accountBalance: accountBalance ?? this.accountBalance,
      overdraftAmount: overdraftAmount ?? this.overdraftAmount,
      remainOverdraftAmount:
          remainOverdraftAmount ?? this.remainOverdraftAmount,
      accountStatus: accountStatus ?? this.accountStatus,
      openPlace: openPlace ?? this.openPlace,
      aliasname: aliasname ?? this.aliasname,
      nickname: nickname ?? this.nickname,
      vietQrcode: vietQrcode ?? this.vietQrcode,
      statusNickname: statusNickname ?? this.statusNickname,
      postCheck: postCheck ?? this.postCheck,
      currency: currency ?? this.currency,
      currencyName: currencyName ?? this.currencyName,
      accountTypeInt: accountTypeInt ?? this.accountTypeInt,
      miAcctTypCd: miAcctTypCd ?? this.miAcctTypCd,
      nickName: nickName ?? this.nickName,
    );
  }
}

class AccountSummaryModel {
  int? id;
  String? name;
  double? amount;
  double? amountUSD;
  int? count;
  double? totalOutstandingBalance;
  bool? isDisableOpenAccount;

  AccountSummaryModel();

  String get amountText => GlobalModel.formatMoney(amount, hasCurrency: false);

  String get amountUSDText => (amountUSD != null && amountUSD! > 0
      ? GlobalModel.formatMoney(amountUSD, currency: 2, hasCurrency: false)
      : "");

  String get nameText => "$name ($count)";

  String get totalOutstandingBalanceText =>
      GlobalModel.formatMoney(totalOutstandingBalance, hasCurrency: false);

  AccountSummaryModel.fromDetail(AccountSummary map, {bool? isDisable}) {
    id = map.id ?? 0;
    name = map.name ?? "";
    amount = toDouble(map.balanceVND);
    amountUSD = toDouble(map.balanceUSD);
    count = toInt(map.count);
    isDisableOpenAccount = isDisable;
  }

  AccountSummaryModel.fromCard(SummaryCreditCard map) {
    //id = map.id ?? 0;
    name = map.title ?? "";
    amount = toDouble(map.totalCreditLimit);
    count = toInt(map.totalCount);
    totalOutstandingBalance = toDouble(map.totalOutstandingBalance);
  }

  empty() {
    name = "";
    amount = 0;
    count = 0;
    totalOutstandingBalance = 0;
  }
}

class GroupStatementModel {
  String? name;
  late List<StatementModel> child;

  GroupStatementModel.fromList(StatementGroup map) {
    name = map.name ?? "";
    child = [];
    for (var item in map.items!) {
      child.add(StatementModel.fromMap(item));
    }
  }
}

class StatementModel {
  String? title;
  String? description;
  String? fromDate;
  String? toDate;

  StatementModel.fromMap(StatementItem map) {
    title = map.title ?? "";
    description = map.description ?? "";
    fromDate = map.fromDate ?? "";
    toDate = map.toDate ?? "";
  }
}

class ProfileMenuModel {
  final String? id;
  final String? name;
  final String? icon;

  ProfileMenuModel({this.id, this.name, this.icon});
}

class ProfileMenuType {
  static String bank = "bank";
  static String security = "security";
  static String securities = "securities";
  static String qr = "qr";
  static String staff = "staff";
  static String collaborators = "collaborators";
  static String map = "map";
  static String question = "question";
  static String earphone = "earphone";
  static String message = "message";
  static String layout = "layout";
}
