import 'package:common/utils/model_util.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

class OpenAccountModel {
  String? recordID;
  String? vipAccountNo;
  String? vipGroup;
  double? feeAmount;
  String? feeAmountStr;
  String? checkCode;
  String? checkName;
  String? accountNo;
  String? aliasName;
  String? accountName;
  String? createdDate;
  String? bankName;
  int? responseList;
  String? description;
  num? feeVAT;
  String? customerName;
  num? feeAmountIncludeVat;

  OpenAccountModel(
      {this.recordID,
        this.vipAccountNo,
        this.vipGroup,
        this.feeAmount,
        this.checkCode,
        this.checkName,
        this.accountNo,
        this.aliasName,
        this.accountName,
        this.createdDate,
        this.bankName,
        this.description,
        this.responseList,
        this.feeVAT,
        this.customerName,
        this.feeAmountIncludeVat});

  OpenAccountModel.fromJson(Map<String, dynamic> json) {
    // recordID = json['recordID'];
    vipAccountNo = json['vipAccountNo'];
    vipGroup = json['vipGroup'];
    feeAmount = json['feeAmount'];
    checkCode = json['checkCode'];
    checkName = json['checkName'];
    accountNo = json['accountNo'];
    aliasName = json['aliasName'];
    feeVAT = json['feeVat'];
    feeAmountIncludeVat = json['feeAmountIncludeVat'];
  }

  OpenAccountModel.fromResponse(VipAccountV1Response map) {
    // recordID = map.recordID;
    vipAccountNo = map.vipAccountNo;
    vipGroup = map.vipGroup;
    feeAmountStr = map.feeAmountStr;
    feeAmount = toDouble(map.feeAmount);
    checkCode = map.checkCode;
    checkName = map.checkName;
    feeVAT  = map.feeVat;
    feeAmountIncludeVat = map.feeAmountIncludeVat;
  }

  OpenAccountModel.fromResponseDTO(VipAccountInfoDto map) {
    recordID = map.recordID;
    vipAccountNo = map.vipAccountNo;
    vipGroup = map.vipGroup;
    feeAmountStr = map.feeAmountStr;
    feeAmount = toDouble(map.feeAmount);
    checkCode = map.checkCode;
    checkName = map.checkName;
  }

  OpenAccountModel.fromResponseOption(AccountNumberInfo map) {
    vipAccountNo = map.accountNumber;
    feeAmountStr = map.feeAmountStr;
    feeAmount = toDouble(map.feeAmount);
    feeVAT = map.feeVat;
    feeAmountIncludeVat = map.feeAmountIncludeVat;
  }

  OpenAccountModel.fromResponseCreate(OpenVipAccountResponse? map) {
    accountNo = map?.accountNo;
    accountName = map?.accountName;
    aliasName = map?.aliasName;
    feeAmount = toDouble(map?.feeAmount);
    bankName = map?.bankName;
    createdDate = map?.createdDate;
  }

  OpenAccountModel.fromResponseNoVipAccountCreate(OpenAccountResponse map) {
    accountNo = map.accountNo;
    accountName = map.accountName;
    description = map.aliasName;
    createdDate = map.createdDate;
    bankName = map.bankName;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recordID'] = this.recordID;
    data['vipAccountNo'] = this.vipAccountNo;
    data['vipGroup'] = this.vipGroup;
    data['feeAmount'] = this.feeAmount;
    data['checkCode'] = this.checkCode;
    data['checkName'] = this.checkName;
    data['accountNo'] = this.accountNo;
    data['aliasName'] = this.aliasName;
    return data;
  }

  empty() {
    recordID = "";
    vipAccountNo = "";
    vipGroup = "";
    feeAmount = 0;
    feeAmountStr = '';
    checkCode = "";
    checkName = "";
    accountNo = "";
    aliasName = "";
    responseList = 0;
  }

  @override
  String toString() {
    return 'OpenAccountModel{recordID: $recordID, vipAccountNo: $vipAccountNo, vipGroup: $vipGroup, feeAmount: $feeAmount, feeAmountStr: $feeAmountStr, checkCode: $checkCode, checkName: $checkName, accountNo: $accountNo, aliasName: $aliasName, accountName: $accountName, createdDate: $createdDate, bankName: $bankName, responseList: $responseList, description: $description, feeVAT: $feeVAT, customerName: $customerName, feeAmountIncludeVat: $feeAmountIncludeVat}';
  }
}
