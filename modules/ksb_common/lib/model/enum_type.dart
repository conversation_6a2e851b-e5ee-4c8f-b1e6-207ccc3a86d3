enum ContractType { INVEST, CREDIT, BOND, SMART_INVEST, GOLD }

enum HomeCardType { USER, INVEST, CREDIT, BOND, SECURITIES, HOUSE }

enum AccountType { THANH_TOAN, VAY, TIET_KIEM, TIN_DUNG }

enum PaymentUserType { FAVORITE, INTERNAL, EXTERNAL, ATM, PHONE, CHOOSE }

enum PDFType { ACCOUNT, CARD }

enum NewsType { PROMOTION, NEWS }

enum UserStatus { AUTHENTICATED, UNAUTHORIZED, DISABLED }
enum PaymentStatus { PROCESSING, SUCCESS, EXPIRED }
enum PaymentMethod { BANK, TRANSFER, CASH }
enum BannerType { project, projectType, province, apartment, single }
enum MediaType { img, video, photo360 }
enum FilterType {
  projectType,
  propertyType,
  project,
  roomBedNum,
  building,
  position,
  view,
  direction,
  status,
  fromFloor,
  toFloor,
}

enum DetailTabType { TONG_QUAN,QUYEN_LOI, RUT_VON, TAT_TOAN,CHUYEN_NHUONG,THANH_TOAN,UU_DAI }

enum TypeLink { Account, Card }

enum AuthMethod { OTP, E_TOKEN }

enum FrequencyType { DAILY, WEEKLY, MONTHLY }

enum TransferMethod { FAST, NORMAL }

enum PaymentMethods { UNIBANK, INTERNAL_CARD, EXTERNAL_CARD, QRCODE, BANK }

enum RaiseLimitType {
  SALARY,
  OTHER_CARD,
  ELECTRIC_BILL,
  RETIREMENT_BOOK,
}

enum ReceivedSalaryMethod { CASH, BY_BANK }

enum DocumentType {
  RESIDENCE,
  VERIFY_SALARY,
  HEALTH_INSURANCE,
  CREDIT,
  SOCIAL_INSURANCE,
  PENSION_RECEIPT,
  ELECTRIC_BILL
}

enum OtpType { LOGIN, REGISTER }

enum InvestType { finance, real_estate, stock, gold, currency, none }

enum StatusPackageInvest { release, comingSoon }

enum InvestModuleType { RUT_VON, TAT_TOAN }

enum NicknameStatus { ACTIVE, DISABLE, INACTIVE }

