import 'package:common/ks_common.dart';
import 'package:ksbank_api_profile/ksbank_api_profile.dart';

class ProfileInfo {
  int? loginType;
  String? fullName;
  String? avatarUrl;
  String? phoneNumber;
  String? otpMethod;
  String? email;
  bool? verifyEmail;
  String? aliasName;
  String? username;
  bool? facebookLinked;
  String? facebookId;
  String? facebookName;
  bool? googleLinked;
  String? googleId;
  String? googleName;
  String? sex;
  DateTime? birthday;
  String? idCardType;
  String? idCardNo;
  DateTime? idCardIssueDate;
  DateTime? idCardExpireDate;
  String? idCardIssuePlace;
  String? resAddr;
  String? resCity;
  String? nationCode;
  String? taxCode;
  bool? enabled2fa;
  String? provinceCode;
  String? districtCode;
  String? wardVlg;
  String? fullAddress;
  String? createdFrom;
  String? organization;
  bool? lockedOldApp;
  bool? bankVerified;
  String? suspiciousEkycStatus;
  String? customerGroupUserId;
  bool? passEkyc;
  String? identityStatus;
  bool? isVerifiedSTH;

  ProfileInfo({
    this.loginType,
    this.fullName,
    this.avatarUrl,
    this.phoneNumber,
    this.otpMethod,
    this.email,
    this.verifyEmail,
    this.aliasName,
    this.username,
    this.facebookLinked,
    this.facebookId,
    this.facebookName,
    this.googleLinked,
    this.googleId,
    this.googleName,
    this.sex,
    this.birthday,
    this.idCardType,
    this.idCardNo,
    this.idCardIssueDate,
    this.idCardExpireDate,
    this.idCardIssuePlace,
    this.resAddr,
    this.resCity,
    this.nationCode,
    this.taxCode,
    this.enabled2fa,
    this.provinceCode,
    this.districtCode,
    this.wardVlg,
    this.createdFrom,
    this.lockedOldApp,
    this.bankVerified,
    this.suspiciousEkycStatus,
    this.passEkyc,
    this.organization,
    this.fullAddress,
    this.identityStatus,
    this.isVerifiedSTH,
  });

  ProfileInfo.fromResponse(QueryInfoResponse response) {
    loginType = response.loginType;
    fullName = response.fullName;
    avatarUrl = response.avatarUrl;
    phoneNumber = response.phoneNumber;
    otpMethod = response.otpMethod;
    email = response.email;
    verifyEmail = response.verifiedEmail;
    aliasName = response.aliasName;
    username = response.username;
    facebookLinked = response.facebookLinked;
    facebookId = response.facebookId;
    facebookName = response.facebookName;
    googleLinked = response.googleLinked;
    googleId = response.googleId;
    googleName = response.googleName;
    sex = response.sex;
    birthday = getDateLocal(response.birthday?.toDateTime(), isUtc: false);
    idCardType = response.idCardType;
    idCardNo = response.idCardNo;
    idCardIssueDate =
        getDateLocal(response.idCardIssueDate?.toDateTime(), isUtc: false);
    idCardExpireDate =
        getDateLocal(response.idCardExpireDate?.toDateTime(), isUtc: false);
    idCardIssuePlace = response.idCardIssuePlace;
    resAddr = response.resAddr;
    resCity = response.resCity;
    nationCode = response.nationCode;
    taxCode = response.taxCode;
    enabled2fa = response.enabled2fa;
    provinceCode = response.provinceCode;
    districtCode = response.districtCode;
    wardVlg = response.wardVlg;
    fullAddress = response.fullAddress;
  }

  ProfileInfo.fromResponseV2(QueryInfoResponseV2 response) {
    loginType = response.loginType;
    fullName = response.fullName;
    avatarUrl = response.avatarUrl;
    phoneNumber = response.phoneNumber;
    otpMethod = response.otpMethod;
    email = response.email;
    verifyEmail = response.verifiedEmail;
    aliasName = response.aliasName;
    username = response.username;
    facebookLinked = response.facebookLinked;
    facebookId = response.facebookId;
    facebookName = response.facebookName;
    googleLinked = response.googleLinked;
    googleId = response.googleId;
    googleName = response.googleName;
    sex = response.sex;
    birthday = getDateLocal(response.birthday?.toDateTime(), isUtc: false);
    idCardType = response.idCardType;
    idCardNo = response.idCardNo;
    idCardIssueDate =
        getDateLocal(response.idCardIssueDate?.toDateTime(), isUtc: false);
    idCardExpireDate =
        getDateLocal(response.idCardExpireDate?.toDateTime(), isUtc: false);
    idCardIssuePlace = response.idCardIssuePlace;
    resAddr = response.resAddr;
    resCity = response.resCity;
    nationCode = response.nationCode;
    taxCode = response.taxCode;
    enabled2fa = response.enabled2fa;
    createdFrom = response.createdFrom;
    lockedOldApp = response.lockedOldApp;
    provinceCode = response.provinceCode;
    districtCode = response.districtCode;
    wardVlg = response.wardVlg;
    fullAddress = response.fullAddress;
    bankVerified = response.bankVerified;
    suspiciousEkycStatus = response.suspiciousEkycStatus;
    customerGroupUserId = response.customerGroupUserId;
    passEkyc = response.passEkyc;
    organization = response.organization?.name;
    identityStatus = response.identityStatus;
    isVerifiedSTH = response.isVerifiedSTH;
  }

  ProfileInfo.fromResponseUpdate(UpdateInfoResponse response) {
    loginType = response.loginType;
    fullName = response.fullName;
    avatarUrl = response.avatarUrl;
    phoneNumber = response.phoneNumber;
    otpMethod = response.otpMethod;
    email = response.email;
    aliasName = response.aliasName;
    username = response.username;
    facebookLinked = response.facebookLinked;
    facebookId = response.facebookId;
    facebookName = response.facebookName;
    googleLinked = response.googleLinked;
    googleId = response.googleId;
    googleName = response.googleName;
    sex = response.sex;
    birthday = getDateLocal(response.birthday?.toDateTime(), isUtc: false);
    idCardType = response.idCardType;
    idCardNo = response.idCardNo;
    idCardIssueDate =
        getDateLocal(response.idCardIssueDate?.toDateTime(), isUtc: false);
    idCardExpireDate =
        getDateLocal(response.idCardExpireDate?.toDateTime(), isUtc: false);
    idCardIssuePlace = response.idCardIssuePlace;
    resAddr = response.resAddr;
    resCity = response.resCity;
    nationCode = response.nationCode;
    taxCode = response.taxCode;
    enabled2fa = response.enabled2fa;
  }

  ProfileInfo.fromJson(Map<String, dynamic> json) {
    loginType = json['loginType'];
    fullName = json['fullName'];
    avatarUrl = json['avatarUrl'];
    phoneNumber = json['phoneNumber'];
    otpMethod = json['otpMethod'];
    email = json['email'] ?? "";
    aliasName = json['aliasName'];
    username = json['username'];
    facebookLinked = json['facebookLinked'];
    facebookId = json['facebookId'];
    facebookName = json['facebookName'];
    googleLinked = json['googleLinked'];
    googleId = json['googleId'];
    googleName = json['googleName'];
    sex = json['sex'];
    birthday = getDate(json['birthday']);
    idCardType = json['idCardType'];
    idCardNo = json['idCardNo'];
    idCardIssueDate = getDate(json['idCardIssueDate']);
    idCardExpireDate = getDate(json['idCardExpireDate']);
    idCardIssuePlace = json['idCardIssuePlace'];
    resAddr = json['resAddr'];
    resCity = json['resCity'];
    nationCode = json['nationCode'];
    taxCode = json['taxCode'];
    enabled2fa = json['enabled2fa'];
    bankVerified = json['bankVerified'];
    customerGroupUserId = json['customerGroupUserId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['loginType'] = loginType;
    data['fullName'] = fullName;
    data['avatarUrl'] = avatarUrl;
    data['phoneNumber'] = phoneNumber;
    data['otpMethod'] = otpMethod;
    data['email'] = email;
    data['aliasName'] = aliasName;
    data['username'] = username;
    data['facebookLinked'] = facebookLinked;
    data['facebookId'] = facebookId;
    data['facebookName'] = facebookName;
    data['googleLinked'] = googleLinked;
    data['googleId'] = googleId;
    data['googleName'] = googleName;
    data['sex'] = sex;
    data['birthday'] = birthday?.toIso8601String();
    data['idCardType'] = idCardType;
    data['idCardNo'] = idCardNo;
    data['idCardIssueDate'] = idCardIssueDate?.toIso8601String();
    data['idCardExpireDate'] = idCardExpireDate?.toIso8601String();
    data['idCardIssuePlace'] = idCardIssuePlace;
    data['resAddr'] = resAddr;
    data['resCity'] = resCity;
    data['nationCode'] = nationCode;
    data['taxCode'] = taxCode;
    data['enabled2fa'] = enabled2fa;
    data['bankVerified'] = bankVerified;
    data['customerGroupUserId'] = customerGroupUserId;
    return data;
  }

  Map<String, dynamic> toMiniAppJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['phone'] = phoneNumber;
    data['full_name'] = fullName;
    data['email'] = email;
    data['birthday'] = birthday?.toIso8601String();
    data['sex'] = sex?.toInt ?? 0;
    data['idcard_no'] = idCardNo;
    data['idcard_issue_dt'] = idCardIssueDate?.toIso8601String();
    data['idcard_issue_plc'] = idCardIssuePlace;
    data['origin_add'] = resAddr;
    data['avatarUrl'] = avatarUrl;
    return data;
  }
}

enum ProfileField {
  loginType,
  fullName,
  avatarUrl,
  phoneNumber,
  otpMethod,
  email,
  aliasName,
  username,
  facebookLinked,
  facebookId,
  facebookName,
  googleLinked,
  googleId,
  googleName,
  sex,
  birthday,
  idCardType,
  idCardNo,
  idCardIssueDate,
  idCardExpireDate,
  idCardIssuePlace,
  resAddr,
  resCity,
  nationCode,
  taxCode,
  enabled2fa
}

class EkycStatus {
  static const String WAIT_CHECKING = "WAIT_CHECKING";
  static const String CHECKING = "CHECKING";
  static const String WAIT_CONFIRM = "WAIT_CONFIRM";
  static const String CONFIRMING = "CONFIRMING";
  static const String DONE = "DONE";
  static const String LOCKED = "LOCKED";
}
