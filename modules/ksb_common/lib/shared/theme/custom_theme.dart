import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ksb_common/shared/theme/custom_colors.dart';

ThemeData _getThemeData(BuildContext context) {
  final baseTheme = ThemeData.light();
  TextTheme textTheme = getTextTheme(baseTheme.textTheme);
  return baseTheme.copyWith(
    textTheme: textTheme.copyWith(
        titleMedium: textTheme.titleMedium?.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF333333),
        ),
        bodyMedium: textTheme.bodyMedium?.copyWith(fontSize: 14.sp),
        bodyLarge: textTheme.bodyLarge?.copyWith(
          color: const Color(0xFF333333).withOpacity(0.8),
        ),
        bodySmall: textTheme.bodySmall?.copyWith(
          color: const Color(0xFF333333).withOpacity(0.8),
        ),
        headlineMedium:
            textTheme.headlineMedium?.copyWith(color: Colors.black)),
    primaryColor: const Color(0xFF353282),
    canvasColor: Colors.white,
    buttonTheme: const ButtonThemeData(
      buttonColor: Color(0xFF353282),
    ),
    floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        backgroundColor: const Color(0xFF353282),
        foregroundColor: Colors.white),
    cardColor: Colors.white,
    cardTheme: const CardTheme(
      color: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    primaryIconTheme: Theme.of(context).iconTheme.copyWith(
          color: const Color(0xFF9C9C9C),
        ),
    indicatorColor: const Color(0xFF565656),
    iconTheme: Theme.of(context).iconTheme.copyWith(
          color: const Color(0xFF9C9C9C),
        ),
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: Colors.black54,
      selectionColor: Color(0xFFFFEFC6),
      selectionHandleColor: Color(0xFFFFEFC6),
    ),
    scaffoldBackgroundColor: Colors.white,
    dividerColor: const Color(0xFFDFDFDF),
    dividerTheme: const DividerThemeData(
      color: Color(0xFFDFDFDF),
      thickness: 0.55,
    ),
    bottomAppBarTheme: const BottomAppBarTheme(color: Color(0xFFE5E5E5)),
    disabledColor: Colors.grey,
    elevatedButtonTheme: getElevatedButtonTheme(
      textTheme,
      primaryColor: const Color(0xFF292663),
    ),
    popupMenuTheme: const PopupMenuThemeData(
      color: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    outlinedButtonTheme: getOutlinedButtonTheme(textTheme),
    tabBarTheme: baseTheme.tabBarTheme.copyWith(
      labelColor: const Color(0xFF353282),
      labelStyle: baseTheme.textTheme.bodyMedium,
      unselectedLabelStyle: baseTheme.textTheme.bodyMedium,
      unselectedLabelColor: const Color(0xFF565656),
      indicatorSize: TabBarIndicatorSize.tab,
    ),
    appBarTheme: AppBarTheme(
      color: Colors.white,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: StyleApp.titleBoldStyle(context, color: Colors.black),
      toolbarTextStyle: baseTheme.textTheme.titleMedium,
      actionsIconTheme: const IconThemeData(color: Color(0xFF097AD3)),
      iconTheme: const IconThemeData(color: Color(0xFF097AD3)),
      shadowColor: const Color(0xFFE5E5E5),
    ),
    colorScheme: const ColorScheme.light().copyWith(
      secondary: const Color(0xFF0095DE),
      primary: const Color(0xFF0095DE),
      background: const Color(0xFFF7F7F7),
      error: const Color(0xFFEB5757),
    ),
    listTileTheme: ListTileThemeData(
      subtitleTextStyle: textTheme.bodyMedium?.copyWith(
        color: const Color(0xFF333333).withOpacity(0.8),
      ),
    ),
    dialogTheme: DialogTheme(
      surfaceTintColor: Colors.transparent,
      actionsPadding: const EdgeInsets.all(8),
      titleTextStyle: textTheme.titleLarge,
      backgroundColor: Colors.white,
    ),
  );
}

///Config ThemeData For DarkMode
ThemeData _getThemeDataDark(BuildContext context) {
  final baseTheme = ThemeData.dark();
  TextTheme textTheme = getTextTheme(baseTheme.textTheme);
  return baseTheme.copyWith(
    textTheme: textTheme.copyWith(
        titleMedium: textTheme.titleMedium?.copyWith(fontSize: 16.sp),
        bodyMedium: textTheme.bodyMedium?.copyWith(fontSize: 14.sp),
        bodyLarge: textTheme.bodyLarge?.copyWith(
          color: const Color(0xFF333333).withOpacity(0.8),
        ),
        bodySmall: textTheme.bodySmall
            ?.copyWith(color: const Color(0xFF737373), fontSize: 12.sp),
        headlineMedium:
            textTheme.headlineMedium?.copyWith(color: Colors.white)),
    dividerColor: const Color(0xFF323232),
    dividerTheme: const DividerThemeData(
      color: Color(0xFF373737),
      thickness: 0.55,
    ),
    popupMenuTheme: const PopupMenuThemeData(
      color: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: Colors.white,
      selectionColor: Color(0xFFFFEFC6),
    ),
    floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        backgroundColor: const Color(0xFF353282),
        foregroundColor: Colors.white),
    scaffoldBackgroundColor: Colors.black,
    cardColor: const Color(0xFF0A0A0A),
    cardTheme: const CardTheme(
      color: Color(0xFF0A0A0A),
      surfaceTintColor: Colors.transparent,
    ),
    primaryColor: const Color(0xFF1A1E2D),
    buttonTheme: const ButtonThemeData(
      buttonColor: Color(0xFF8A89A0),
    ),
    bottomAppBarTheme: const BottomAppBarTheme(color: Color(0xFF171717)),
    appBarTheme: AppBarTheme(
      color: const Color(0XFF171717),
      surfaceTintColor: Colors.transparent,
      titleTextStyle: StyleApp.titleBoldStyle(context, color: Colors.white),
      toolbarTextStyle: baseTheme.textTheme.titleMedium,
      actionsIconTheme: const IconThemeData(color: Color(0xFF8A89A0)),
      iconTheme: const IconThemeData(color: Color(0xFF8A89A0)),
    ),
    elevatedButtonTheme: getElevatedButtonTheme(
      textTheme,
      primaryColor: const Color(0xFF292663),
    ),
    outlinedButtonTheme: getOutlinedButtonTheme(textTheme),
    tabBarTheme: baseTheme.tabBarTheme.copyWith(
      labelColor: const Color(0xFFFFFFFF),
      labelStyle: baseTheme.textTheme.bodyMedium,
      unselectedLabelColor: const Color(0xFFB1B1B1),
      unselectedLabelStyle: baseTheme.textTheme.bodyMedium,
      indicatorSize: TabBarIndicatorSize.tab,
    ),
    colorScheme: const ColorScheme.dark().copyWith(
      secondary: const Color(0xFF8A89A0),
      background: const Color(0xFF171717),
    ),
    listTileTheme: ListTileThemeData(
      subtitleTextStyle: textTheme.bodyMedium?.copyWith(
        color: const Color(0xFF333333).withOpacity(0.8),
      ),
    ),
    dialogTheme: DialogTheme(
      surfaceTintColor: Colors.transparent,
      actionsPadding: const EdgeInsets.all(8),
      titleTextStyle: textTheme.titleLarge,
      backgroundColor: Colors.white,
    ),
  );
}

ThemeData getTheme(BuildContext context, bool isLight) {
  if (isLight) {
    return _getThemeData(context);
  } else {
    return _getThemeDataDark(context);
  }
}

class DynamicTheme extends InheritedWidget {
  final BuildContext context;
  late ThemeData themeData;
  late CustomColor customColor;
  bool isThemeLight;

  DynamicTheme(
    this.context, {
    Key? key,
    Widget? child,
    this.isThemeLight = true,
  }) : super(key: key, child: child!) {
    themeData = getTheme(context, isThemeLight);
    customColor = getCustomColor(isThemeLight);
  }

  @override
  bool updateShouldNotify(DynamicTheme oldWidget) {
    return true;
  }

  static DynamicTheme? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<DynamicTheme>();
  }
}
