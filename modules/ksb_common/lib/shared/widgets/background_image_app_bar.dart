import 'package:common/utils/extension.dart';
import 'package:common/model/loading_event.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';

import '../../model/base_item.dart';
import '../assets.dart';
import '../constant.dart';
import 'background_app.dart';
import 'image/image_from_type.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';

class BackgroundImageAppBar extends StatelessWidget {
  const BackgroundImageAppBar({
    super.key,
    this.title,
    required this.child,
    this.appBar,
    this.isHasBackground = true,
    this.image,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.stream,
    this.loadingView,
    this.onTryClick,
    this.imageHeight,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.elevation,
    this.actions,
    this.color,
    this.leading,
    this.backgroundAppBarColor,
    this.brightness,
  });

  final Widget child;
  final PreferredSizeWidget? appBar;
  final bool isHasBackground;
  final dynamic image;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final Stream<LoadingWidgetModel>? stream;
  final Widget? loadingView;
  final VoidCallback? onTryClick;
  final double? imageHeight;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final String? title;
  final double? elevation;
  final List<Widget>? actions;
  final Color? color;
  final Widget? leading;
  final Color? backgroundAppBarColor;
  final Brightness? brightness;

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      backgroundColor: backgroundColor,
      isHasBackground: isHasBackground,
      stream: stream,
      // imageHeight: imageHeight,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      loadingView: loadingView,
      onTryClick: onTryClick,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      appBar: appBar ??
          AppBarCustom(
            elevation: elevation ?? 0.0,
            leading: leading,
            backgroundColor: backgroundAppBarColor ?? Colors.transparent,
            actions: actions,
            title: title,
            iconTheme: IconThemeData(
                color: color ??
                    (ksbCommonProperties?.brightness == true
                        ? Colors.black
                        : Colors.white)),
            brightness: brightness ??
                (ksbCommonProperties?.brightness == true
                    ? Brightness.light
                    : Brightness.dark),
            style: StyleApp.titleStyle(context).copyWith(
                color: color ??
                    (ksbCommonProperties?.brightness == true
                        ? Colors.black
                        : Colors.white)),
          ),
      image: CacheImageUrl(
        url: ksbCommonProperties?.backgroundImageAppBar,
        urlType: ksbCommonProperties?.backgroundImageAppBarType,
        localImage: ImageAssets.img_appbar,
      ),
      child: child,
    );
  }
}

class CacheImageUrl extends StatelessWidget {
  const CacheImageUrl({
    super.key,
    this.url,
    this.urlType,
    this.fit,
    this.emptyWidget,
    this.localImage,
    this.width,
    this.height,
    this.isLoading,
    this.loadingView,
    this.widthLoadingView,
    this.heightLoadingView,
    this.boxShapeDefaultLoadingView = BoxShape.rectangle,
    this.borRadiusDefaultLoadingView,
  }) : assert(
          boxShapeDefaultLoadingView != BoxShape.circle ||
              borRadiusDefaultLoadingView == null,
        );

  final String? url;
  final String? urlType;
  final BoxFit? fit;
  final Widget? emptyWidget;
  final dynamic localImage;
  final double? width;
  final double? height;
  final Widget? loadingView;
  final double? widthLoadingView;
  final double? heightLoadingView;
  final BoxShape boxShapeDefaultLoadingView;
  final BorderRadiusGeometry? borRadiusDefaultLoadingView;
  final bool? isLoading;

  @override
  Widget build(BuildContext context) {
    if (isLoading == true) {
      return loadingView ??
          Shimmer.fromColors(
            baseColor: Colors.black12,
            highlightColor: Colors.black87,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black12,
                shape: boxShapeDefaultLoadingView,
                borderRadius: borRadiusDefaultLoadingView ??
                    (boxShapeDefaultLoadingView == BoxShape.circle
                        ? null
                        : BorderRadius.circular(kRadiusButton)),
              ),
              width: widthLoadingView,
              height: heightLoadingView,
            ),
          );
    }
    if (url.isNullOrEmpty) {
      if (localImage is Widget) {
        return localImage;
      } else if (localImage is String) {
        return ImageAssets.svgAssets(
          localImage,
          fit: BoxFit.fill,
        );
      }
      return const SizedBox();
    }
    return getImageWidgetFromType(
      url,
      getImageTypeFrom(urlType),
      fit: BoxFit.fill,
      width: width,
      height: height,
      emptyUrl: emptyWidget ??
          StreamBuilder(
            stream: DefaultCacheManager().getFileStream(url!),
            builder: (context, snapshot) {
              if (snapshot.data is FileInfo) {
                final data = snapshot.data as FileInfo;
                return SvgPicture.file(
                  data.file,
                  fit: fit ?? BoxFit.fill,
                );
              }
              return Container();
            },
          ),
    );
  }
}

KSBCommonProperties? ksbCommonProperties;

class KSBCommonProperties {
  final String? backgroundImageAppBar;
  final String? backgroundImageAppBarType;
  final bool? brightness;
  final Color? priorityProfileColor;

  KSBCommonProperties({
    this.backgroundImageAppBar,
    this.backgroundImageAppBarType,
    this.brightness,
    this.priorityProfileColor,
  });
}
