import 'package:common/model/loading_event.dart';
import 'package:common/model/network_status.dart';
import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/loading/loading_widget.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ksb_common/shared/assets.dart';

import 'background_image_app_bar.dart';

class BackgroundAppBarImage extends StatelessWidget {
  final Widget child;
  final PreferredSizeWidget? appBar;
  final bool isHasBackground;
  final dynamic image;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final Stream<LoadingWidgetModel>? stream;
  final Widget? loadingView;
  final VoidCallback? onTryClick;
  final double? imageHeight;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool? useNetworkImageAppBar;

  const BackgroundAppBarImage({
    Key? key,
    required this.child,
    this.appBar,
    this.resizeToAvoidBottomInset = true,
    this.image,
    this.backgroundColor,
    this.isHasBackground = true,
    this.stream,
    this.onTryClick,
    this.imageHeight,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.loadingView,
    this.useNetworkImageAppBar = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wScreen = MediaQuery.of(context).size.width;
    final height =
        imageHeight ?? (MediaQuery.of(context).padding.top + toSp(48));
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: LayoutBuilder(
        builder: (context, constraint) {
          return Stack(
            children: [
              Positioned.fill(
                child: Container(
                  color: backgroundColor ?? Theme.of(context).cardColor,
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: constraint.maxHeight - height,
                child: useNetworkImageAppBar == true && appBar != null
                    ? CacheImageUrl(
                        url: ksbCommonProperties?.backgroundImageAppBar,
                        urlType: ksbCommonProperties?.backgroundImageAppBarType,
                        localImage: ImageAssets.img_appbar,
                      )
                    : (image is Widget
                        ? image
                        : image != null && image is String
                            ? ImageAssets.svgAssets(
                                image ?? '',
                                fit: BoxFit.fill,
                                width: wScreen,
                              )
                            : Container()),
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: appBar,
                body: Container(
                  color: appBar != null && isHasBackground
                      ? Theme.of(context).cardColor
                      : Colors.transparent,
                  child: child,
                ),
                resizeToAvoidBottomInset: resizeToAvoidBottomInset,
                floatingActionButton: floatingActionButton,
                floatingActionButtonLocation: floatingActionButtonLocation,
              ),
              Positioned.fill(
                child: LoadingWidget(
                  stream: stream,
                  onTryClick: onTryClick,
                  loading: loadingView,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class BackgroundApp extends StatelessWidget {
  final Widget child;
  final Color? color;
  final Stream<NetworkStatus>? networkStream;
  final Widget? backgroundWidget;

  const BackgroundApp({
    Key? key,
    required this.child,
    this.networkStream,
    this.color,
    this.backgroundWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        backgroundWidget ?? Container(),
        child,
        networkStream != null
            ? StreamBuilder<NetworkStatus>(
                stream: networkStream,
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data == NetworkStatus.none) {
                    return Material(
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Text("Ngắt kết nối"),
                          ],
                        ),
                        alignment: Alignment.bottomCenter,
                        color: Theme.of(context).colorScheme.error,
                        height: 66,
                      ),
                    );
                  }
                  return Container();
                })
            : Container()
      ],
    );
  }
}

class BackgroundImage extends StatelessWidget {
  final Widget child;
  final Widget? background;
  final AppBarCustom? appBar;
  final bool resizeToAvoidBottomInset;
  final Stream<LoadingWidgetModel>? stream;
  final VoidCallback? onTryClick;
  final bool isHome;

  const BackgroundImage({
    Key? key,
    required this.child,
    this.background,
    this.appBar,
    this.resizeToAvoidBottomInset = true,
    this.stream,
    this.onTryClick,
    this.isHome = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        background != null
            ? Positioned(top: 0, left: 0, right: 0, child: background!)
            : const BackgroundWidget(),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: appBar,
          body: child,
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        ),
        Positioned.fill(
          child: LoadingWidget(
            stream: stream,
            onTryClick: onTryClick,
          ),
        ),
      ],
    );
  }
}

class BackgroundAppBar extends StatelessWidget {
  final Widget child;
  final AppBarCustom? appBar;
  final Color? backgroundColor;
  final VoidCallback? onScreenTap;
  final Stream<LoadingWidgetModel>? stream;
  final VoidCallback? onTryClick;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final bool displayErrorOverlay;
  final bool extendBodyBehindAppBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  const BackgroundAppBar({
    Key? key,
    required this.child,
    this.appBar,
    this.backgroundColor,
    this.onScreenTap,
    this.stream,
    this.onTryClick,
    this.displayErrorOverlay = false,
    this.scaffoldKey,
    this.extendBodyBehindAppBar = false,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onScreenTap,
      child: Stack(
        children: [
          Scaffold(
            extendBodyBehindAppBar: extendBodyBehindAppBar,
            key: scaffoldKey,
            backgroundColor:
                backgroundColor ?? Theme.of(context).colorScheme.background,
            appBar: appBar,
            body: child,
            floatingActionButton: floatingActionButton,
            floatingActionButtonLocation: floatingActionButtonLocation,
          ),
          Positioned.fill(
            child: LoadingWidget(
              stream: stream,
              onTryClick: onTryClick,
            ),
          ),
        ],
      ),
    );
  }
}

class BackgroundWidget extends StatelessWidget {
  final Widget? child;
  final Widget? background;

  const BackgroundWidget({Key? key, this.child, this.background})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (background != null)
          Positioned.fill(
            child: FittedBox(fit: BoxFit.cover, child: background),
          ),
        Positioned(
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          child: child ?? Container(),
        )
      ],
    );
  }
}

class AppBarCustom extends AppBar with AppBarEco {
  AppBarCustom({
    Key? key,
    TextStyle? style,
    dynamic title,
    List<Widget>? actions,
    Stream<bool>? loadingStream,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    double? elevation = 1.0,
    double? titleSpacing = NavigationToolbar.kMiddleSpacing,
    IconThemeData? iconTheme,
    Brightness? brightness,
    Function()? backPage,
    Widget? leading,
    Widget? divider,
    bool showDivider = false,
    Color? titleColor,
    Color? shadowColor,
    double? scrolledUnderElevation,
    bool? centerTitle,
  }) : super(
          key: key,
          title: title is String
              ? getTitleView(title: title, style: style, titleColor: titleColor)
              : title,
          centerTitle: centerTitle ?? true,
          titleSpacing: titleSpacing,
          systemOverlayStyle: brightness != null
              ? SystemUiOverlayStyle(
                  statusBarBrightness: brightness,
                  statusBarIconBrightness: brightness == Brightness.dark
                      ? Brightness.light
                      : Brightness.dark,
                )
              : null,
          backgroundColor: backgroundColor,
          elevation: elevation,
          scrolledUnderElevation: scrolledUnderElevation ?? 0,
          shadowColor: shadowColor,
          bottomOpacity: 1.0,
          actions: actions,
          iconTheme: iconTheme,
          bottom: PreferredSize(
            child: loadingStream != null
                ? StreamBuilder<bool>(
                    stream: loadingStream,
                    initialData: false,
                    builder: (context, snapshot) => snapshot.data!
                        ? const SizedBox(
                            height: 1, child: LinearProgressIndicator())
                        : Container(
                            height: 0.5,
                            color: Colors.grey,
                          ),
                  )
                : divider ?? Container(),
            preferredSize: loadingStream != null
                ? const Size.fromHeight(1)
                : const Size.fromHeight(1),
          ),
          automaticallyImplyLeading: automaticallyImplyLeading,
          leading: backPage != null ? BackButton(onPressed: backPage) : leading,
        );
}

Widget baseRowText(
  String title,
  String value, {
  GestureTapCallback? onTap,
  Color? color,
  bool hasNext = false,
  bool hasNew = false,
}) {
  return baseRow(
      titleRow(title),
      Text(
        value,
        maxLines: 2,
        textAlign: TextAlign.right,
      ),
      onTap: onTap,
      color: color,
      hasNext: hasNext,
      hasNew: hasNew);
}

Widget titleRow(String title) {
  return Text(
    title,
    maxLines: 2,
    style: const TextStyle(color: Colors.white),
  );
}

Widget baseRow(
  Widget title,
  Widget value, {
  GestureTapCallback? onTap,
  Color? color,
  bool hasNext = false,
  bool hasNew = false,
}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      height: 42,
      color: color,
      child: Row(
        children: <Widget>[
          title,
          const SizedBox(
            width: 30.0,
          ),
          Expanded(
              child: Align(
            child: value,
            alignment: Alignment.centerRight,
          )),
          if (hasNew == true)
            Container(
              decoration: const BoxDecoration(
                color: Colors.redAccent,
                shape: BoxShape.circle,
              ),
              width: 8.0,
              height: 8.0,
            ),
          if (hasNext) ...[
            const Icon(
              Icons.navigate_next,
              color: Colors.black54,
            ),
            const SizedBox(width: 4.0)
          ] else
            const SizedBox(width: 10.0)
        ],
      ),
    ),
  );
}

mixin AppBarEco on PreferredSizeWidget {
  @override
  get preferredSize => const Size.fromHeight(53);
}

class DropWidget extends StatelessWidget {
  final String? title;
  final String? value;
  final Function? onTap;

  const DropWidget({Key? key, this.title, this.value, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap != null) onTap!();
      },
      child: Container(
        padding:
            const EdgeInsets.only(left: 15, right: 15, top: 15, bottom: 15),
        decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title ?? '',
              style: StyleApp.bodyStyle(context),
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              children: [
                Expanded(
                    child: Text(
                  value ?? '',
                  style: StyleApp.settingStyle(context),
                )),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Theme.of(context).primaryColor,
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}

Widget checkBoxCustome(BuildContext context, bool status) {
  return Container(
    alignment: Alignment.center,
    width: 24,
    height: 24,
    decoration: BoxDecoration(
      borderRadius: const BorderRadius.all(Radius.circular(4)),
      border: Border.all(width: 2.5, color: Colors.grey),
    ),
    child: status
        ? Icon(Icons.check,
            color: Theme.of(context).colorScheme.onPrimary, size: 20)
        : Container(),
  );
}

Widget getTitleView(
    {required String title, TextStyle? style, Color? titleColor}) {
  return Text(
    title,
    style: style ??
        TextStyle(fontSize: 17, fontWeight: FontWeight.w600, color: titleColor),
    overflow: TextOverflow.ellipsis,
  );
}

Widget getBackView(BuildContext context, {Color? color}) {
  return IconButton(
    icon: Icon(
      Icons.arrow_back_ios,
      color: color ?? Colors.white,
    ),
    onPressed: () => Navigator.pop(context),
  );
}

Widget getCloseView(BuildContext context, {Color? color}) {
  return IconButton(
    icon: Icon(
      Icons.close,
      color: color ?? Colors.white,
    ),
    onPressed: () => Navigator.pop(context),
  );
}

AppBarCustom getAppBarDark(
  BuildContext context, {
  dynamic title,
  double? elevation,
  List<Widget>? actions,
  Color? color,
  Widget? leading,
  Color? backgroundColor,
  Brightness? brightness,
  Function()? backPage,
  bool automaticallyImplyLeading = true,
  bool? useNetworkImage = true,
}) {
  final colorFilter = color ??
      (useNetworkImage == true
          ? (ksbCommonProperties?.brightness == true ? Colors.black : Colors.white)
          : Colors.white);
  return AppBarCustom(
    title: title is Widget
        ? ColorFiltered(
            colorFilter: ColorFilter.mode(colorFilter, BlendMode.srcIn),
            child: title,
          )
        : title,
    style: StyleApp.titleStyle(context).copyWith(
      color: colorFilter,
    ),
    iconTheme: IconThemeData(color: colorFilter),
    elevation: elevation ?? 0.0,
    brightness: brightness ??
        (useNetworkImage == true
            ? (ksbCommonProperties?.brightness == true
                ? Brightness.light
                : Brightness.dark)
            : Brightness.dark),
    leading: leading,
    backgroundColor: backgroundColor ?? Colors.transparent,
    actions: actions?.map((e) {
      return ColorFiltered(
        colorFilter: ColorFilter.mode(colorFilter, BlendMode.srcIn),
        child: e,
      );
    }).toList(),
    automaticallyImplyLeading: automaticallyImplyLeading,
    backPage: backPage,
    scrolledUnderElevation: 0.0,
  );
}
