import 'package:common/global_callback.dart';
import 'package:common/model/base_response.dart';
import 'package:flutter/material.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

import '../route_path.dart';

abstract class FlowTransactionState<T extends StatefulWidget> extends State<T> {
  String? phoneNumber;
  BaseProfile? profile;

  _getPhoneNumber() async {
    profile = await _getProfile();
    phoneNumber = profile?.phoneNumber;
  }

  _getProfile() {
    return GlobalCallback.instance.onGetProfile?.call();
  }

  @override
  void initState() {
    super.initState();
    _getPhoneNumber();
  }

  @protected
  updateCCCD({String? firstRoute, String? route}) async {
    phoneNumber ??= (profile ?? (await _getProfile()))?.phoneNumber;
    if (!context.mounted) return;
    GlobalCallback.instance.callBackForBiology?.updateCCCD?.call(
      context,
      phoneNumber: phoneNumber!,
      firstRoute: firstRoute,
      route: route,
    );
  }
}

mixin AuthTransactionMixin<T extends StatefulWidget>
    on FlowTransactionState<T> {
  String get routeAuth => RoutePaths.home;

  ///override with BaseResponseModel for each module
  listenError(BaseResponseModel? status) {
    GlobalCallback.instance.callBackForBiology?.handleError?.call(
      context,
      status: status,
      updateCCCD: () => updateCCCD(route: routeAuth),
      handleGoToLogin: () => GlobalCallback
          .instance.callBackForBiology?.handleGoToLogin
          ?.call(context),
    );
  }

  onGoToConfirm({
    required VoidCallback nextToConfirmPage,
    required TransNextStep? nextStep,
    required String? message,
  }) {
    if ([TransNextStep.SHOW_GUIDE, TransNextStep.SHOW_GUIDE_GTTT_EXPIRED]
        .contains(nextStep)) {
      GlobalCallback.instance.callBackForBiology?.handleNotAuth?.call(
        context,
        onCancel: nextToConfirmPage,
        onSubmit: () => updateCCCD(route: routeAuth),
        message: message,
      );
      return;
    }
    nextToConfirmPage.call();
  }
}

mixin AuthTransactionConfirmMixin<T extends StatefulWidget>
    on FlowTransactionState<T> {
  bool _passAuthFaceID = false;

  bool get passAuthFaceID => _passAuthFaceID;

  void setPassAuthFaceID(bool value) => _passAuthFaceID = value;

  String get firstRoute => RoutePaths.home;

  String get routeConfirm => RoutePaths.home;

  Future<void> authByFaceID({
    bool showFaceID = false,
    String? transactionNo,
  }) async {
    if (!showFaceID) {
      _passAuthFaceID = true;
    } else {
      phoneNumber ??= (profile ?? (await _getProfile()))?.phoneNumber;
      if (!mounted) return;
      final result =
          await GlobalCallback.instance.callBackForBiology?.authFaceID?.call(
        context,
        firstRoute: firstRoute,
        transactionNo: transactionNo,
        phoneNumber: phoneNumber!,
        routeConfirm: routeConfirm,
      );
      if (result == true) {
        _passAuthFaceID = true;
      }
    }
  }

  Future<void> authByFaceIDWithAWSLiveness({
    bool showFaceID = false,
    String? transactionNo,
    String? sessionId,
    String? sessionToken,
    String? region,
    String? accessKeyId,
    String? secretAccessKey,
    Future<bool?> Function()? onComplete,
  }) async {
    if (!showFaceID) {
      _passAuthFaceID = true;
    } else {
      if (!mounted || sessionId == null || sessionToken == null) return;
      final result =
          await GlobalCallback.instance.callBackForBiology?.authFaceIDV2?.call(
        context,
        transactionNo: transactionNo,
        sessionId: sessionId,
        sessionToken: sessionToken,
        region: region,
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
        // onComplete: onComplete,
      );
      if (result == true) {
        _passAuthFaceID = (await onComplete?.call()) ?? false;
      }
    }
  }
}
