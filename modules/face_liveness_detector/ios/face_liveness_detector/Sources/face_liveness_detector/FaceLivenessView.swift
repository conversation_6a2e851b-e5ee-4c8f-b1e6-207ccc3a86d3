import Flutter
import SwiftUI
import FaceLiveness
import Amplify
import AWSPluginsCore

class FaceLivenessView: NSObject, FlutterPlatformView {
    private var _view: UIView

    init(
        frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?,
        binaryMessenger messenger: FlutterBinaryMessenger?,
        handler: EventStreamHadler
    ) {
        _view = UIView(frame: frame)
        super.init()

        // Configure the base view for full screen
        _view.backgroundColor = UIColor(red: 0.18, green: 0.18, blue: 0.21, alpha: 1.0)
        _view.clipsToBounds = true

        createNativeView(view: _view, arguments: args, handler: handler)
    }

    func view() -> UIView {
        return _view
    }
    
    func createNativeView(view _view: UIView, arguments args: Any?, handler: EventStreamHadler){
        guard let args = args as? [String: Any] else { return }

        let keyWindows = UIApplication.shared.windows.first(where: { $0.isKeyWindow}) ?? UIApplication.shared.windows.first
        let topController = keyWindows?.rootViewController

        // Extract credentials parameters
        let accessKeyId = args["accessKeyId"] as? String
        let secretAccessKey = args["secretAccessKey"] as? String
        let sessionToken = args["sessionToken"] as? String
        let expiration = args["expiration"] as? Double
        let disableStartView = args["disableStartView"] as? Bool ?? true

        let vc = UIHostingController(
            rootView: NativeView(
                sessionId: args["sessionId"] as! String,
                region: args["region"] as! String,
                accessKeyId: accessKeyId,
                secretAccessKey: secretAccessKey,
                sessionToken: sessionToken,
                expiration: expiration,
                disableStartView: disableStartView,
                handler: handler
            )
        )

        // Configure for full screen presentation
        vc.modalPresentationStyle = .fullScreen
        vc.modalTransitionStyle = .crossDissolve

        let swiftUiView = vc.view!
        swiftUiView.translatesAutoresizingMaskIntoConstraints = false

        // Set background color to ensure full coverage
        swiftUiView.backgroundColor = UIColor(red: 0.18, green: 0.18, blue: 0.21, alpha: 1.0) // #2E2D36
        _view.backgroundColor = UIColor(red: 0.18, green: 0.18, blue: 0.21, alpha: 1.0)

        // Ensure the view fills the entire container
        swiftUiView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        swiftUiView.frame = _view.bounds

        topController?.addChild(vc)
        _view.addSubview(swiftUiView)

        // Use constraints to ensure full screen coverage
        NSLayoutConstraint.activate([
            swiftUiView.leadingAnchor.constraint(equalTo: _view.leadingAnchor),
            swiftUiView.trailingAnchor.constraint(equalTo: _view.trailingAnchor),
            swiftUiView.topAnchor.constraint(equalTo: _view.topAnchor),
            swiftUiView.bottomAnchor.constraint(equalTo: _view.bottomAnchor)
        ])

        vc.didMove(toParent: topController)

        // Force layout update
        DispatchQueue.main.async {
            _view.setNeedsLayout()
            _view.layoutIfNeeded()
        }
    }
}

struct NativeView: View {
    let sessionId: String
    let region: String
    let accessKeyId: String?
    let secretAccessKey: String?
    let sessionToken: String?
    let expiration: Double?
    let disableStartView: Bool
    let handler: EventStreamHadler
    
    @State private var isPresentingLiveness = true
    
    init(sessionId: String, region: String, accessKeyId: String?, secretAccessKey: String?, sessionToken: String?, expiration: Double?, disableStartView: Bool, handler: EventStreamHadler) {
        self.sessionId = sessionId
        self.region = region
        self.accessKeyId = accessKeyId
        self.secretAccessKey = secretAccessKey
        self.sessionToken = sessionToken
        self.expiration = expiration
        self.disableStartView = disableStartView
        self.handler = handler
    }

    var body: some View {
        // Prepare custom credentials provider if all credentials are provided
        // This is ready for future implementation when credentialsProvider parameter is available
        let customCredentialsProvider: MyCredentialsProvider? = {
            guard let accessKeyId = accessKeyId,
                  let secretAccessKey = secretAccessKey,
                  let sessionToken = sessionToken else {
                return nil
            }

            let expirationDate = expiration.map { Date(timeIntervalSince1970: $0) } ?? Date(timeIntervalSinceNow: 3600)
            return MyCredentialsProvider(
                accessKeyId: accessKeyId,
                secretAccessKey: secretAccessKey,
                sessionToken: sessionToken,
                expiration: expirationDate
            )
        }()

        // TODO: Use customCredentialsProvider when FaceLivenessDetectorView supports credentialsProvider parameter
        // For now, using standard implementation with Amplify Auth
        FaceLivenessDetectorView(
            sessionID: self.sessionId,
            credentialsProvider: customCredentialsProvider,
            region: self.region,
            disableStartView: self.disableStartView,
            isPresented: $isPresentingLiveness,
            onCompletion: { result in
                switch result {
                case .success:
                    handler.onComplete()
                case .failure(let error):
                    switch error {
                    case .userCancelled:
                        handler.onError(code: "userCancelled")
                        return
                    case .sessionTimedOut:
                        handler.onError(code: "sessionTimedOut")
                        return
                    case .sessionNotFound:
                        handler.onError(code: "sessionNotFound")
                        return
                    default:
                        handler.onError(code: "error")
                        return
                    }
                default:
                    return
                }
            }
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white) // Change background to white to match the oval area
        .ignoresSafeArea(.all)
    }
} 
