group 'com.gunschu.jitsi_meet'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.8.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        maven {
            url "https://git.sunshinetech.vn/quyenlv/jitsi-maven-repository/raw/master/releases"
        }
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.gunschu.jitsi_meet'
    }
    compileSdkVersion 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 23
    }
    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    // ./gradlew build --refresh-dependencies for refresh dependencies
    // Jitsi Meet from https://git.sunshinetech.vn/quyenlv/jitsi-maven-repository/raw/master/releases
    // Fix Defective SoLoader Version https://support.google.com/faqs/answer/12576726
    implementation 'com.facebook.soloader:soloader:0.10.4+'
    implementation ('org.jitsi.react:jitsi-meet-sdk:6.2.1') { transitive = true }
}
