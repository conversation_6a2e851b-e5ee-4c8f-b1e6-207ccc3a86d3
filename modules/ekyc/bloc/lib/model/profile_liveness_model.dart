import 'package:common/ks_common.dart';

class ProfileLivenessModel {
  ProfileLivenessModel({
    this.areaHeight,
    this.areaLeft,
    this.areaTop,
    this.areaWidth,
    this.exercise,
    this.externalPersonId,
    this.id,
    this.imageHeight,
    this.imageWidth,
    this.minFaceAreaPercent,
    this.taskOrder,
    this.message,
    this.resultCode,
    this.challengeId,
  });

  int? areaHeight;
  int? areaLeft;
  int? areaTop;
  int? areaWidth;
  String? exercise;
  String? externalPersonId;
  String? id;
  int? imageHeight;
  int? imageWidth;
  int? minFaceAreaPercent;
  TaskOrder? taskOrder;
  String? message;
  int? resultCode;
  String? challengeId;

  double? noseTop;
  double? noseLeft;
  double? noseWidth;
  double? noseHeight;
  double? frameRate;
  int? duration;
  String? imagePath; // Note: not in the response
  String? faceUrl;

  factory ProfileLivenessModel.fromJson(Map<String, dynamic> json) =>
      ProfileLivenessModel(
        areaHeight: json["area_height"],
        areaLeft: json["area_left"],
        areaTop: json["area_top"],
        areaWidth: json["area_width"],
        // exercise: json["exercise"],
        externalPersonId: json["external_person_id"],
        id: json["id"],
        imageHeight: json["image_height"],
        imageWidth: json["image_width"],
        minFaceAreaPercent: json["min_face_area_percent"],
        taskOrder: TaskOrder.fromJson(json["task_order"]),
        message: json["message"],
        resultCode: json["result_code"],
        challengeId: json['id'],
      );

  Map<String, dynamic> toJson() => {
        "area_height": areaHeight,
        "area_left": areaLeft,
        "area_top": areaTop,
        "area_width": areaWidth,
        "exercise": exercise,
        "external_person_id": externalPersonId,
        "id": id,
        "image_height": imageHeight,
        "image_width": imageWidth,
        "min_face_area_percent": minFaceAreaPercent,
        // "task_order": taskOrder?.toJson(),
        "message": message,
        "result_code": resultCode,
        "challenge_id": challengeId,
      };
}

class TaskOrder {
  TaskOrder({
    this.tasks,
  });

  List<Task>? tasks;

  factory TaskOrder.fromJson(Map<String, dynamic> json) {
    List<Task>? listTask = [];
    for (var element in json.keys) {
      final times = json[element];
      if (times != 0) {
        listTask.add(Task(task: element, times: times));
      }

    }
    return TaskOrder(tasks: listTask);
  }

// Map<String, dynamic> toJson() => {
//       "turn-face-right": turnFaceRight,
//       "turn-face-left": turnFaceLeft,
//       "keep-face-ahead": keepFaceAhead,
//       "tilt-face-up": tiltFaceUp,
//       "tilt-face-down": tiltFaceDown,
//     };
}

class Task {
  String? task;
  int? times;

  Task({this.task, this.times});
}

class ProfileLiveNessVerifyResponse {
  bool? success;
  String? message;

  // Không tìm thấy khuôn mặt trong hình
  static const String NO_FACE_DETECTED = 'No Face Detected';

  // Quá nhiều khuôn mặt trong khung hình
  static const String FOUND_MORE_THAN_ONE_FACE = 'Found More Than One Face';

  // Không tìm thấy task order
  static const String TASK_ORDER_NOT_FOUND = 'Task Order Not Found';

  // Challenge ID không tồn tại
  static const String REQUEST_CHALLENGE_ID_NOT_FOUND =
      'Request Challenge ID Not Found';

  // Challenge chưa có ảnh để xác thực
  static const String MOVING_PHOTO_IS_MISSING = 'Moving Photo Is Missing';

  // Khuôn mặt góc phải không chính xác
  static const String RIGHT_FACE_ANGEL_NOT_CORRECT =
      'Right Face Angle Not Correct';

  // Khuôn mặt góc trái không chính xác
  static const String LEFT_FACE_ANGEL_NOT_CORRECT =
      'Left Face Angle Not Correct';

  // Khuôn mặt ngước trên không chính xác
  static const String UP_FACE_ANGEL_NOT_CORRECT = 'Up Face Angle Not Correct';

  // Khuôn mặt cúi dưới không chính xác
  static const String DOWN_FACE_ANGEL_NOT_CORRECT =
      'Down Face Angle Not Correct';

  // Khuôn mặt góc thẳng không chính xác
  static const String AHEAD_FACE_ANGEL_NOT_CORRECT =
      'Ahead Face Angle Not Correct';

  // Challenge thực hiện quá lâu
  static const String TRAJECTORY_TOO_SLOW = 'Trajectory Too Slow';

  //Lỗi không xác định
  static const String unknownException = 'UNKNOWN_EXCEPTION';

  ProfileLiveNessVerifyResponse({
    this.success,
    this.message,
  });

  ProfileLiveNessVerifyResponse.fromMap(Map<String, dynamic> json) {
    success = toBool(json['success']);
    message = json['message'];
  }
}
