import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:ksb_common/model/payment_qr/money_model.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';
import 'package:myshop_api/myshop_api.dart';
import 'package:umee_shop/environment.dart';
import 'package:umee_shop/repository/src/model/index.dart';
import 'package:umee_shop/repository/src/model/magic_paybox.dart';
import 'package:umee_shop/repository/src/model/package_plan/preview_package_invoice_model.dart';

import '../../ui/send_mail/model/list_email_model.dart';
import 'model/business/business_model.dart';
import 'model/magic_paybox_responses.dart';
import 'model/notify_payment/package_plan_payment_notify_model.dart';
import 'model/package_plan/package_invoice_model.dart';
import 'model/package_plan/package_plan_model.dart';
import 'model/select_account/select_account_model.dart';

abstract class MyShopRepository {
  void setUp(Environment environment, Interceptor interceptor);

  String getPayBoxConfigUrl();

  Future<String?> updateImageFile(File file);

  Future<ShopModel?> createShop(ShopModel? shopModel, {String? otp});

  Future<ShopResponseModel?> getListShop({int? page, int? pageSize});

  Future<ShopResponseModel?> getListShop2({int? page, int? pageSize});

  Future<ShopModel?> getDetailShop({
    String? shopId,
    String? shopCode,
  });

  Future<TransactionResponseModel?> getTransactionHisShop({
    required String shopId,
    String? fromDate,
    String? toDate,
    int? page,
    int? pageSize,
  });

  Future<bool> checkUserCreatedOrJoinShop();

  Future<MemberResponseModel?> getMemberShop(
      {required String shopId, int? page, int? pageSize});

  Future<bool> checkShopIdExist({
    required String shopId,
  });

  Future<bool> cancelRequestToJoinShop({String? requestId});

  Future<bool> approveRequestToJoinShop({String? requestId});

  Future<bool> deleteMember({String? shopId, String? userUId});

  Future<bool> rejectRequestToJoinShop({String? requestId});

  Future<MoneyModel> createPaymentRequest(
      {String? shopId,
      double? amount,
      String? description,
      String? accountNo,
      required bool defaultTransfer,
      List<String>? payBoxIds});

  Future<ShopModel> updateShopInfo({ShopModel? shopModel, String? otp});

  Future<bool> deleteShop({required String shopId, required String otp});

  Future<bool> receivedRequestJoin({
    required String shopId,
    required bool allow,
  });

  Future<bool> checkNickNameExist({
    required String nickName,
    required String accountNo,
  });

  Future<bool> checkShopName({required String shopName});

  Future<bool> setPayboxStatus(
      {required String shopId, required String deviceId, required bool active});

  Future<bool> unPairShop({required String shopId, required String deviceId});
  Future<bool> pairShop({
    required String shopId,
    required String deviceId,
    required String pairingKey,
    String? referralCode,
    String? referralBranchCode,
    String? magicPlanId,
  });

  Future<bool> createRequestJoinShop({String? shopCode});

  Future<ShopModel?> createDefaultTransferDescShop({
    String? shopId,
    String? defaultTransferDesc,
  });

  Future<bool> leaveShop({required String shopId});

  Future<ReferralModel?> getReferral({required String referralCode});

  Future<ShopModel?> getRecentShop();

  Future<PayboxInfo?> getDetailPaybox({required String deviceId});

  // Send Mail
  Future<List<ShopEmailItem>?> getListShopEmail(
      {required String shopId, String? email});

  Future<ShopEmailItem?> addEmailToShop(
      {required String shopId, required String email});

  Future<bool?> deleteEmailFromShop({required String emailId});

  Future<bool?> sendTransHistoryViaEmail(
      {required String shopId,
      required List<String> listEmail,
      required String deviceName,
      required String fromDate,
      required String toDate,
      required String otp});

  Future<Uint8List?> printHistoryTransShop({
    required String shopId,
    required String fromDate,
    required String toDate,
  });

  // Package Plans
  Future<List<PackagePlan>?> getPackageList();

  Future<PackageDetail?> myPackageDetail();

  Future<PackagePlan?> packageDetailById({
    required String id,
  });

  Future<PackageDetail?> registerPackagePlan({
    required String planId,
    required String accountNo,
    required String otp,
    String? transactionNo,
  });

  Future<bool> deleteMyPakage();

  Future<CheckCanCreateShopResponse?> checkCanUseCreateShop();

  Future<CheckUserReferralData?> checkReferralCodeCreateShop(
      {required String referralCode});

  Future<List<PayboxInfo>?> getListPayBox({required String shopId});

  Future<AccountOrganizationModel?> getOrganizationAccounts({
    SelectAccountType? accountType,
    String? onlyTTGT,
  });

  Future<bool?> checkUserHasOrganizeAccount();

  Future<bool?> settingTransferNotification({
    required String shopId,
    bool? isNotifyOwner = false,
    bool? isNotifyMember = false,
  });

  Future<List<PackageInvoice>?> getPackagePlanInvoiceList({
    int? year,
    PackageInvoiceStatus? status,
    required int page,
    required int size,
  });

  Future<bool> payPackageInvoiceFee({
    required String invoiceId,
    required String otp,
    required String transactionNo,
  });

  Future<PreviewPackageInvoice?> getPackagePlanInvoiceDetail(
      {required String invoiceId});

  Future<TransactionDetailModel?> getTransactionDetail({
    required String transactionId,
  });

  Future<PackagePlanPaymentNotifyModel?> getPackagePlanNotifyPayment();

  Future<bool?> disableNotifyPayment();

  Future<List<IndustryModel?>?> getListBusiness();

  Future<CheckCashLimitationResponse?> callReviewTransactionApi({
    required double amount,
    required TransactionCategoriesType transactionCategoriesType,
  });

  // Magic Paybox
  Future<List<MagicPackage>?> getMagicPackageListByDeviceId(
      {required String id});

  Future<void> getPayboxTypeList();

  Future<ListAllPayboxModelResponse?> getPayboxModelList();

  Future<PackageDetail?> registerMagicPackage({
    required String planId,
    required String accountNo,
    required String otp,
    String? transactionNo,
    String? magicPackageId,
    String? deviceModel,
    String? modelType,
  });

  Future<ListRegisteredMagicPayboxResponse?> getListRegisteredMagicPaybox({
    required String planId,
    required String status,
  });

  Future<bool> checkCancelOrChangePackage(
      {String? registeredMagicPackageId, String? planId});

  Future<RegisteredMagicPaybox?> cancelMagicPaybox(
      {required String registeredMagicPackageId, required String otp});

  Future<CheckDevicePurchasedResponseModel?> isHiredOrBoughtDevice(
      {required String serialNumber, required String model});

  Future<ChangeMagicPackageResponse?> changeMagicPackage({
    String? accountNo,
    String? registeredMagicPackageId,
    String? newMagicPackageId,
    required String otp,
  });
}
