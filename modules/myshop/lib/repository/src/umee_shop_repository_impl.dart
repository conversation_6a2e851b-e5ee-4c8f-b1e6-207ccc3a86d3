import 'dart:io';
import 'dart:typed_data';

import 'package:built_collection/built_collection.dart';
import 'package:common/utils/log.dart';
import 'package:dio/dio.dart';
import 'package:ksb_common/model/constant_type.dart';
import 'package:ksb_common/model/payment_qr/money_model.dart';
import 'package:ksbank_api_loyalty/ksbank_api_loyalty.dart' as loyalty;
import 'package:ksbank_api_media/ksbank_api_media.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart' as smartBank;
import 'package:myshop_api/myshop_api.dart';
import 'package:path/path.dart';
import 'package:umee_shop/di/injection.dart';
import 'package:umee_shop/environment.dart';
import 'package:umee_shop/repository/src/model/business/business_model.dart';
import 'package:umee_shop/repository/src/model/magic_paybox.dart';
import 'package:umee_shop/repository/src/model/magic_paybox_responses.dart';
import 'package:umee_shop/repository/src/model/package_plan/preview_package_invoice_model.dart';
import 'package:umee_shop/repository/umee_shop_repository.dart';

import '../../ui/send_mail/model/list_email_model.dart';
import 'model/notify_payment/package_plan_payment_notify_model.dart';
import 'model/package_plan/package_invoice_model.dart';
import 'model/package_plan/package_plan_model.dart';
import 'model/select_account/select_account_model.dart';

final repository = getIt<MyShopRepository>();

class MyShopRepositoryImpl extends MyShopRepository {
  MyshopApi _apiShop;
  KsbankApiMedia _apiMedia;
  smartBank.KsbankApiSmartbank _apiSmartBank;
  loyalty.KsbankApiLoyalty _loyaltyApi;
  String _payBoxConfigUrl;

  MyShopRepositoryImpl(
      {required MyshopApi apiShop,
      required KsbankApiMedia apiMedia,
      required smartBank.KsbankApiSmartbank apiSmartBank,
      required loyalty.KsbankApiLoyalty loyaltyApi,
      required String payBoxConfigUrl})
      : _apiShop = apiShop,
        _apiMedia = apiMedia,
        _apiSmartBank = apiSmartBank,
        _loyaltyApi = loyaltyApi,
        _payBoxConfigUrl = payBoxConfigUrl;

  @override
  setUp(Environment environment, Interceptor interceptor) {
    _apiShop.dio.options.baseUrl = (environment.serverUrl ?? "") + "/myshop";
    _apiMedia.dio.options.baseUrl = (environment.serverUrl ?? "") + "/media";
    _apiSmartBank.dio.options.baseUrl =
        (environment.serverUrl ?? "") + "/smartbank";
    _payBoxConfigUrl = environment.payboxConfigUrl ?? '';
  }

  @override
  String getPayBoxConfigUrl() => _payBoxConfigUrl;

  @override
  Future<ShopModel?> createShop(ShopModel? shopModel, {String? otp}) {
    return _apiShop.getAPIShopApi().createShop(
        registerShopRequest: RegisterShopRequest((b) {
      b.accountNo = shopModel?.accountNo;
      b.accountNoNickname = shopModel?.accountNickname;
      b.nickName = shopModel?.nickName;
      b.name = shopModel?.name;
      b.owner = shopModel?.owner;
      b.address = shopModel?.address;
      b.addressDetail = shopModel?.addressDetail;
      b.latitudeAddress = shopModel?.latitude;
      b.longitudeAddress = shopModel?.longitude;
      b.fontImage = shopModel?.fontImage;
      b.isPushNotify = shopModel?.isPushNotify;
      b.isSmsNotify = shopModel?.isSmsNotify;
      b.isEmailNotify = shopModel?.isEmailNotify;
      b.referralCode = shopModel?.referralModel?.code;
      b.verifySoftOtp = VerifySoftOtpRequestBuilder()..otp = otp;
      b.industryId = shopModel?.industryId;
    })).then((value) {
      final myShop = ShopModel.fromResponse(value.data?.data);
      return myShop;
    });
  }

  @override
  Future<ShopResponseModel?> getListShop({int? page, int? pageSize}) {
    return _apiShop
        .getAPIShopApi()
        .getShopsUserCreatedOrJoined(page: page, size: pageSize)
        .then((value) {
      return ShopResponseModel.fromResponse(value.data?.data);
    });
  }

  @override
  Future<TransactionResponseModel?> getTransactionHisShop({
    required String shopId,
    String? fromDate,
    String? toDate,
    int? page,
    int? pageSize,
  }) async {
    final result = await _apiShop.getAPIQunLGiaoDchApi().getTransHisInfoShop(
          shopId: shopId,
          fromDate: fromDate,
          toDate: toDate,
          size: pageSize,
          page: page,
        );
    return TransactionResponseModel.fromResponse(result.data!.data!);
  }

  @override
  Future<ShopModel?> getDetailShop({String? shopId, String? shopCode}) async {
    final result = await _apiShop
        .getAPIShopApi()
        .getShopDetailByShopId(shopId: shopId, shopCode: shopCode);
    return ShopModel.fromDetailResponse(result.data?.data);
  }

  @override
  Future<bool> checkUserCreatedOrJoinShop() {
    return _apiShop.getAPIShopApi().checkUserCreatedOrJoinShop().then((res) {
      final success = res.data?.success ?? false;
      final createdOrJoinShop = res.data?.data?.createdOrJoinShop ?? false;
      return success && createdOrJoinShop;
    }).catchError((error) {
      print(error);
    });
  }

  @override
  Future<MemberResponseModel> getMemberShop(
      {required String shopId, int? page, int? pageSize}) async {
    final result = await _apiShop.getAPIShopApi().getListUsers(shopId: shopId);
    return MemberResponseModel.fromResponse(result.data?.data);
  }

  Future<bool> checkShopIdExist({required String shopId}) {
    return _apiShop
        .getAPIShopApi()
        .checkShopCodeExist(shopCode: shopId)
        .then((value) {
      return value.data?.data?.exist ?? false;
    });
  }

  @override
  Future<String?> updateImageFile(File file) {
    return _apiMedia
        .getMinioControllerApi()
        .upload(
          fileType: FileType.OTHER,
          file: MultipartFile.fromFileSync(
            file.path,
            filename: basename(file.path),
          ),
        )
        .then((value) {
      return value.data?.previewUrl;
    });
  }

  @override
  Future<bool> approveRequestToJoinShop({String? requestId}) async {
    final result = await _apiShop.getAPIShopApi().approveRequestToJoinShop(
        approveRequestToJoinShopRequest: ApproveRequestToJoinShopRequest(
      (b) {
        b.requestId = requestId;
      },
    ));
    return result.data?.success ?? false;
  }

  @override
  Future<bool> cancelRequestToJoinShop({String? requestId}) async {
    final result = await _apiShop.getAPIShopApi().cancelRequestToJoinShop(
        cancelRequestToJoinShopRequest: CancelRequestToJoinShopRequest(
      (b) {
        b.requestId = requestId;
      },
    ));
    return result.data?.success ?? false;
  }

  @override
  Future<bool> deleteMember({String? shopId, String? userUId}) async {
    final result = await _apiShop.getAPIShopApi().deleteUserFromShop(
        deleteUserFromShopRequest: DeleteUserFromShopRequest(
      (b) {
        b
          ..userId = userUId
          ..shopId = shopId;
      },
    ));
    return result.data?.success ?? false;
  }

  @override
  Future<bool> rejectRequestToJoinShop({String? requestId}) async {
    final result = await _apiShop.getAPIShopApi().rejectRequestToJoinShop(
        rejectRequestToJoinShopRequest: RejectRequestToJoinShopRequest(
      (b) {
        b.requestId = requestId;
      },
    ));
    return result.data?.success ?? false;
  }

  @override
  Future<MoneyModel> createPaymentRequest({
    String? shopId,
    double? amount,
    String? description,
    String? accountNo,
    required bool defaultTransfer,
    List<String>? payBoxIds = const [],
  }) async {
    final request = CreateRequestTransferRequest((builder) {
      builder.amount = amount;
      builder.desc = description;
      builder.shopId = shopId;
      builder.defaultTransfer = defaultTransfer;
      builder.payBoxIds = payBoxIds?.toBuiltList().toBuilder();
    });
    final res = await _apiShop
        .getAPIQunLGiaoDchApi()
        .requestTransfer(createRequestTransferRequest: request);
    return MoneyModel.fromCreateResponseShop(res.data?.data);
  }

  @override
  Future<ShopModel> updateShopInfo({ShopModel? shopModel, String? otp}) {
    return _apiShop.getAPIShopApi().updateShopInfo(
        updateShopInfoRequest: UpdateShopInfoRequest((b) {
      b.id = shopModel?.id;
      b.accountNo = shopModel?.accountNickname;
      b.name = shopModel?.name;
      b.owner = shopModel?.owner;
      b.address = shopModel?.address;
      b.latitudeAddress = shopModel?.latitude;
      b.longitudeAddress = shopModel?.longitude;
      b.fontImage = shopModel?.fontImage;
      b.isPushNotify = shopModel?.isPushNotify;
      b.isSmsNotify = shopModel?.isSmsNotify;
      b.isEmailNotify = shopModel?.isEmailNotify;
      b.addressDetail = shopModel?.addressDetail;
      b.defaultTransferDesc = shopModel?.defaultTransfer;
      b.nickName = shopModel?.nickName;
      b.accountNoNickname = shopModel?.accountNickname;
      b.verifySoftOtp = VerifySoftOtpRequestBuilder()..otp = otp;
      b.industryId = shopModel?.industryId;
    })).then((value) {
      final myShop = ShopModel.fromUpdateResponse(value.data?.data);
      return myShop;
    });
  }

  @override
  Future<bool> checkNickNameExist(
      {required String nickName, required String accountNo}) {
    return _apiShop
        .getAPIShopApi()
        .checkValidNickname(nickname: nickName)
        .then((value) {
      return value.data?.success ?? false;
    });
  }

  @override
  Future<bool> createRequestJoinShop({String? shopCode}) {
    return _apiShop.getAPIShopApi().createRequestToJoinShop(
        createRequestToJoinShopRequest: CreateRequestToJoinShopRequest((b) {
      b.shopCode = shopCode;
    })).then((value) {
      final myShop = ShopModel.fromRequestJoinShop(value.data?.data);
      if (myShop.id != null) {
        return true;
      }
      return false;
    });
  }

  @override
  Future<ShopModel?> createDefaultTransferDescShop(
      {String? shopId, String? defaultTransferDesc}) {
    final request = CreateDefaultTransferDescShopRequest((b) {
      b.shopId = shopId;
      b.defaultTransferDesc = defaultTransferDesc;
    });
    return _apiShop
        .getAPIShopApi()
        .createDefaultTransferDescShop(
            createDefaultTransferDescShopRequest: request)
        .then((value) {
      final shop = ShopModel.fromCreateDesResponse(value.data?.data);
      return shop;
    });
  }

  @override
  Future<bool> deleteShop({required String shopId, required String otp}) async {
    final result = await _apiShop.getAPIShopApi().deleteShop(
        deleteShopByIdRequest: DeleteShopByIdRequest(
      (b) {
        b.shopId = shopId;
        b.verifySoftOtp = VerifySoftOtpRequestBuilder()..otp = otp;
      },
    ));
    return result.data?.success ?? false;
  }

  @override
  Future<bool> checkShopName({required String shopName}) async {
    final result =
        await _apiShop.getAPIShopApi().checkShopName(shopName: shopName);
    return result.data?.success ?? false;
  }

  @override
  Future<bool> receivedRequestJoin(
      {required String shopId, required bool allow}) async {
    final result = await _apiShop.getAPIShopApi().settingAllowRequest(
        id: shopId,
        settingAllowJoinShopRequest: SettingAllowJoinShopRequest((builder) {
          builder.allowRequest = allow;
        }));
    return result.data?.success ?? false;
  }

  @override
  Future<bool> leaveShop({required String shopId}) async {
    final result = await _apiShop.getAPIShopApi().leaveShop(
        leaveShopRequest: LeaveShopRequest((builder) {
      builder.shopId = shopId;
    }));
    return result.data?.data?.leaveShop ?? false;
  }

  @override
  Future<bool> pairShop({
    required String shopId,
    required String deviceId,
    required String pairingKey,
    String? referralCode,
    String? referralBranchCode,
    String? magicPlanId,
  }) async {
    var res = await _apiShop.getAPIQunLKtNiPayboxTiMyShopApi().pair(
      pairPayBoxShopRequest: PairPayBoxShopRequest(
        (b) {
          b.shopId = shopId;
          b.deviceId = deviceId;
          b.pairingKey = pairingKey;
          b.referralCode = referralCode;
          b.referralBranchCode = referralBranchCode;
          b.magicPlanId = magicPlanId;
        },
      ),
    );
    return res.data?.success == true;
  }

  @override
  Future<bool> unPairShop(
      {required String shopId, required String deviceId}) async {
    var res = await _apiShop.getAPIQunLKtNiPayboxTiMyShopApi().unPair(
        unPairPayBoxShopRequest: UnPairPayBoxShopRequest(
      (b) {
        b.shopId = shopId;
        b.deviceId = deviceId;
      },
    ));
    return res.data?.success == true;
  }

  @override
  Future<ShopResponseModel?> getListShop2({int? page, int? pageSize}) {
    return _apiShop
        .getAPIShopApi()
        .getShopActivatedButNotConnectedPayBoxRequest(
            page: page, size: pageSize)
        .then((value) {
      return ShopResponseModel.fromResponse2(value.data?.data);
    });
  }

  @override
  Future<bool> setPayboxStatus(
      {required String shopId,
      required String deviceId,
      required bool active}) async {
    var res = await _apiShop
        .getAPIQunLKtNiPayboxTiMyShopApi()
        .settingStatusShopPaybox(
            settingStatusShopPayboxRequest: SettingStatusShopPayboxRequest(
      (b) {
        b.shopId = shopId;
        b.deviceId = deviceId;
        b.active = active;
      },
    ));
    return res.data?.success == true;
  }

  @override
  Future<ReferralModel?> getReferral({required String referralCode}) {
    return _loyaltyApi.getReferralUserApi().checkReferralCode(
      checkReferralCodeRequest: loyalty.CheckReferralCodeRequest((builder) {
        builder.code = referralCode;
      }),
    ).then((value) {
      final result = value.data?.data;
      if (result != null)
        return ReferralModel.fromResponse(result);
      else
        return null;
    });
  }

  @override
  Future<ShopModel?> getRecentShop() async {
    final result = await _apiShop.getAPIShopApi().getShopDetailRecent();
    if (result.data?.data != null) {
      return ShopModel.fromRecentResponse(result.data?.data);
    } else
      return null;
  }

  @override
  Future<PayboxInfo?> getDetailPaybox({required String deviceId}) async {
    return _apiShop
        .getAPIQunLKtNiPayboxTiMyShopApi()
        .device(deviceId: deviceId)
        .then((value) {
      final result = value.data?.data;
      if (result != null)
        return PayboxInfo.fromResponseDetail(result);
      else
        return null;
    });
  }

  @override
  Future<List<ShopEmailItem>?> getListShopEmail(
      {required String shopId, String? email}) {
    return _apiShop
        .getAPIShopApi()
        .getListEmailOfShop(shopId: shopId, email: email)
        .then((value) {
      final result = value.data?.data?.emailShops?.toList();
      if (result != null)
        return result
            .map((item) => ShopEmailItem.fromShopEmailQueryInfo(item))
            .toList();
      else
        return null;
    });
  }

  @override
  Future<ShopEmailItem?> addEmailToShop(
      {required String shopId, required String email}) {
    return _apiShop.getAPIShopApi().addEmailShop(
        addShopEmailRequest: AddShopEmailRequest((builder) {
      builder.shopId = shopId;
      builder.email = email;
    })).then((value) {
      final result = value.data?.data;
      if (result != null)
        return ShopEmailItem.fromAddShopEmailResponse(result);
      else
        return null;
    });
  }

  @override
  Future<bool?> sendTransHistoryViaEmail(
      {required String shopId,
      required List<String> listEmail,
      required String deviceName,
      required String fromDate,
      required String toDate,
      required String otp}) {
    return _apiShop
        .getAPIShopApi()
        .sendEmailHistoryTransShop(
          shopId: shopId,
          shopEmailIds: listEmail.toBuiltList(),
          deviceName: deviceName,
          fromDate: fromDate,
          toDate: toDate,
          otp: otp,
        )
        .then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  Future<Uint8List?> printHistoryTransShop({
    required String shopId,
    required String fromDate,
    required String toDate,
  }) {
    return _apiShop
        .getAPIShopApi()
        .printHistoryTransShop(
          shopId: shopId,
          fromDate: fromDate,
          toDate: toDate,
        )
        .then((value) {
      return value.data;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<List<PackagePlan>?> getPackageList() {
    return _apiShop.getAPIPackageApi().getListPackage().then((value) {
      return value.data?.data?.packages
          ?.map((p0) => PackagePlan.fromMyShopPackageData(p0))
          .toList();
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<PackageDetail?> myPackageDetail() {
    return _apiShop.getAPIPackageApi().getRegisterPackageDetail().then((value) {
      if (value.data != null && value.data!.data != null) {
        return PackageDetail.fromPackageDetail(value.data!.data!);
      }
    }).catchError((error) {
      logger.wtf(error);
      throw error;
    });
  }

  @override
  Future<PackagePlan?> packageDetailById({required String id}) {
    return _apiShop.getAPIPackageApi().getPackageById(id: id).then((value) {
      if (value.data != null && value.data!.data != null) {
        return PackagePlan.fromPackageId(value.data!.data!);
      }
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<PackageDetail?> registerPackagePlan({
    required String planId,
    required String accountNo,
    required String otp,
    String? transactionNo,
  }) {
    return _apiShop.getAPIPackageApi().registerPackages(
        registerMyShopPackageRequest: RegisterMyShopPackageRequest((builder) {
      builder.accountNo = accountNo;
      builder.planId = planId;
      builder.transactionNo = transactionNo;
      builder.verifySoftOtp = VerifySoftOtpRequest((b) {
        b.otp = otp;
      }).toBuilder();
    })).then((value) {
      if (value.data != null && value.data!.data != null) {
        return PackageDetail.fromRegisterInfo(value.data!.data!);
      }
    }).catchError((error) {
      throw error;
    });
  }

  Future<bool> deleteMyPakage() {
    return _apiShop
        .getAPIPackageApi()
        .cancelPackagesOfCurrentUser()
        .then((value) {
      return value.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<CheckCanCreateShopResponse?> checkCanUseCreateShop() {
    return _apiShop
        .getAPIShopApi()
        .checkCanCreateShop()
        .then((value) => value.data?.data)
        .catchError((error) {
      throw error;
    });
  }

  @override
  Future<CheckUserReferralData?> checkReferralCodeCreateShop(
      {required String referralCode}) {
    return _apiShop
        .getAPIUserReferralApi()
        .checkReferralCanUseCreateShop(referralCode: referralCode)
        .then((value) => value.data?.data?.data)
        .catchError((error) {
      throw error;
    });
  }

  @override
  Future<bool?> deleteEmailFromShop({required String emailId}) {
    return _apiShop.getAPIShopApi().deleteEmailShop(
        deleteShopEmailRequest: DeleteShopEmailRequest((builder) {
      builder.emailShopId = emailId;
    })).then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<List<PayboxInfo>?> getListPayBox({required String shopId}) {
    return _apiShop
        .getAPIQunLKtNiPayboxTiMyShopApi()
        .getAllPayBox(shopId: shopId)
        .then((value) {
      return value.data?.data?.payBoxs
              ?.map((e) => PayboxInfo.fromResponse(e))
              .toList() ??
          <PayboxInfo>[];
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<AccountOrganizationModel?> getOrganizationAccounts({
    SelectAccountType? accountType,
    String? onlyTTGT,
  }) {
    return _apiSmartBank
        .getAccountControllerApi()
        .getMyShopPaymentAccount(
          onlyTTGT: onlyTTGT,
          accountType: accountType?.name.toUpperCase(),
        )
        .then((value) {
      return AccountOrganizationModel.fromAccountResponseMapper(
          value.data?.data?.accounts?.toList());
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<bool?> checkUserHasOrganizeAccount() {
    return _apiShop.getAPIShopApi().checkLinkWithOrg().then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<bool?> settingTransferNotification({
    required String shopId,
    bool? isNotifyOwner = false,
    bool? isNotifyMember = false,
  }) {
    return _apiShop
        .getAPIShopApi()
        .settingNotifyTransferShop(
          settingNotifyTransferShopRequest: SettingNotifyTransferShopRequest(
            (builder) => builder
              ..shopId = shopId
              ..isNotifyOwner = isNotifyOwner
              ..isNotifyMember = isNotifyMember,
          ),
        )
        .then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<List<PackageInvoice>?> getPackagePlanInvoiceList({
    int? year,
    PackageInvoiceStatus? status,
    required int page,
    required int size,
  }) {
    return _apiShop
        .getAPIPhDchVMyShopApi()
        .getInvoices(
          year: year.toString(),
          status: status.toInvoiceDataStatusQuery(),
          page: page,
          size: size,
        )
        .then((value) {
      return value.data?.data?.content
              ?.toList()
              .map((e) => PackageInvoice.fromInvoiceData(e))
              .toList() ??
          <PackageInvoice>[];
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<PreviewPackageInvoice?> getPackagePlanInvoiceDetail({
    required String invoiceId,
  }) {
    return _apiShop
        .getAPIPhDchVMyShopApi()
        .previewInvoicePayment(invoiceId: invoiceId)
        .then((value) {
      return PreviewPackageInvoice.fromPreviewInvoiceResponse(
          value.data!.data!);
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<bool> payPackageInvoiceFee({
    required String invoiceId,
    required String otp,
    required String transactionNo,
  }) {
    return _apiShop.getAPIPhDchVMyShopApi().paymentFee(
        userExecutePaymentRequest: UserExecutePaymentRequest(
      (b) {
        b.invoiceId = invoiceId;
        b.transactionNo = transactionNo;
        b.verifySoftOtp = VerifySoftOtpRequest(
          (b) => b.otp = otp,
        ).toBuilder();
      },
    )).then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  Future<TransactionDetailModel?> getTransactionDetail(
      {required String transactionId}) {
    return _apiShop
        .getAPIQunLGiaoDchApi()
        .getTransactionDetail(
          transactionId: transactionId,
        )
        .then((value) {
      return TransactionDetailModel.fromGetDetailTransactionResponse(
          value.data?.data);
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<PackagePlanPaymentNotifyModel?> getPackagePlanNotifyPayment() {
    return _apiShop.getAPIPhDchVMyShopApi().showNotifyPayment().then((value) {
      return PackagePlanPaymentNotifyModel.fromShowNotifyPaymentResponse(
          value.data?.data);
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<bool?> disableNotifyPayment() {
    return _apiShop.getAPIPhDchVMyShopApi().offNotifyPayment().then((value) {
      return value.data?.data?.success ?? false;
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<List<IndustryModel?>?> getListBusiness() async {
    return _apiShop.getAPIIndustryApi().getListIndustry().then((value) {
      return value.data?.data?.industries
          ?.map((e) => IndustryModel().fromIndustryResponse(e))
          .toList();
    });
  }

  @override
  Future<smartBank.CheckCashLimitationResponse?> callReviewTransactionApi({
    required double amount,
    required smartBank.TransactionCategoriesType transactionCategoriesType,
  }) async {
    final response = await _apiSmartBank
        .getCashLimitationControllerApi()
        .checkCashLimitationVer2(
      checkCashLimitationVers2Request:
          smartBank.CheckCashLimitationVers2Request(
        (b) {
          b
            ..amount = amount
            ..transactionCategoriesType = transactionCategoriesType;
        },
      ),
    );
    return response.data?.data;
  }

  // Magic Paybox
  @override
  Future<List<MagicPackage>?> getMagicPackageListByDeviceId(
      {required String id}) async {
    return _apiShop
        .getAPIMagicPayboxApi()
        .getMagicPackageList(payboxModelId: id)
        .then((value) {
      return value.data?.data?.magicPackages
          ?.map((e) => MagicPackage.fromResponse(e))
          .toList();
    });
  }

  @override
  Future<void> getPayboxTypeList() async {
    return _apiShop
        .getAPIQunLKtNiPayboxTiMyShopApi()
        .getPayboxTypeList()
        .then((value) => {});
  }

  @override
  Future<ListAllPayboxModelResponse?> getPayboxModelList() async {
    return _apiShop.getAPIQunLKtNiPayboxTiMyShopApi().getPayboxModelList().then(
        (value) => ListAllPayboxModelResponse.fromResponse(value.data?.data));
  }

  @override
  Future<PackageDetail?> registerMagicPackage({
    required String planId,
    required String accountNo,
    required String otp,
    String? transactionNo,
    String? magicPackageId,
    String? deviceModel,
    String? modelType,
  }) {
    return _apiShop.getAPIPhDchVMyShopApi().paymentFeeRegister(
        userExecutePaymentResgisterRequest:
            UserExecutePaymentResgisterRequest((builder) {
      builder.accountNo = accountNo;
      builder.planId = planId;
      builder.transactionNo = transactionNo;
      builder.verifySoftOtp = VerifySoftOtpRequest((b) {
        b.otp = otp;
      }).toBuilder();
      builder.magicPackageId = magicPackageId ?? '';
      builder.model = deviceModel ?? '';
      builder.modelType = modelType ?? '';
    })).then((value) {
      if (value.data != null && value.data!.data != null) {
        return PackageDetail.fromMagicRegisterResponse(value.data!.data!);
      }
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<ListRegisteredMagicPayboxResponse?> getListRegisteredMagicPaybox({
    required String planId,
    required String status,
  }) {
    return _apiShop
        .getAPIMagicPayboxApi()
        .getRegisterPackageMagicPayboxList(userPlanId: planId, status: status)
        .then((value) {
      return ListRegisteredMagicPayboxResponse.fromResponse(value.data?.data);
    });
  }

  @override
  Future<bool> checkCancelOrChangePackage(
      {String? registeredMagicPackageId, String? planId}) {
    return _apiShop
        .getAPIMagicPayboxApi()
        .checkCancelOrChange(
            magicPlanId: registeredMagicPackageId, planId: planId)
        .then((value) => value.data?.data?.success ?? false);
  }

  @override
  Future<RegisteredMagicPaybox?> cancelMagicPaybox(
      {required String registeredMagicPackageId, required String otp}) {
    return _apiShop.getAPIShopApi().cancelRequestMagicPaybox(
        cancelRequestMagicPayboxRequest:
            CancelRequestMagicPayboxRequest((builder) {
      builder.magicPayboxUserPlanId = registeredMagicPackageId;
      builder.verifySoftOtp = VerifySoftOtpRequest((b) {
        b.otp = otp;
      }).toBuilder();
    })).then((value) {
      if (value.data != null && value.data!.data != null) {
        return RegisteredMagicPaybox.fromCancelResponse(value.data!.data!);
      }
    }).catchError((error) {
      throw error;
    });
  }

  @override
  Future<CheckDevicePurchasedResponseModel?> isHiredOrBoughtDevice(
      {required String serialNumber, required String model}) async {
    return _apiShop
        .getAPIQunLKtNiPayboxTiMyShopApi()
        .checkDevice(serialNumber: serialNumber, model: model)
        .then((value) {
      return CheckDevicePurchasedResponseModel.fromResponse(value.data?.data);
    });
  }

  @override
  Future<ChangeMagicPackageResponse?> changeMagicPackage({
    String? accountNo,
    String? registeredMagicPackageId,
    String? newMagicPackageId,
    required String otp,
  }) {
    return _apiShop
        .getAPIThcHinVicNgKGiCcMyShopMagicPayboxApi()
        .changeMagicPayboxPackage(changeMagicPayboxPackageRequest:
            ChangeMagicPayboxPackageRequest((builder) {
      builder.accountNo = accountNo;
      builder.magicPayboxPackageId = newMagicPackageId;
      builder.magicPayboxUserPlanId = registeredMagicPackageId;
      builder.verifySoftOtp = VerifySoftOtpRequest((b) {
        b.otp = otp;
      }).toBuilder();
    })).then((value) {
      if (value.data != null && value.data!.data != null) {
        return ChangeMagicPackageResponse.fromResponse(value.data!.data!);
      }
    }).catchError((error) {
      throw error;
    });
  }
}
