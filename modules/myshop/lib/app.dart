import 'package:flutter/material.dart';
import 'package:nested/nested.dart';
import 'di/bloc_provider_injector.dart';
import 'di/handlers/event_notification_handler.dart';
import 'di/handlers/myshop_notification_handler.dart';
import 'di/provider_injector.dart';
import 'di/screen_util_wrapper.dart';
import 'di/unfocus_handler.dart';
import 'ui/main/myshop.dart';
import 'utils/navigator.dart';

class KLBMyShopConfig {
  KLBMyShopConfig({
    required this.routeName,
    required this.phoneNumber,
    this.redirectUri,
  });

  final String phoneNumber;
  final Uri? redirectUri;
  final String routeName;
}

class KLBMyShopModule extends StatefulWidget {
  const KLBMyShopModule({
    Key? key,
    required this.config,
  }) : super(key: key);

  final KLBMyShopConfig config;

  @override
  State<KLBMyShopModule> createState() => _KLBMyShopModuleState();
}

class _KLBMyShopModuleState extends State<KLBMyShopModule> {
  final GlobalKey<NavigatorState> myShopNavigatorKey =
      GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Nested(
      children: [
        KLBMyShopRepositoryProvider(config: widget.config),
        KLBMyShopBlocProvider(config: widget.config),
        UnfocusHandler(),
        ScreenUtilWrapper(),
      ],
      child: WillPopScope(
        onWillPop: () async {
          if (myShopNavigatorKey.currentState?.canPop() == true) {
            myShopNavigatorKey.currentState!.maybePop();
            return false;
          }
          return true;
        },
        child: Navigator(
          key: myShopNavigatorKey,
          onGenerateRoute: (settings) {
            return MaterialPageRoute(
              settings: RouteSettings(name: widget.config.routeName),
              builder: (context) => WillPopScope(
                onWillPop: () async => false,
                child: Nested(children: [
                  // Handlers
                  EventNotificationHandler(),
                  MyShopNotificationHandler()
                ], child: MyShop()),
              ),
            );
          },
          onPopPage: (route, result) => route.didPop(result),
        ),
      ),
    );
  }
}
