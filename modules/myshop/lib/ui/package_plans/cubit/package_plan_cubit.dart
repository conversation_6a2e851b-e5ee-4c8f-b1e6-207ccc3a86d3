import 'dart:async';

import 'package:bloc_notification/bloc_notification.dart';
import 'package:common/global_callback.dart';
import 'package:equatable/equatable.dart';
import 'package:ksb_common/model/base_response.dart';
import 'package:meta/meta.dart';
import 'package:umee_shop/di/injection.dart';
import 'package:umee_shop/repository/src/model/magic_paybox_responses.dart';
import 'package:umee_shop/repository/src/model/package_plan/package_plan_model.dart';
import 'package:umee_shop/repository/src/myshop_preferences.dart';
import 'package:umee_shop/repository/umee_shop_repository.dart';

import '../../../../shared/cubits/base/base_cubit.dart';

part 'package_plan_notification.dart';
part 'package_plan_state.dart';

class PackagePlanCubit extends BaseCubit<PackagePlanState>
    with BlocNotificationMixin<PackagePlanState, PackagePlanNotification> {
  PackagePlanCubit({required this.repository})
      : super(PackagePlanInitial(), repository);

  final MyShopRepository repository;

  Future<List<PackagePlan>?> getPackageList() async {
    emit(PackagePlanLoading());
    return repository.getPackageList().then((value) {
      if (value != null) {
        emit(PackagePlanSuccess(listPackage: value));
        return value;
      }
    }).catchError((e) async {
      final error = await handleApiError(e);
      if (error != null) {
        emit(PackagePlanFailed(error: error));
      }
    });
  }

  Future<PackagePlan?> packageDetailById({
    required String id,
  }) {
    return repository.packageDetailById(id: id).then((value) {
      if (value != null) {
        return value;
      }
    }).catchError((e) async {
      final error = await handleApiError(e);
      if (error != null) {
        throw error;
      }
    });
  }

  Future<void> registerPackagePlan({
    required String planId,
    required String accountNo,
    String? transactionNo,
    String? magicPackageId,
    String? deviceModel,
    String? modelType,
  }) async {
    try {
      final eToken = await GlobalCallback.instance.openOtp?.call();
      if (eToken != null && eToken.isNotEmpty) {
        var result = await repository.registerMagicPackage(
          planId: planId,
          accountNo: accountNo,
          otp: eToken,
          transactionNo: transactionNo,
          magicPackageId: magicPackageId,
          deviceModel: deviceModel,
          modelType: modelType,
        );

        if (result != null) {
          notify(RegisterPackagePlanSuccessNotification(packageDetail: result));
        }
      }
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(RegisterPackagePlanFailedNotification(failure: error));
      }
    }
  }

  Future<void> changeMagicPackage({
    String? accountNo,
    String? registeredMagicPackageId,
    String? newMagicPackageId,
  }) async {
    try {
      final eToken = await GlobalCallback.instance.openOtp?.call();
      if (eToken != null && eToken.isNotEmpty) {
        var result = await repository.changeMagicPackage(
          accountNo: accountNo,
          registeredMagicPackageId: registeredMagicPackageId,
          newMagicPackageId: newMagicPackageId,
          otp: eToken,
        );

        if (result != null) {
          notify(ChangeMagicPackageSuccessNotification(response: result));
        }
      }
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(RegisterPackagePlanFailedNotification(failure: error));
      }
    }
  }
}

class MyPackagePlanCubit extends BaseCubit<MyPackagePlanState>
    with BlocNotificationMixin<MyPackagePlanState, PackagePlanNotification> {
  MyPackagePlanCubit({required this.repository})
      : super(MyPackagePlanInitial(), repository);

  final MyShopRepository repository;

  PackageDetail? _myPackagePlan;

  PackageDetail? get myPackagePlan => _myPackagePlan;

  MyShopPreferences _preferences = getIt<MyShopPreferences>();

  void closeNoneMagic() {
    if (state is MyPackagePlanSuccess) {
      _preferences.setIsClosedNewMagic(myPackagePlan?.cifNo ?? 'noneCif', true);
      emit(MyPackagePlanSuccess(
        myPackage: (state as MyPackagePlanSuccess).myPackage,
        isClosedNewMagic: true,
      ));
    }
  }

  Future<PackageDetail?> myPackageDetail({bool? showLoading = false}) async {
    try {
      if (showLoading == true) {
        emit(MyPackagePlanLoading());
      }
      final result = await repository.myPackageDetail();
      if (result != null) {
        emit(MyPackagePlanSuccess(
          myPackage: result,
          isClosedNewMagic: _preferences.isClosedNewMagic(result.cifNo),
        ));
        _myPackagePlan = result;
        return result;
      }
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        handlePackagePlanError(error);
      }
    }
    return null;
  }

  void handlePackagePlanError(BaseResponseModel failure) {
    if (failure.statusCode == 'SHOP2001') {
      notify(PackagePlanNotRegisterNotification());
    }
    emit(MyPackagePlanFailure(error: failure));
  }

  Future<bool> isBlockedUnpaid({String? planId}) async {
    try {
      // if value != true => error
      return await repository
          .checkCancelOrChangePackage(planId: planId)
          .then((value) => value);
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(RegisterPackagePlanFailedNotification(failure: error));
      }
      return false;
    }
  }

  Future<bool> deleteMyPakage() async {
    return repository.deleteMyPakage().then((value) {
      return value;
    }).catchError((e) async {
      final error = await handleApiError(e);
      if (error != null) {
        notify(RegisterPackagePlanFailedNotification(failure: error));
      }
    });
  }
}
