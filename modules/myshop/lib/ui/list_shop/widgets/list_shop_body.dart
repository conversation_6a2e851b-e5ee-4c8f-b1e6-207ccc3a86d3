import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:ksb_common/shared/assets.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:umee_shop/generated/l10n.dart';
import 'package:umee_shop/shared/cubits/myshop/my_shop_cubit.dart';
import 'package:umee_shop/shared/widgets/list_shop/controller/list_shop_controller.dart';
import 'package:umee_shop/shared/widgets/list_shop/list_shop.dart';
import 'package:umee_shop/shared/widgets/package_invoice_payment_notification/package_invoice_payment_notification_widget.dart';
import 'package:umee_shop/shared/widgets/remove_scroll_glow/remove_scroll_glow.dart';
import 'package:umee_shop/ui/list_shop/widgets/my_magic_package_info.dart';
import 'package:umee_shop/ui/list_shop/widgets/my_package_plan_info.dart';
import 'package:umee_shop/ui/magic_paybox/pages/list_models_selection_page/list_models_selection_page.dart';
import 'package:umee_shop/ui/package_plans/cubit/package_plan_cubit.dart';
import 'package:umee_shop/ui/package_plans/views/package_plans_list_page.dart';

import '../../../repository/src/model/index.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../../request_join_shop/request_join_shop_page.dart';

const _kStringMagicPb = 'Magic Paybox';

class ListShopBody extends StatefulWidget {
  const ListShopBody({
    Key? key,
    this.onItemShopTab,
    this.showCheckCurrentShop,
  }) : super(key: key);

  final Function(ShopModel)? onItemShopTab;
  final bool Function(ShopModel)? showCheckCurrentShop;

  @override
  State<ListShopBody> createState() => _ListShopBodyState();
}

class _ListShopBodyState extends State<ListShopBody> {
  late MyPackagePlanInfoController packagePlanController;
  late ListShopWidgetController listShopController;

  @override
  void initState() {
    super.initState();
    packagePlanController = MyPackagePlanInfoController(
        myPackagePlanCubit: context.read<MyPackagePlanCubit>());
    listShopController = ListShopWidgetController();
  }

  SpeedDialChild _speedDialItem(BuildContext context,
      {String? title, required String icon, Function()? onTap}) {
    return SpeedDialChild(
      elevation: 3.0,
      onTap: onTap,
      labelWidget: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(40 / 2),
        ),
        width: 181.w,
        height: 40.h,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageAssets.svgAssets(icon,
                  width: 24, height: 24, color: Theme.of(context).primaryColor),
              SizedBox(width: 10),
              Text(
                title ?? '',
                style: StyleApp.subtitle1(context, true)?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).primaryColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
      child: null,
    );
  }

  Widget _floatingBtn(BuildContext context) {
    return SpeedDial(
      icon: Icons.add,
      overlayColor: Theme.of(context).primaryColor,
      overlayOpacity: 0.2,
      activeIcon: Icons.close,
      spaceBetweenChildren: 0,
      elevation: 3.0,
      isOpenOnStart: false,
      children: [
        _speedDialItem(
          context,
          title: _kStringMagicPb,
          icon: ImageAssets.ic_magicpb_blue,
          onTap: () => go(context, ListModelsSelectionPage()),
        ),
        _speedDialItem(context,
            title: S.of(context).create_shop,
            icon: ImageAssets.ic_plus_blue,
            onTap: () async => handleCreateShop(context)),
        _speedDialItem(
          context,
          title: S.of(context).input_shop,
          icon: ImageAssets.ic_edit_blue,
          onTap: () => go(context, RequestJoinShopPage()),
        ),
      ],
    );
  }

  void handleCreateShop(BuildContext context) async {
    final userHasPackagePlan =
        context.read<MyPackagePlanCubit>().myPackagePlan != null;
    if (userHasPackagePlan) {
      await LoadingOverlay.of(context)
          .during(future: context.read<MyShopCubit>().checkCanCreateShop());
    } else {
      await DialogUtil.confirm(context,
          Text('Quý khách chưa đăng ký gói. Vui lòng đăng ký gói để tạo shop'),
          submitText: 'Đăng ký gói',
          onSubmit: () => go(context, ShopPlansListPage()));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: DefaultTabController(
        length: 1,
        child: RefreshIndicator(
            notificationPredicate: (notification) {
              if (notification is OverscrollNotification || Platform.isIOS) {
                return notification.depth == 2;
              }
              return notification.depth == 0;
            },
            onRefresh: () async {
              packagePlanController.refresh(showLoading: true);
              listShopController.refresh();
            },
            child: NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    SliverPadding(
                      padding: EdgeInsets.only(bottom: kInnerPadding),
                      sliver: SliverToBoxAdapter(
                        child: PackagePlanPaymentNotificationWidget(),
                      ),
                    ),
                    if (!isClosedNoneMagic())
                      SliverPadding(
                        padding: EdgeInsets.only(bottom: kInnerPadding),
                        sliver: SliverToBoxAdapter(
                          child: _buildNoneMagic(),
                        ),
                      ),
                    SliverPadding(
                      padding: EdgeInsets.only(bottom: kSmallPadding),
                      sliver: SliverToBoxAdapter(
                        child: MyPackagePlanInfo(
                          controller: packagePlanController,
                        ),
                      ),
                    ),
                    if (packagePlanController
                            .myPackagePlan?.registerMagicPlan ==
                        true)
                      SliverPadding(
                        padding: EdgeInsets.only(bottom: kSmallPadding),
                        sliver: SliverToBoxAdapter(
                          child: MyMagicPackageInfo(),
                        ),
                      ),
                  ];
                },
                body: TabBarView(children: [
                  RemoveScrollGlow(
                    child: ListShopWidget(
                        controller: listShopController,
                        onItemShopTab: widget.onItemShopTab,
                        useRefreshIndicator: false,
                        showCheckCurrentShop: widget.showCheckCurrentShop,
                        shopPagingProvider: ({required pageNumber, pageSize}) =>
                            context
                                .read<MyShopCubit>()
                                .getListShopPaging(pageNumber: pageNumber)),
                  ),
                ]))),
      ),
      floatingActionButton: _floatingBtn(context),
    );
  }

  bool isClosedNoneMagic() {
    final state = context.watch<MyPackagePlanCubit>().state;
    bool result = true;
    if (state is MyPackagePlanSuccess) {
      result = state.isClosedNewMagic || state.myPackage.registerMagicPlan;
    }
    return result;
  }

  Widget _buildNoneMagic() {
    return Stack(
      alignment: Alignment.topRight,
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: kHorizontalPaddingStandard,
          padding: kInnerPaddingStandard.copyWith(left: kSmallPadding.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(kRadiusButton.r),
            color: DynamicTheme.of(context)?.customColor.buttonTransparentColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ImageAssets.svgAssets(
                    ImageAssets.ic_no_magic,
                    width: 48.r,
                  ),
                  SizedBox(width: kSmallPadding.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Magic Paybox đã ra mắt',
                          style: StyleApp.subtitle1(context, true)?.copyWith(
                            color:
                                DynamicTheme.of(context)?.customColor.lightBlue,
                          ),
                        ),
                        SizedBox(height: kTinyPadding.h),
                        Text(
                          'Magic Paybox cung cấp phương thức thanh toán tiện lợi và tối ưu hóa quy trình bán hàng cho chủ shop',
                          style: StyleApp.bodyText2(context)?.copyWith(
                            color: DynamicTheme.of(context)
                                ?.customColor
                                .captionColor,
                          ),
                          textAlign: TextAlign.justify,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: kSmallPadding.h),
              Row(
                children: [
                  SizedBox(width: 48.r + kSmallPadding.r),
                  InkWell(
                    child: Text(
                      'Đăng ký ngay',
                      style: StyleApp.bodyText2(context)
                          ?.copyWith(color: Color(0xFF228BCC)),
                    ),
                    onTap: () => go(context, ListModelsSelectionPage()),
                  ),
                ],
              ),
            ],
          ),
        ),
        Positioned(
            top: -8,
            right: 8,
            child: InkWell(
              onTap: () => context.read<MyPackagePlanCubit>().closeNoneMagic(),
              child: Container(
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(kRadiusStadiumButton)),
                child: Icon(Icons.close, color: Colors.white, size: 18),
              ),
            ))
      ],
    );
  }
}
