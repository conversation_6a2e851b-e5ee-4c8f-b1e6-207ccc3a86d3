// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "active": MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
        "add_nick_name":
            MessageLookupByLibrary.simpleMessage("Đăng ký tài khoản nickname"),
        "address": MessageLookupByLibrary.simpleMessage("Địa chỉ"),
        "are_you_sure_delete_shop": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn xóa shop?"),
        "are_you_sure_to_leave": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn rời shop?"),
        "block_unpaid": MessageLookupByLibrary.simpleMessage("Khoá do nợ phí"),
        "cancel_service": MessageLookupByLibrary.simpleMessage("Huỷ dịch vụ"),
        "cancellation_date":
            MessageLookupByLibrary.simpleMessage("Ngày huỷ dịch vụ"),
        "change_term": MessageLookupByLibrary.simpleMessage("Thay đổi kỳ hạn"),
        "choose_industry":
            MessageLookupByLibrary.simpleMessage("Chọn ngành nghề kinh doanh"),
        "common_back": MessageLookupByLibrary.simpleMessage("Thoát"),
        "common_canceled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
        "common_detail": MessageLookupByLibrary.simpleMessage("Chi tiết"),
        "common_free": MessageLookupByLibrary.simpleMessage("Miễn phí"),
        "common_in_use": MessageLookupByLibrary.simpleMessage("Đang sử dụng"),
        "common_register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "complete": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "config_notification":
            MessageLookupByLibrary.simpleMessage("Cấu hình thông báo"),
        "confirm_cancellation":
            MessageLookupByLibrary.simpleMessage("Xác nhận hủy"),
        "content_input_id_shop": MessageLookupByLibrary.simpleMessage(
            "Nhập ID của Shop để yêu cầu tham gia shop,\nID Shop sẽ do người quản lý cung cấp."),
        "continue_creating_shop":
            MessageLookupByLibrary.simpleMessage("Tiếp tục tạo shop"),
        "continue_text": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "copy_successfully":
            MessageLookupByLibrary.simpleMessage("Sao chép thành công"),
        "create_account_nickname_shop":
            MessageLookupByLibrary.simpleMessage("Mở tài khoản nickname shop"),
        "create_payment_request":
            MessageLookupByLibrary.simpleMessage("Tạo yêu cầu thanh toán"),
        "create_shop": MessageLookupByLibrary.simpleMessage("Mở thêm shop"),
        "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
        "delete_shop": MessageLookupByLibrary.simpleMessage("Xóa shop"),
        "device_id": MessageLookupByLibrary.simpleMessage("Mã thiết bị"),
        "device_type": MessageLookupByLibrary.simpleMessage("Loại thiết bị"),
        "edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "edit_content_transfer_success": MessageLookupByLibrary.simpleMessage(
            "Chỉnh sửa nội dung chuyển tiền thành công"),
        "edit_fix": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "edit_nickname_shop":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa nickname shop"),
        "edit_shop_info":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa thông tin shop"),
        "edit_shop_name":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa tên shop"),
        "enter_info_shop":
            MessageLookupByLibrary.simpleMessage("Điền thông tin shop"),
        "hint_id_shop": MessageLookupByLibrary.simpleMessage("ID Shop"),
        "history": MessageLookupByLibrary.simpleMessage("History"),
        "history_transaction":
            MessageLookupByLibrary.simpleMessage("Lịch sử giao dịch"),
        "image_shop_violation": MessageLookupByLibrary.simpleMessage(
            "Hình ảnh shop có dấu hiệu vi phạm"),
        "industry":
            MessageLookupByLibrary.simpleMessage("Ngành nghề kinh doanh"),
        "info_shop_violation": MessageLookupByLibrary.simpleMessage(
            "Thông tin shop của quý khách đã vi phạm chính sách của KienlongBank"),
        "information": MessageLookupByLibrary.simpleMessage("Thông tin"),
        "input_id_shop": MessageLookupByLibrary.simpleMessage("Nhập ID Shop"),
        "input_shop": MessageLookupByLibrary.simpleMessage("Nhập mã shop"),
        "join_shop": MessageLookupByLibrary.simpleMessage("Nhập mã shop"),
        "leave": MessageLookupByLibrary.simpleMessage("Rời"),
        "leave_shop": MessageLookupByLibrary.simpleMessage("Rời shop"),
        "linking_success":
            MessageLookupByLibrary.simpleMessage("Liên kết thành công"),
        "linking_success_describe": MessageLookupByLibrary.simpleMessage(
            "Chúc mừng, quý khách đã liên kết PAYBOX thành công."),
        "list_of_member":
            MessageLookupByLibrary.simpleMessage("Danh sách thành viên"),
        "list_of_violations":
            MessageLookupByLibrary.simpleMessage("Danh sách vi phạm"),
        "list_shop": MessageLookupByLibrary.simpleMessage("Danh sách shop"),
        "locked": MessageLookupByLibrary.simpleMessage("Đã khóa"),
        "locked_unpaid_fees":
            MessageLookupByLibrary.simpleMessage("Khóa do nợ phí"),
        "magic_package_detail":
            MessageLookupByLibrary.simpleMessage("Thông tin gói"),
        "magic_paybox": MessageLookupByLibrary.simpleMessage("Magic Paybox"),
        "member": MessageLookupByLibrary.simpleMessage("Thành viên"),
        "my_shop_faq": MessageLookupByLibrary.simpleMessage("My Shop là gì?"),
        "myshop_back_to_home":
            MessageLookupByLibrary.simpleMessage("Về trang chủ"),
        "myshop_choose_service":
            MessageLookupByLibrary.simpleMessage("Chọn dịch vụ này"),
        "myshop_plan_package":
            MessageLookupByLibrary.simpleMessage("Gói cước Myshop"),
        "name_of_the_unit_managing_the_referrer":
            MessageLookupByLibrary.simpleMessage(
                "Tên đơn vị quản lý người giới thiệu"),
        "next": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "next_payment_date":
            MessageLookupByLibrary.simpleMessage("Ngày thanh toán kế tiếp"),
        "nickname_shop": MessageLookupByLibrary.simpleMessage("Nickname shop"),
        "nickname_violation": MessageLookupByLibrary.simpleMessage(
            "Nickname shop có dấu hiệu vi phạm"),
        "no_services_available":
            MessageLookupByLibrary.simpleMessage("Không có dịch vụ nào"),
        "not_link_paybox":
            MessageLookupByLibrary.simpleMessage("Chưa liên kết PAYBOX"),
        "not_link_paybox_describe": MessageLookupByLibrary.simpleMessage(
            "Liên kết PAYBOX giúp cho việc thanh toán thuận tiện, đơn giản và chuyên nghiệp hơn bao giờ hết."),
        "operation_status":
            MessageLookupByLibrary.simpleMessage("Trạng thái hoạt động"),
        "owner_name": MessageLookupByLibrary.simpleMessage("Chủ cửa hàng"),
        "owner_shop": MessageLookupByLibrary.simpleMessage("Chủ cửa hàng"),
        "package_fee": MessageLookupByLibrary.simpleMessage("Cước phí"),
        "pairing_paybox":
            MessageLookupByLibrary.simpleMessage("Liên kết Paybox"),
        "pairing_paybox_title":
            MessageLookupByLibrary.simpleMessage("Liên kết PAYBOX"),
        "pay_now": MessageLookupByLibrary.simpleMessage("Thanh toán ngay"),
        "paybox_faq_msg_1": MessageLookupByLibrary.simpleMessage(
            "Tiết kiệm thời gian thanh toán, nâng cao trải nghiệm khách hàng."),
        "paybox_faq_msg_2": MessageLookupByLibrary.simpleMessage(
            "Thiết bị liên kết trực tiếp với MyShop, giải quyết bài toán thanh toán qua VietQR"),
        "paybox_faq_msg_3": MessageLookupByLibrary.simpleMessage(
            "Thiết bị cảm ứng hỗ trợ đa phương thức thanh toán nhiều loại thẻ và QR Code các ngân hàng/ví điện tử thông dụng"),
        "paybox_faq_title_1": MessageLookupByLibrary.simpleMessage(
            "Giải pháp thanh toán tiện lợi dành cho mọi cửa hàng"),
        "paybox_faq_title_2":
            MessageLookupByLibrary.simpleMessage("Paybox Lite"),
        "paybox_faq_title_3":
            MessageLookupByLibrary.simpleMessage("Paybox Mini"),
        "payment_cycle": MessageLookupByLibrary.simpleMessage("Kỳ thanh toán"),
        "payment_cycle_changed__successfully":
            MessageLookupByLibrary.simpleMessage("Đổi kỳ hạn thành công"),
        "payment_cycle_information":
            MessageLookupByLibrary.simpleMessage("Thông tin kỳ hạn thanh toán"),
        "payment_cycle_title_section":
            MessageLookupByLibrary.simpleMessage("Kỳ hạn thanh toán"),
        "payment_date": MessageLookupByLibrary.simpleMessage("Ngày thanh toán"),
        "please_edit_the_violating": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chỉnh sửa lại những thông tin vi phạm dưới đây để kích hoạt và sử dụng shop."),
        "position_at_shop":
            MessageLookupByLibrary.simpleMessage("Vai trò của quý khách là?"),
        "qr_code_unknown_message": MessageLookupByLibrary.simpleMessage(
            "Thật ngại quá! Dường như KienlongBank không thể đọc nội dung QR Code."),
        "receive_join_shop":
            MessageLookupByLibrary.simpleMessage("Nhận yêu cầu tham gia shop"),
        "receive_join_shop_content": MessageLookupByLibrary.simpleMessage(
            "Cho phép quý khách nhận các yêu cầu tham gia shop từ mọi người"),
        "register_magic_paybox":
            MessageLookupByLibrary.simpleMessage("Đăng ký Magic Paybox"),
        "register_open_shop":
            MessageLookupByLibrary.simpleMessage("Đăng ký mở shop"),
        "register_shop":
            MessageLookupByLibrary.simpleMessage("Đăng ký mở shop"),
        "registered_services":
            MessageLookupByLibrary.simpleMessage("Dịch vụ đã đăng ký"),
        "registration_date":
            MessageLookupByLibrary.simpleMessage("Ngày đăng ký"),
        "registration_successful":
            MessageLookupByLibrary.simpleMessage("Đăng ký thành công"),
        "request_join_shop":
            MessageLookupByLibrary.simpleMessage("Yêu cầu tham gia"),
        "request_shop":
            MessageLookupByLibrary.simpleMessage("Yêu cầu tham gia shop"),
        "request_tranfer":
            MessageLookupByLibrary.simpleMessage("Tạo yêu cầu thanh toán"),
        "required_register_shop_message": MessageLookupByLibrary.simpleMessage(
            "Mỗi shop chỉ được đăng ký 1 nickname shop, quý khách vui lòng kiểm tra kỹ trước khi xác nhận"),
        "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "see_all": MessageLookupByLibrary.simpleMessage("Xem tất cả"),
        "selecting_service_instruction":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn 01 dịch vụ."),
        "service_canceled_successfully":
            MessageLookupByLibrary.simpleMessage("Hủy dịch vụ thành công"),
        "service_disabled":
            MessageLookupByLibrary.simpleMessage("Vô hiệu hóa dịch vụ"),
        "service_information":
            MessageLookupByLibrary.simpleMessage("Thông tin dịch vụ"),
        "service_package": MessageLookupByLibrary.simpleMessage("Gói đăng ký"),
        "setup_content_tranfer": MessageLookupByLibrary.simpleMessage(
            "Cài đặt nội dung chuyển tiền"),
        "setup_noti_share":
            MessageLookupByLibrary.simpleMessage("Cài đặt chia sẻ thông báo"),
        "shop_account_holder":
            MessageLookupByLibrary.simpleMessage("Chủ tài khoản"),
        "shop_add": MessageLookupByLibrary.simpleMessage("Thêm mới"),
        "shop_amount": MessageLookupByLibrary.simpleMessage("Số tiền"),
        "shop_back": MessageLookupByLibrary.simpleMessage("Quay lại"),
        "shop_confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "shop_confirm_info":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin"),
        "shop_content": MessageLookupByLibrary.simpleMessage("Nội dung"),
        "shop_create_payment_request_success":
            MessageLookupByLibrary.simpleMessage(
                "Tạo yêu cầu chuyển tiền thành công"),
        "shop_disable_status":
            MessageLookupByLibrary.simpleMessage("Vô hiệu hóa"),
        "shop_enter_content":
            MessageLookupByLibrary.simpleMessage("Nhập nội dung"),
        "shop_enter_referral_code_message": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu sẽ giúp quý khách đảm bảo quyền lợi khi mở shop tại KienlongBank."),
        "shop_enter_referral_code_tool_tip": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi khi mở shop."),
        "shop_enter_referral_code_with_paybox_tool_tip":
            MessageLookupByLibrary.simpleMessage(
                "Nhập mã giới thiệu sẽ giúp khách hàng đảm bảo quyền lợi khi liên kết Paybox với Myshop của KienlongBank."),
        "shop_id_exists_please_choose_another_id":
            MessageLookupByLibrary.simpleMessage(
                "ID Shop này đã tồn tại trên hệ thống, vui lòng chọn ID Shop khác."),
        "shop_info": MessageLookupByLibrary.simpleMessage("Thông tin shop"),
        "shop_link_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên kết shop"),
        "shop_name": MessageLookupByLibrary.simpleMessage("Tên cửa hàng"),
        "shop_name_violation": MessageLookupByLibrary.simpleMessage(
            "Tên shop có dấu hiệu vi phạm"),
        "shop_no_transaction_yet":
            MessageLookupByLibrary.simpleMessage("Chưa có giao dịch"),
        "shop_payment_transactions_displayed_here":
            MessageLookupByLibrary.simpleMessage(
                "Các giao dịch thanh toán của shop sẽ hiển thị tại đây."),
        "shop_referer":
            MessageLookupByLibrary.simpleMessage("Người giới thiệu"),
        "shop_referer_if_any":
            MessageLookupByLibrary.simpleMessage("Người giới thiệu (nếu có)"),
        "shop_register_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản đăng ký"),
        "shop_save": MessageLookupByLibrary.simpleMessage("Lưu"),
        "shop_save_qr": MessageLookupByLibrary.simpleMessage("Lưu mã"),
        "shop_settings_share_notification":
            MessageLookupByLibrary.simpleMessage("Cài đặt chia sẻ thông báo"),
        "shop_share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "shop_source_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nguồn"),
        "shop_transfer_content":
            MessageLookupByLibrary.simpleMessage("Nội dung chuyển tiền"),
        "shop_waiting_status":
            MessageLookupByLibrary.simpleMessage("Chờ duyệt"),
        "shutdown_status":
            MessageLookupByLibrary.simpleMessage("Ngưng hoạt động"),
        "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "subscribe_now": MessageLookupByLibrary.simpleMessage("Đăng ký ngay"),
        "sure_want_to_enable_receiving_requests_join_shop":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có chắc chắn muốn bật nhận yêu cầu tham gia shop?"),
        "terms_and_conditions":
            MessageLookupByLibrary.simpleMessage("Điều khoản và điều kiện"),
        "title_input_id_shop":
            MessageLookupByLibrary.simpleMessage("Nhập ID Shop để tiếp tục"),
        "total_due": MessageLookupByLibrary.simpleMessage("Tổng thu"),
        "transaction": MessageLookupByLibrary.simpleMessage("Giao dịch"),
        "transaction_fee":
            MessageLookupByLibrary.simpleMessage("Phí giao dịch"),
        "turn_off_paybox":
            MessageLookupByLibrary.simpleMessage("Tắt hoạt động PAYBOX"),
        "turn_off_paybox_describe": MessageLookupByLibrary.simpleMessage(
            "PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn tắt?"),
        "understood": MessageLookupByLibrary.simpleMessage("Đã hiểu"),
        "unit_month": MessageLookupByLibrary.simpleMessage("tháng"),
        "unlink_paybox":
            MessageLookupByLibrary.simpleMessage("Huỷ liên kết PAYBOX"),
        "unlink_paybox_describe": MessageLookupByLibrary.simpleMessage(
            "PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn huỷ liên kết?"),
        "using_paybox_describe": MessageLookupByLibrary.simpleMessage(
            "Sản phẩm PAYBOX đến từ Unicloud cung cấp giải pháp thanh toán an toàn, hiện đại và tiện lợi."),
        "using_paybox_title": MessageLookupByLibrary.simpleMessage(
            "Sử dụng PAYBOX, thanh toán tiện lợi"),
        "valid_input_id_shop":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập ID Shop")
      };
}
