// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Tiếp tục`
  String get next {
    return Intl.message(
      'Tiếp tục',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký mở shop`
  String get register_open_shop {
    return Intl.message(
      'Đăng ký mở shop',
      name: 'register_open_shop',
      desc: '',
      args: [],
    );
  }

  /// `Vai trò của quý khách là?`
  String get position_at_shop {
    return Intl.message(
      'Vai trò của quý khách là?',
      name: 'position_at_shop',
      desc: '',
      args: [],
    );
  }

  /// `Chủ cửa hàng`
  String get owner_shop {
    return Intl.message(
      'Chủ cửa hàng',
      name: 'owner_shop',
      desc: '',
      args: [],
    );
  }

  /// `Thành viên`
  String get member {
    return Intl.message(
      'Thành viên',
      name: 'member',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký mở shop`
  String get register_shop {
    return Intl.message(
      'Đăng ký mở shop',
      name: 'register_shop',
      desc: '',
      args: [],
    );
  }

  /// `Nhập mã shop`
  String get join_shop {
    return Intl.message(
      'Nhập mã shop',
      name: 'join_shop',
      desc: '',
      args: [],
    );
  }

  /// `Giao dịch`
  String get transaction {
    return Intl.message(
      'Giao dịch',
      name: 'transaction',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin`
  String get information {
    return Intl.message(
      'Thông tin',
      name: 'information',
      desc: '',
      args: [],
    );
  }

  /// `My Shop là gì?`
  String get my_shop_faq {
    return Intl.message(
      'My Shop là gì?',
      name: 'my_shop_faq',
      desc: '',
      args: [],
    );
  }

  /// `Đã hiểu`
  String get understood {
    return Intl.message(
      'Đã hiểu',
      name: 'understood',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách shop`
  String get list_shop {
    return Intl.message(
      'Danh sách shop',
      name: 'list_shop',
      desc: '',
      args: [],
    );
  }

  /// `Nhập mã shop`
  String get input_shop {
    return Intl.message(
      'Nhập mã shop',
      name: 'input_shop',
      desc: '',
      args: [],
    );
  }

  /// `Mở thêm shop`
  String get create_shop {
    return Intl.message(
      'Mở thêm shop',
      name: 'create_shop',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get history {
    return Intl.message(
      'History',
      name: 'history',
      desc: '',
      args: [],
    );
  }

  /// `Lịch sử giao dịch`
  String get history_transaction {
    return Intl.message(
      'Lịch sử giao dịch',
      name: 'history_transaction',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách thành viên`
  String get list_of_member {
    return Intl.message(
      'Danh sách thành viên',
      name: 'list_of_member',
      desc: '',
      args: [],
    );
  }

  /// `Xem tất cả`
  String get see_all {
    return Intl.message(
      'Xem tất cả',
      name: 'see_all',
      desc: '',
      args: [],
    );
  }

  /// `Thêm mới`
  String get shop_add {
    return Intl.message(
      'Thêm mới',
      name: 'shop_add',
      desc: '',
      args: [],
    );
  }

  /// `Chờ duyệt`
  String get shop_waiting_status {
    return Intl.message(
      'Chờ duyệt',
      name: 'shop_waiting_status',
      desc: '',
      args: [],
    );
  }

  /// `Sao chép thành công`
  String get copy_successfully {
    return Intl.message(
      'Sao chép thành công',
      name: 'copy_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Yêu cầu tham gia shop`
  String get request_shop {
    return Intl.message(
      'Yêu cầu tham gia shop',
      name: 'request_shop',
      desc: '',
      args: [],
    );
  }

  /// `Yêu cầu tham gia`
  String get request_join_shop {
    return Intl.message(
      'Yêu cầu tham gia',
      name: 'request_join_shop',
      desc: '',
      args: [],
    );
  }

  /// `Nhập ID Shop`
  String get input_id_shop {
    return Intl.message(
      'Nhập ID Shop',
      name: 'input_id_shop',
      desc: '',
      args: [],
    );
  }

  /// `ID Shop`
  String get hint_id_shop {
    return Intl.message(
      'ID Shop',
      name: 'hint_id_shop',
      desc: '',
      args: [],
    );
  }

  /// `Tìm kiếm`
  String get search {
    return Intl.message(
      'Tìm kiếm',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng nhập ID Shop`
  String get valid_input_id_shop {
    return Intl.message(
      'Vui lòng nhập ID Shop',
      name: 'valid_input_id_shop',
      desc: '',
      args: [],
    );
  }

  /// `Nhập ID Shop để tiếp tục`
  String get title_input_id_shop {
    return Intl.message(
      'Nhập ID Shop để tiếp tục',
      name: 'title_input_id_shop',
      desc: '',
      args: [],
    );
  }

  /// `Nhập ID của Shop để yêu cầu tham gia shop,\nID Shop sẽ do người quản lý cung cấp.`
  String get content_input_id_shop {
    return Intl.message(
      'Nhập ID của Shop để yêu cầu tham gia shop,\nID Shop sẽ do người quản lý cung cấp.',
      name: 'content_input_id_shop',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin shop`
  String get shop_info {
    return Intl.message(
      'Thông tin shop',
      name: 'shop_info',
      desc: '',
      args: [],
    );
  }

  /// `Tên cửa hàng`
  String get shop_name {
    return Intl.message(
      'Tên cửa hàng',
      name: 'shop_name',
      desc: '',
      args: [],
    );
  }

  /// `Chủ cửa hàng`
  String get owner_name {
    return Intl.message(
      'Chủ cửa hàng',
      name: 'owner_name',
      desc: '',
      args: [],
    );
  }

  /// `Địa chỉ`
  String get address {
    return Intl.message(
      'Địa chỉ',
      name: 'address',
      desc: '',
      args: [],
    );
  }

  /// `Tạo yêu cầu thanh toán`
  String get create_payment_request {
    return Intl.message(
      'Tạo yêu cầu thanh toán',
      name: 'create_payment_request',
      desc: '',
      args: [],
    );
  }

  /// `Lưu mã`
  String get shop_save_qr {
    return Intl.message(
      'Lưu mã',
      name: 'shop_save_qr',
      desc: '',
      args: [],
    );
  }

  /// `Chia sẻ`
  String get shop_share {
    return Intl.message(
      'Chia sẻ',
      name: 'shop_share',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa thông tin shop`
  String get edit_shop_info {
    return Intl.message(
      'Chỉnh sửa thông tin shop',
      name: 'edit_shop_info',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký tài khoản nickname`
  String get add_nick_name {
    return Intl.message(
      'Đăng ký tài khoản nickname',
      name: 'add_nick_name',
      desc: '',
      args: [],
    );
  }

  /// `Tạo yêu cầu thanh toán`
  String get request_tranfer {
    return Intl.message(
      'Tạo yêu cầu thanh toán',
      name: 'request_tranfer',
      desc: '',
      args: [],
    );
  }

  /// `Cài đặt nội dung chuyển tiền`
  String get setup_content_tranfer {
    return Intl.message(
      'Cài đặt nội dung chuyển tiền',
      name: 'setup_content_tranfer',
      desc: '',
      args: [],
    );
  }

  /// `Cài đặt chia sẻ thông báo`
  String get setup_noti_share {
    return Intl.message(
      'Cài đặt chia sẻ thông báo',
      name: 'setup_noti_share',
      desc: '',
      args: [],
    );
  }

  /// `Nhận yêu cầu tham gia shop`
  String get receive_join_shop {
    return Intl.message(
      'Nhận yêu cầu tham gia shop',
      name: 'receive_join_shop',
      desc: '',
      args: [],
    );
  }

  /// `Vô hiệu hóa`
  String get shop_disable_status {
    return Intl.message(
      'Vô hiệu hóa',
      name: 'shop_disable_status',
      desc: '',
      args: [],
    );
  }

  /// `Cho phép quý khách nhận các yêu cầu tham gia shop từ mọi người`
  String get receive_join_shop_content {
    return Intl.message(
      'Cho phép quý khách nhận các yêu cầu tham gia shop từ mọi người',
      name: 'receive_join_shop_content',
      desc: '',
      args: [],
    );
  }

  /// `Tạo yêu cầu chuyển tiền thành công`
  String get shop_create_payment_request_success {
    return Intl.message(
      'Tạo yêu cầu chuyển tiền thành công',
      name: 'shop_create_payment_request_success',
      desc: '',
      args: [],
    );
  }

  /// `Chủ tài khoản`
  String get shop_account_holder {
    return Intl.message(
      'Chủ tài khoản',
      name: 'shop_account_holder',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền`
  String get shop_amount {
    return Intl.message(
      'Số tiền',
      name: 'shop_amount',
      desc: '',
      args: [],
    );
  }

  /// `Nội dung`
  String get shop_content {
    return Intl.message(
      'Nội dung',
      name: 'shop_content',
      desc: '',
      args: [],
    );
  }

  /// `Nickname shop`
  String get nickname_shop {
    return Intl.message(
      'Nickname shop',
      name: 'nickname_shop',
      desc: '',
      args: [],
    );
  }

  /// `Chưa có giao dịch`
  String get shop_no_transaction_yet {
    return Intl.message(
      'Chưa có giao dịch',
      name: 'shop_no_transaction_yet',
      desc: '',
      args: [],
    );
  }

  /// `Các giao dịch thanh toán của shop sẽ hiển thị tại đây.`
  String get shop_payment_transactions_displayed_here {
    return Intl.message(
      'Các giao dịch thanh toán của shop sẽ hiển thị tại đây.',
      name: 'shop_payment_transactions_displayed_here',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa nội dung chuyển tiền thành công`
  String get edit_content_transfer_success {
    return Intl.message(
      'Chỉnh sửa nội dung chuyển tiền thành công',
      name: 'edit_content_transfer_success',
      desc: '',
      args: [],
    );
  }

  /// `Nhập nội dung`
  String get shop_enter_content {
    return Intl.message(
      'Nhập nội dung',
      name: 'shop_enter_content',
      desc: '',
      args: [],
    );
  }

  /// `Nội dung chuyển tiền`
  String get shop_transfer_content {
    return Intl.message(
      'Nội dung chuyển tiền',
      name: 'shop_transfer_content',
      desc: '',
      args: [],
    );
  }

  /// `Lưu`
  String get shop_save {
    return Intl.message(
      'Lưu',
      name: 'shop_save',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận`
  String get shop_confirm {
    return Intl.message(
      'Xác nhận',
      name: 'shop_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Quý khách có chắc chắn muốn bật nhận yêu cầu tham gia shop?`
  String get sure_want_to_enable_receiving_requests_join_shop {
    return Intl.message(
      'Quý khách có chắc chắn muốn bật nhận yêu cầu tham gia shop?',
      name: 'sure_want_to_enable_receiving_requests_join_shop',
      desc: '',
      args: [],
    );
  }

  /// `Quay lại`
  String get shop_back {
    return Intl.message(
      'Quay lại',
      name: 'shop_back',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận thông tin`
  String get shop_confirm_info {
    return Intl.message(
      'Xác nhận thông tin',
      name: 'shop_confirm_info',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản nguồn`
  String get shop_source_account {
    return Intl.message(
      'Tài khoản nguồn',
      name: 'shop_source_account',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản đăng ký`
  String get shop_register_account {
    return Intl.message(
      'Tài khoản đăng ký',
      name: 'shop_register_account',
      desc: '',
      args: [],
    );
  }

  /// `Phí giao dịch`
  String get transaction_fee {
    return Intl.message(
      'Phí giao dịch',
      name: 'transaction_fee',
      desc: '',
      args: [],
    );
  }

  /// `Mỗi shop chỉ được đăng ký 1 nickname shop, quý khách vui lòng kiểm tra kỹ trước khi xác nhận`
  String get required_register_shop_message {
    return Intl.message(
      'Mỗi shop chỉ được đăng ký 1 nickname shop, quý khách vui lòng kiểm tra kỹ trước khi xác nhận',
      name: 'required_register_shop_message',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản liên kết shop`
  String get shop_link_account {
    return Intl.message(
      'Tài khoản liên kết shop',
      name: 'shop_link_account',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa nickname shop`
  String get edit_nickname_shop {
    return Intl.message(
      'Chỉnh sửa nickname shop',
      name: 'edit_nickname_shop',
      desc: '',
      args: [],
    );
  }

  /// `Điền thông tin shop`
  String get enter_info_shop {
    return Intl.message(
      'Điền thông tin shop',
      name: 'enter_info_shop',
      desc: '',
      args: [],
    );
  }

  /// `ID Shop này đã tồn tại trên hệ thống, vui lòng chọn ID Shop khác.`
  String get shop_id_exists_please_choose_another_id {
    return Intl.message(
      'ID Shop này đã tồn tại trên hệ thống, vui lòng chọn ID Shop khác.',
      name: 'shop_id_exists_please_choose_another_id',
      desc: '',
      args: [],
    );
  }

  /// `Liên kết thành công`
  String get linking_success {
    return Intl.message(
      'Liên kết thành công',
      name: 'linking_success',
      desc: '',
      args: [],
    );
  }

  /// `Chúc mừng, quý khách đã liên kết PAYBOX thành công.`
  String get linking_success_describe {
    return Intl.message(
      'Chúc mừng, quý khách đã liên kết PAYBOX thành công.',
      name: 'linking_success_describe',
      desc: '',
      args: [],
    );
  }

  /// `Trạng thái hoạt động`
  String get operation_status {
    return Intl.message(
      'Trạng thái hoạt động',
      name: 'operation_status',
      desc: '',
      args: [],
    );
  }

  /// `Ngưng hoạt động`
  String get shutdown_status {
    return Intl.message(
      'Ngưng hoạt động',
      name: 'shutdown_status',
      desc: '',
      args: [],
    );
  }

  /// `Tắt hoạt động PAYBOX`
  String get turn_off_paybox {
    return Intl.message(
      'Tắt hoạt động PAYBOX',
      name: 'turn_off_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Tiếp tục`
  String get continue_text {
    return Intl.message(
      'Tiếp tục',
      name: 'continue_text',
      desc: '',
      args: [],
    );
  }

  /// `Đang hoạt động`
  String get active {
    return Intl.message(
      'Đang hoạt động',
      name: 'active',
      desc: '',
      args: [],
    );
  }

  /// `Hoàn thành`
  String get complete {
    return Intl.message(
      'Hoàn thành',
      name: 'complete',
      desc: '',
      args: [],
    );
  }

  /// `Thật ngại quá! Dường như KienlongBank không thể đọc nội dung QR Code.`
  String get qr_code_unknown_message {
    return Intl.message(
      'Thật ngại quá! Dường như KienlongBank không thể đọc nội dung QR Code.',
      name: 'qr_code_unknown_message',
      desc: '',
      args: [],
    );
  }

  /// `Chưa liên kết PAYBOX`
  String get not_link_paybox {
    return Intl.message(
      'Chưa liên kết PAYBOX',
      name: 'not_link_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Liên kết PAYBOX giúp cho việc thanh toán thuận tiện, đơn giản và chuyên nghiệp hơn bao giờ hết.`
  String get not_link_paybox_describe {
    return Intl.message(
      'Liên kết PAYBOX giúp cho việc thanh toán thuận tiện, đơn giản và chuyên nghiệp hơn bao giờ hết.',
      name: 'not_link_paybox_describe',
      desc: '',
      args: [],
    );
  }

  /// `PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn tắt?`
  String get turn_off_paybox_describe {
    return Intl.message(
      'PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn tắt?',
      name: 'turn_off_paybox_describe',
      desc: '',
      args: [],
    );
  }

  /// `Huỷ liên kết PAYBOX`
  String get unlink_paybox {
    return Intl.message(
      'Huỷ liên kết PAYBOX',
      name: 'unlink_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Sử dụng PAYBOX, thanh toán tiện lợi`
  String get using_paybox_title {
    return Intl.message(
      'Sử dụng PAYBOX, thanh toán tiện lợi',
      name: 'using_paybox_title',
      desc: '',
      args: [],
    );
  }

  /// `Sản phẩm PAYBOX đến từ Unicloud cung cấp giải pháp thanh toán an toàn, hiện đại và tiện lợi.`
  String get using_paybox_describe {
    return Intl.message(
      'Sản phẩm PAYBOX đến từ Unicloud cung cấp giải pháp thanh toán an toàn, hiện đại và tiện lợi.',
      name: 'using_paybox_describe',
      desc: '',
      args: [],
    );
  }

  /// `PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn huỷ liên kết?`
  String get unlink_paybox_describe {
    return Intl.message(
      'PAYBOX sẽ không nhận được thông tin chuyển tiền từ shop nữa. Quý khách có chắc chắn muốn huỷ liên kết?',
      name: 'unlink_paybox_describe',
      desc: '',
      args: [],
    );
  }

  /// `Cài đặt chia sẻ thông báo`
  String get shop_settings_share_notification {
    return Intl.message(
      'Cài đặt chia sẻ thông báo',
      name: 'shop_settings_share_notification',
      desc: '',
      args: [],
    );
  }

  /// `Người giới thiệu`
  String get shop_referer {
    return Intl.message(
      'Người giới thiệu',
      name: 'shop_referer',
      desc: '',
      args: [],
    );
  }

  /// `Người giới thiệu (nếu có)`
  String get shop_referer_if_any {
    return Intl.message(
      'Người giới thiệu (nếu có)',
      name: 'shop_referer_if_any',
      desc: '',
      args: [],
    );
  }

  /// `Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi khi mở shop.`
  String get shop_enter_referral_code_tool_tip {
    return Intl.message(
      'Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi khi mở shop.',
      name: 'shop_enter_referral_code_tool_tip',
      desc: '',
      args: [],
    );
  }

  /// `Nhập mã người giới thiệu sẽ giúp quý khách đảm bảo quyền lợi khi mở shop tại KienlongBank.`
  String get shop_enter_referral_code_message {
    return Intl.message(
      'Nhập mã người giới thiệu sẽ giúp quý khách đảm bảo quyền lợi khi mở shop tại KienlongBank.',
      name: 'shop_enter_referral_code_message',
      desc: '',
      args: [],
    );
  }

  /// `Khoá do nợ phí`
  String get block_unpaid {
    return Intl.message(
      'Khoá do nợ phí',
      name: 'block_unpaid',
      desc: '',
      args: [],
    );
  }

  /// `Đã khóa`
  String get locked {
    return Intl.message(
      'Đã khóa',
      name: 'locked',
      desc: '',
      args: [],
    );
  }

  /// `Cấu hình thông báo`
  String get config_notification {
    return Intl.message(
      'Cấu hình thông báo',
      name: 'config_notification',
      desc: '',
      args: [],
    );
  }

  /// `Giải pháp thanh toán tiện lợi dành cho mọi cửa hàng`
  String get paybox_faq_title_1 {
    return Intl.message(
      'Giải pháp thanh toán tiện lợi dành cho mọi cửa hàng',
      name: 'paybox_faq_title_1',
      desc: '',
      args: [],
    );
  }

  /// `Tiết kiệm thời gian thanh toán, nâng cao trải nghiệm khách hàng.`
  String get paybox_faq_msg_1 {
    return Intl.message(
      'Tiết kiệm thời gian thanh toán, nâng cao trải nghiệm khách hàng.',
      name: 'paybox_faq_msg_1',
      desc: '',
      args: [],
    );
  }

  /// `Paybox Lite`
  String get paybox_faq_title_2 {
    return Intl.message(
      'Paybox Lite',
      name: 'paybox_faq_title_2',
      desc: '',
      args: [],
    );
  }

  /// `Thiết bị liên kết trực tiếp với MyShop, giải quyết bài toán thanh toán qua VietQR`
  String get paybox_faq_msg_2 {
    return Intl.message(
      'Thiết bị liên kết trực tiếp với MyShop, giải quyết bài toán thanh toán qua VietQR',
      name: 'paybox_faq_msg_2',
      desc: '',
      args: [],
    );
  }

  /// `Paybox Mini`
  String get paybox_faq_title_3 {
    return Intl.message(
      'Paybox Mini',
      name: 'paybox_faq_title_3',
      desc: '',
      args: [],
    );
  }

  /// `Thiết bị cảm ứng hỗ trợ đa phương thức thanh toán nhiều loại thẻ và QR Code các ngân hàng/ví điện tử thông dụng`
  String get paybox_faq_msg_3 {
    return Intl.message(
      'Thiết bị cảm ứng hỗ trợ đa phương thức thanh toán nhiều loại thẻ và QR Code các ngân hàng/ví điện tử thông dụng',
      name: 'paybox_faq_msg_3',
      desc: '',
      args: [],
    );
  }

  /// `Tên đơn vị quản lý người giới thiệu`
  String get name_of_the_unit_managing_the_referrer {
    return Intl.message(
      'Tên đơn vị quản lý người giới thiệu',
      name: 'name_of_the_unit_managing_the_referrer',
      desc: '',
      args: [],
    );
  }

  /// `Nhập mã giới thiệu sẽ giúp khách hàng đảm bảo quyền lợi khi liên kết Paybox với Myshop của KienlongBank.`
  String get shop_enter_referral_code_with_paybox_tool_tip {
    return Intl.message(
      'Nhập mã giới thiệu sẽ giúp khách hàng đảm bảo quyền lợi khi liên kết Paybox với Myshop của KienlongBank.',
      name: 'shop_enter_referral_code_with_paybox_tool_tip',
      desc: '',
      args: [],
    );
  }

  /// `Chọn ngành nghề kinh doanh`
  String get choose_industry {
    return Intl.message(
      'Chọn ngành nghề kinh doanh',
      name: 'choose_industry',
      desc: '',
      args: [],
    );
  }

  /// `Ngành nghề kinh doanh`
  String get industry {
    return Intl.message(
      'Ngành nghề kinh doanh',
      name: 'industry',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin shop của quý khách đã vi phạm chính sách của KienlongBank`
  String get info_shop_violation {
    return Intl.message(
      'Thông tin shop của quý khách đã vi phạm chính sách của KienlongBank',
      name: 'info_shop_violation',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách vi phạm`
  String get list_of_violations {
    return Intl.message(
      'Danh sách vi phạm',
      name: 'list_of_violations',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng chỉnh sửa lại những thông tin vi phạm dưới đây để kích hoạt và sử dụng shop.`
  String get please_edit_the_violating {
    return Intl.message(
      'Vui lòng chỉnh sửa lại những thông tin vi phạm dưới đây để kích hoạt và sử dụng shop.',
      name: 'please_edit_the_violating',
      desc: '',
      args: [],
    );
  }

  /// `Tên shop có dấu hiệu vi phạm`
  String get shop_name_violation {
    return Intl.message(
      'Tên shop có dấu hiệu vi phạm',
      name: 'shop_name_violation',
      desc: '',
      args: [],
    );
  }

  /// `Nickname shop có dấu hiệu vi phạm`
  String get nickname_violation {
    return Intl.message(
      'Nickname shop có dấu hiệu vi phạm',
      name: 'nickname_violation',
      desc: '',
      args: [],
    );
  }

  /// `Hình ảnh shop có dấu hiệu vi phạm`
  String get image_shop_violation {
    return Intl.message(
      'Hình ảnh shop có dấu hiệu vi phạm',
      name: 'image_shop_violation',
      desc: '',
      args: [],
    );
  }

  /// `Xóa shop`
  String get delete_shop {
    return Intl.message(
      'Xóa shop',
      name: 'delete_shop',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa`
  String get edit {
    return Intl.message(
      'Chỉnh sửa',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có chắc chắn muốn xóa shop?`
  String get are_you_sure_delete_shop {
    return Intl.message(
      'Bạn có chắc chắn muốn xóa shop?',
      name: 'are_you_sure_delete_shop',
      desc: '',
      args: [],
    );
  }

  /// `Xóa`
  String get delete {
    return Intl.message(
      'Xóa',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa tên shop`
  String get edit_shop_name {
    return Intl.message(
      'Chỉnh sửa tên shop',
      name: 'edit_shop_name',
      desc: '',
      args: [],
    );
  }

  /// `Rời shop`
  String get leave_shop {
    return Intl.message(
      'Rời shop',
      name: 'leave_shop',
      desc: '',
      args: [],
    );
  }

  /// `Rời`
  String get leave {
    return Intl.message(
      'Rời',
      name: 'leave',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có chắc chắn muốn rời shop?`
  String get are_you_sure_to_leave {
    return Intl.message(
      'Bạn có chắc chắn muốn rời shop?',
      name: 'are_you_sure_to_leave',
      desc: '',
      args: [],
    );
  }

  /// `Mở tài khoản nickname shop`
  String get create_account_nickname_shop {
    return Intl.message(
      'Mở tài khoản nickname shop',
      name: 'create_account_nickname_shop',
      desc: '',
      args: [],
    );
  }

  /// `Chỉnh sửa`
  String get edit_fix {
    return Intl.message(
      'Chỉnh sửa',
      name: 'edit_fix',
      desc: '',
      args: [],
    );
  }

  /// `Magic Paybox`
  String get magic_paybox {
    return Intl.message(
      'Magic Paybox',
      name: 'magic_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Chi tiết`
  String get common_detail {
    return Intl.message(
      'Chi tiết',
      name: 'common_detail',
      desc: '',
      args: [],
    );
  }

  /// `Đang sử dụng`
  String get common_in_use {
    return Intl.message(
      'Đang sử dụng',
      name: 'common_in_use',
      desc: '',
      args: [],
    );
  }

  /// `Đã hủy`
  String get common_canceled {
    return Intl.message(
      'Đã hủy',
      name: 'common_canceled',
      desc: '',
      args: [],
    );
  }

  /// `Miễn phí`
  String get common_free {
    return Intl.message(
      'Miễn phí',
      name: 'common_free',
      desc: '',
      args: [],
    );
  }

  /// `Thoát`
  String get common_back {
    return Intl.message(
      'Thoát',
      name: 'common_back',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký`
  String get common_register {
    return Intl.message(
      'Đăng ký',
      name: 'common_register',
      desc: '',
      args: [],
    );
  }

  /// `tháng`
  String get unit_month {
    return Intl.message(
      'tháng',
      name: 'unit_month',
      desc: '',
      args: [],
    );
  }

  /// `Liên kết Paybox`
  String get pairing_paybox {
    return Intl.message(
      'Liên kết Paybox',
      name: 'pairing_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Huỷ dịch vụ`
  String get cancel_service {
    return Intl.message(
      'Huỷ dịch vụ',
      name: 'cancel_service',
      desc: '',
      args: [],
    );
  }

  /// `Thay đổi kỳ hạn`
  String get change_term {
    return Intl.message(
      'Thay đổi kỳ hạn',
      name: 'change_term',
      desc: '',
      args: [],
    );
  }

  /// `Vô hiệu hóa dịch vụ`
  String get service_disabled {
    return Intl.message(
      'Vô hiệu hóa dịch vụ',
      name: 'service_disabled',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký Magic Paybox`
  String get register_magic_paybox {
    return Intl.message(
      'Đăng ký Magic Paybox',
      name: 'register_magic_paybox',
      desc: '',
      args: [],
    );
  }

  /// `Liên kết PAYBOX`
  String get pairing_paybox_title {
    return Intl.message(
      'Liên kết PAYBOX',
      name: 'pairing_paybox_title',
      desc: '',
      args: [],
    );
  }

  /// `Dịch vụ đã đăng ký`
  String get registered_services {
    return Intl.message(
      'Dịch vụ đã đăng ký',
      name: 'registered_services',
      desc: '',
      args: [],
    );
  }

  /// `Không có dịch vụ nào`
  String get no_services_available {
    return Intl.message(
      'Không có dịch vụ nào',
      name: 'no_services_available',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng chọn 01 dịch vụ.`
  String get selecting_service_instruction {
    return Intl.message(
      'Vui lòng chọn 01 dịch vụ.',
      name: 'selecting_service_instruction',
      desc: '',
      args: [],
    );
  }

  /// `Chọn dịch vụ này`
  String get myshop_choose_service {
    return Intl.message(
      'Chọn dịch vụ này',
      name: 'myshop_choose_service',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký ngay`
  String get subscribe_now {
    return Intl.message(
      'Đăng ký ngay',
      name: 'subscribe_now',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin gói`
  String get magic_package_detail {
    return Intl.message(
      'Thông tin gói',
      name: 'magic_package_detail',
      desc: '',
      args: [],
    );
  }

  /// `Gói đăng ký`
  String get service_package {
    return Intl.message(
      'Gói đăng ký',
      name: 'service_package',
      desc: '',
      args: [],
    );
  }

  /// `Mã thiết bị`
  String get device_id {
    return Intl.message(
      'Mã thiết bị',
      name: 'device_id',
      desc: '',
      args: [],
    );
  }

  /// `Cước phí`
  String get package_fee {
    return Intl.message(
      'Cước phí',
      name: 'package_fee',
      desc: '',
      args: [],
    );
  }

  /// `Ngày đăng ký`
  String get registration_date {
    return Intl.message(
      'Ngày đăng ký',
      name: 'registration_date',
      desc: '',
      args: [],
    );
  }

  /// `Ngày thanh toán kế tiếp`
  String get next_payment_date {
    return Intl.message(
      'Ngày thanh toán kế tiếp',
      name: 'next_payment_date',
      desc: '',
      args: [],
    );
  }

  /// `Điều khoản và điều kiện`
  String get terms_and_conditions {
    return Intl.message(
      'Điều khoản và điều kiện',
      name: 'terms_and_conditions',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin kỳ hạn thanh toán`
  String get payment_cycle_information {
    return Intl.message(
      'Thông tin kỳ hạn thanh toán',
      name: 'payment_cycle_information',
      desc: '',
      args: [],
    );
  }

  /// `Hủy dịch vụ thành công`
  String get service_canceled_successfully {
    return Intl.message(
      'Hủy dịch vụ thành công',
      name: 'service_canceled_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin dịch vụ`
  String get service_information {
    return Intl.message(
      'Thông tin dịch vụ',
      name: 'service_information',
      desc: '',
      args: [],
    );
  }

  /// `Kỳ thanh toán`
  String get payment_cycle {
    return Intl.message(
      'Kỳ thanh toán',
      name: 'payment_cycle',
      desc: '',
      args: [],
    );
  }

  /// `Ngày huỷ dịch vụ`
  String get cancellation_date {
    return Intl.message(
      'Ngày huỷ dịch vụ',
      name: 'cancellation_date',
      desc: '',
      args: [],
    );
  }

  /// `Loại thiết bị`
  String get device_type {
    return Intl.message(
      'Loại thiết bị',
      name: 'device_type',
      desc: '',
      args: [],
    );
  }

  /// `Trạng thái`
  String get status {
    return Intl.message(
      'Trạng thái',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `Đổi kỳ hạn thành công`
  String get payment_cycle_changed__successfully {
    return Intl.message(
      'Đổi kỳ hạn thành công',
      name: 'payment_cycle_changed__successfully',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký thành công`
  String get registration_successful {
    return Intl.message(
      'Đăng ký thành công',
      name: 'registration_successful',
      desc: '',
      args: [],
    );
  }

  /// `Về trang chủ`
  String get myshop_back_to_home {
    return Intl.message(
      'Về trang chủ',
      name: 'myshop_back_to_home',
      desc: '',
      args: [],
    );
  }

  /// `Tiếp tục tạo shop`
  String get continue_creating_shop {
    return Intl.message(
      'Tiếp tục tạo shop',
      name: 'continue_creating_shop',
      desc: '',
      args: [],
    );
  }

  /// `Thanh toán ngay`
  String get pay_now {
    return Intl.message(
      'Thanh toán ngay',
      name: 'pay_now',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận hủy`
  String get confirm_cancellation {
    return Intl.message(
      'Xác nhận hủy',
      name: 'confirm_cancellation',
      desc: '',
      args: [],
    );
  }

  /// `Kỳ hạn thanh toán`
  String get payment_cycle_title_section {
    return Intl.message(
      'Kỳ hạn thanh toán',
      name: 'payment_cycle_title_section',
      desc: '',
      args: [],
    );
  }

  /// `Gói cước Myshop`
  String get myshop_plan_package {
    return Intl.message(
      'Gói cước Myshop',
      name: 'myshop_plan_package',
      desc: '',
      args: [],
    );
  }

  /// `Tổng thu`
  String get total_due {
    return Intl.message(
      'Tổng thu',
      name: 'total_due',
      desc: '',
      args: [],
    );
  }

  /// `Ngày thanh toán`
  String get payment_date {
    return Intl.message(
      'Ngày thanh toán',
      name: 'payment_date',
      desc: '',
      args: [],
    );
  }

  /// `Khóa do nợ phí`
  String get locked_unpaid_fees {
    return Intl.message(
      'Khóa do nợ phí',
      name: 'locked_unpaid_fees',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
