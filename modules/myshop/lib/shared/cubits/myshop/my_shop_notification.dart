part of 'my_shop_cubit.dart';

abstract class MyShopNotification {
  const MyShopNotification();
}

// Create shop
class CanCreateShopNotification extends MyShopNotification {}

class CannotCreateExceedMaxShopNotification extends MyShopNotification {
  const CannotCreateExceedMaxShopNotification({required this.packagePlan});

  final PackagePlan packagePlan;
}

class CannotCreateShopNotification extends MyShopNotification {
  const CannotCreateShopNotification({required this.error});

  final BaseResponseModel error;
}

// Delete Shop
class DeleteShopSuccessNotification extends MyShopNotification {}

class DeleteShopFailureNotification extends MyShopNotification {
  const DeleteShopFailureNotification({required this.error});

  final BaseResponseModel error;
}

// Change Main Tab
class GoDetailShopNotification extends MyShopNotification {
  final int tabIndex;
  final ShopModel shop;

  GoDetailShopNotification({this.tabIndex = 0, required this.shop});
}

class ChangeTabIndexNotification extends MyShopNotification {
  final int tabIndex;

  ChangeTabIndexNotification({required this.tabIndex});
}

// Get Detail shop
class DetailShopFailureNotification extends MyShopNotification {
  const DetailShopFailureNotification({required this.error});

  final BaseResponseModel error;
}

class EmptyShopListNotification extends MyShopNotification {}

class NotRegisterPackagePlanNotification extends MyShopNotification {}

// Leave shop
class LeaveShopSuccessNotification extends MyShopNotification {}

class LeaveShopFailureNotification extends MyShopNotification {
  const LeaveShopFailureNotification({required this.error});

  final BaseResponseModel error;
}
