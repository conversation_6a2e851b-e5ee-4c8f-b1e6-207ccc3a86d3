import 'dart:async';

import 'package:bloc_notification/bloc_notification.dart';
import 'package:common/global_callback.dart';
import 'package:common/utils/log.dart';
import 'package:ksb_common/model/base_response.dart';
import 'package:umee_shop/app.dart';
import 'package:umee_shop/repository/src/model/package_plan/package_plan_model.dart';
import 'package:umee_shop/repository/umee_shop_repository.dart';
import 'package:umee_shop/shared/constants.dart';

import '../../../ui/package_plans/cubit/package_plan_cubit.dart';
import '../base/base_cubit.dart';

part 'my_shop_notification.dart';
part 'my_shop_state.dart';

class RedirectFilterModel {
  final int tabIndex;

  RedirectFilterModel({this.tabIndex = 0});

  factory RedirectFilterModel.fromJson(Map<String, dynamic> json) {
    return RedirectFilterModel(tabIndex: int.tryParse(json['tabIndex']) ?? 0);
  }
}

class KLBShopRedirectModel {
  final String shopCode;
  final RedirectFilterModel? query;

  KLBShopRedirectModel({
    this.shopCode = '',
    this.query,
  });

  //  Uri.parse('shop/991766?tabIndex=1')
  static final String shopPattern = 'shop\/[0-9]{1,}';

  static String? getShopCode(Uri uri) {
    final regExp = RegExp(shopPattern);
    if (regExp.hasMatch(uri.path)) {
      return uri.path.split('/').last;
    }
    return null;
  }

  static RedirectFilterModel? getFilter(Uri uri) {
    return RedirectFilterModel.fromJson(uri.queryParameters);
  }
}

class MyShopCubit extends BaseCubit<MyShopState>
    with BlocNotificationMixin<MyShopState, MyShopNotification> {
  MyShopCubit({
    required this.repository,
    required this.myPackagePlanCubit,
    required this.config,
  }) : super(MyShopInitial(), repository);

  final MyShopRepository repository;
  final MyPackagePlanCubit myPackagePlanCubit;
  final KLBMyShopConfig config;

  StreamSubscription<MyPackagePlanState>? _myPackagePlanSubcription;
  StreamSubscription<PackagePlanNotification>?
      _myPackagePlanNotificationSubcription;

  BaseProfile? _userProfile;

  BaseProfile? get userProfile => _userProfile;

  bool _userHasShop = false;

  bool get userHasShop => _userHasShop;

  void initialize() async {
    try {
      final result = await Future.wait([
        myPackagePlanCubit.myPackageDetail(), // check user has package plan
        getListShopPaging(), // check user has any shop
        getUserProfile() // get user profile
      ]);

      final userHasPackagePlan = result[0] != null;
      _userHasShop = (result[1] as List<ShopModel>).isNotEmpty;

      if (!userHasPackagePlan && !_userHasShop) {
        // user not has shop or package plan
        notify(NotRegisterPackagePlanNotification());
      } else {
        getShopInit(config.redirectUri);
      }
      renderShopList();
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        emit(MyShopListCheckFailedState(error: error));
      }
    }
  }

  Future<BaseProfile?> getUserProfile() async {
    final result = await GlobalCallback.instance.onGetProfile?.call();
    _userProfile = result;
    return result;
  }

  Future<void> getShopInit(Uri? redirectUrl) async {
    if (redirectUrl != null) {
      await redirectShopByFilter(redirectUrl);
    } else {
      await getRecentShop();
    }
  }

  void renderShopList() async {
    try {
      emit(MyShopListCheckingState());
      await repository.getListShop(page: 0, pageSize: 10);
      emit(MyShopListCheckSuccessState());
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        emit(MyShopListCheckFailedState(error: error));
      }
    }
  }

  Future<List<ShopModel>> getListShopPaging(
      {int? pageNumber = 0,
      int? pageSize = PAGING_SIZE,
      bool? checkForNotification = false}) async {
    try {
      final result = await repository.getListShop(
        page: pageNumber,
        pageSize: pageSize,
      );

      final listShop = result?.shops?.toList() ?? [];

      return listShop;
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        throw error;
      } else {
        throw e;
      }
    }
  }

  Future<ShopModel?> redirectShopByFilter(Uri uri) async {
    try {
      final String? shopCode = KLBShopRedirectModel.getShopCode(uri);
      if (shopCode != null) {
        final redirectModel = KLBShopRedirectModel(
            shopCode: shopCode, query: KLBShopRedirectModel.getFilter(uri));
        final result = await repository.getDetailShop(shopCode: shopCode);
        if (result != null) {
          notify(GoDetailShopNotification(
            tabIndex: redirectModel.query?.tabIndex ?? 0,
            shop: result,
          ));
          return result;
        }
      }
    } catch (e) {
      // logger.e(await handleApiError(error));

      final error = await handleApiError(e);
      if (error != null) {
        notify(DetailShopFailureNotification(error: error));
      }
    }
    return null;
  }

  Future<ShopModel?> getRecentShop() async {
    try {
      final result = await repository.getRecentShop();
      if (result != null) {
        notify(GoDetailShopNotification(
          shop: result,
        ));
      }
      return result;
    } catch (error) {
      logger.e(await handleApiError(error));

      // notify(DetailShopFailureNotification(error: await handleApiError(error)));
    }
    return null;
  }

  changeTabIndex(int index) {
    notify(ChangeTabIndexNotification(tabIndex: index));
  }

  Future<void> deleteShop({required String shopId}) async {
    try {
      final otp = await GlobalCallback.instance.openOtp?.call();
      if (otp != null && otp.isNotEmpty) {
        final result = await repository.deleteShop(shopId: shopId, otp: otp);
        if (result) {
          notify(DeleteShopSuccessNotification());
        } else {
          notify(DeleteShopFailureNotification(
            error: BaseResponseModel(
                error: 'Không thể xoá shop. Vui lòng thử lại sau'),
          ));
        }
      }
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(DeleteShopFailureNotification(error: error));
      }
    }
  }

  Future<bool> checkCanCreateShop() async {
    try {
      final result = await repository.checkCanUseCreateShop();
      if (result != null &&
          result.success != null &&
          result.detailPackage != null) {
        if (result.success!) {
          notify(CanCreateShopNotification());
          return true;
        } else {
          notify(CannotCreateExceedMaxShopNotification(
              packagePlan:
                  PackagePlan.fromMyShopPackageData(result.detailPackage!)));
          return false;
        }
      }
      return false;
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(CannotCreateShopNotification(error: error));
      }
      return false;
    }
  }

  Future<bool> leaveShop({required String shopId}) async {
    try {
      final otp = await GlobalCallback.instance.openOtp?.call();
      if (otp != null && otp.isNotEmpty) {
        final result = await repository.leaveShop(shopId: shopId);
        if (result) {
          notify(LeaveShopSuccessNotification());
          return result;
        }
      }
    } catch (e) {
      final error = await handleApiError(e);
      if (error != null) {
        notify(LeaveShopFailureNotification(error: error));
      }
    }
    return false;
  }

  @override
  Future<void> close() async {
    _myPackagePlanSubcription?.cancel();
    _myPackagePlanNotificationSubcription?.cancel();
    super.close();
  }
}
