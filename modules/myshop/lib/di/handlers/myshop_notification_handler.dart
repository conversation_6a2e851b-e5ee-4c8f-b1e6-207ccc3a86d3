import 'package:bloc_notification/bloc_notification.dart';
import 'package:common/navigator.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ksb_common/shared/dialog_error.dart';
import 'package:ksb_common/shared/route_path.dart';
import 'package:nested/nested.dart';
import 'package:umee_shop/shared/cubits/myshop/my_shop_cubit.dart';
import 'package:umee_shop/ui/create_shop_v2/create_shop_page.dart';
import 'package:umee_shop/ui/package_plans/views/package_plans_list_page.dart';

import '../../shared/cubits/event/event_cubit.dart';
import '../../ui/onboarding/onboarding_page.dart';
import '../../ui/shop_detail/shop_detail_page.dart';

class MyShopNotificationHandler extends SingleChildStatelessWidget {
  @override
  Widget buildWithChild(BuildContext context, Widget? child) {
    assert(child != null);
    return BlocNotificationListener<MyShopCubit, MyShopState,
        MyShopNotification>(
      notificationListener: (context, notification) {
        // Detail Shop
        if (notification is GoDetailShopNotification) {
          goWithRoute(
            context,
            ShopDetailPage(
              shop: notification.shop,
              tabIndex: notification.tabIndex,
            ),
            RoutePaths.umee_shop_detail,
          );
        } else if (notification is DetailShopFailureNotification) {
          showDialogError(context, model: notification.error);
        }
        // Register shop
        else if (notification is NotRegisterPackagePlanNotification) {
          go(context, OnBoardingPage(direct: OnBoardingDirection.Page));
        }
        // Create Shop
        else if (notification is CanCreateShopNotification) {
          go(context, CreateShopPageV2());
        } else if (notification is CannotCreateExceedMaxShopNotification) {
          final packagePlan = notification.packagePlan;
          DialogUtil.confirm(
              context,
              Text(
                  'Gói ${packagePlan.packageName} chỉ hỗ trợ tối đa ${packagePlan.maxShop} shop. Bạn có muốn nâng cấp gói cước ?'),
              cancelText: 'Quay lại',
              submitText: 'Nâng cấp',
              onSubmit: () => go(context, ShopPlansListPage()));
        } else if (notification is CannotCreateShopNotification) {
          showDialogError(context, model: notification.error);
        }
        // Delete Shop
        else if (notification is DeleteShopSuccessNotification) {
          popUntilWithRoute(context, RoutePaths.umee_shop_list);
          context.read<EventCubit>().setEvent(EventStatus.DELETE_SHOP);
        } else if (notification is DeleteShopFailureNotification) {
          showDialogError(context, model: notification.error);
        }
        // Leave Shop
        else if (notification is LeaveShopSuccessNotification) {
          popUntilWithRoute(context, RoutePaths.umee_shop_list);
        } else if (notification is LeaveShopFailureNotification) {
          showDialogError(context, model: notification.error);
        }
      },
      child: child!,
    );
  }
}
