import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for TransactionStatisticalItemDto
void main() {
  final instance = TransactionStatisticalItemDtoBuilder();
  // TODO add properties to the builder and call build()

  group(TransactionStatisticalItemDto, () {
    // Mã giao dịch
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Phân loại giao dịch
    // int transactionTypeId
    test('to test the property `transactionTypeId`', () async {
      // TODO
    });

    // Tên phân loại giao dịch
    // String transactionTypeName
    test('to test the property `transactionTypeName`', () async {
      // TODO
    });

    // icon url phân loại giao dịch
    // String transactionTypeIconUrl
    test('to test the property `transactionTypeIconUrl`', () async {
      // TODO
    });

    // Nội dung giao dịch
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // Hệ số: 1: nhận tiền, -1: chuyển tiền
    // int coefficient
    test('to test the property `coefficient`', () async {
      // TODO
    });

    // Loại tiền
    // String currency
    test('to test the property `currency`', () async {
      // TODO
    });

    // Loại tiền
    // String amountStr
    test('to test the property `amountStr`', () async {
      // TODO
    });

    // Loại tiền
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Ngày giao dịch
    // DateTime transactionAt
    test('to test the property `transactionAt`', () async {
      // TODO
    });
  });
}
