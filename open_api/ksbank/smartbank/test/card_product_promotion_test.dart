import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for CardProductPromotion
void main() {
  final instance = CardProductPromotionBuilder();
  // TODO add properties to the builder and call build()

  group(CardProductPromotion, () {
    // ID ưu đãi
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Mã ưu đãi
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Tên ưu đãi
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // Tiêu đề  (markdown)
    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // Mô tả ngắn của ưu đãi (markdown)
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // String iconUrl
    test('to test the property `iconUrl`', () async {
      // TODO
    });

    // Hình ảnh ưu đãi
    // String bannerUrl
    test('to test the property `bannerUrl`', () async {
      // TODO
    });

    // Link chi tiết ưu đãi
    // String fullContentUrl
    test('to test the property `fullContentUrl`', () async {
      // TODO
    });

    // Chi tiết hiển thị dạng tiêu đề và mô tả
    // BuiltList<PromotionDetail> detail
    test('to test the property `detail`', () async {
      // TODO
    });
  });
}
