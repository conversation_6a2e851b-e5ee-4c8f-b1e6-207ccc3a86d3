import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for CardListItem
void main() {
  final instance = CardListItemBuilder();
  // TODO add properties to the builder and call build()

  group(CardListItem, () {
    // CardId nội bộ
    // String cardId
    test('to test the property `cardId`', () async {
      // TODO
    });

    // refCardId bên core bank
    // String refCardId
    test('to test the property `refCardId`', () async {
      // TODO
    });

    // Tên chủ thẻ
    // String cardName
    test('to test the property `cardName`', () async {
      // TODO
    });

    // Số thẻ
    // String cardNo
    test('to test the property `cardNo`', () async {
      // TODO
    });

    // Ng<PERSON>y bắt đầu có hiệu lực thẻ
    // String validFrom
    test('to test the property `validFrom`', () async {
      // TODO
    });

    // <PERSON><PERSON><PERSON> hết hạn thẻ
    // String validTo
    test('to test the property `validTo`', () async {
      // TODO
    });

    // Hãng thẻ
    // String brand
    test('to test the property `brand`', () async {
      // TODO
    });

    // Mã sản phẩm
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Tên sản phẩm
    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // Mã loại sản phẩm
    // String productTypeCode
    test('to test the property `productTypeCode`', () async {
      // TODO
    });

    // Ảnh sản phẩm
    // String productImageUrl
    test('to test the property `productImageUrl`', () async {
      // TODO
    });

    // Trạng thái thẻ
    // String statusCode
    test('to test the property `statusCode`', () async {
      // TODO
    });

    // Tên trạng thái thẻ
    // String statusName
    test('to test the property `statusName`', () async {
      // TODO
    });

    // Hạn mức thẻ credit
    // String creditLimit
    test('to test the property `creditLimit`', () async {
      // TODO
    });

    // id record thẻ trong db
    // String idNbr
    test('to test the property `idNbr`', () async {
      // TODO
    });

    // Đã khóa?
    // bool isLocked
    test('to test the property `isLocked`', () async {
      // TODO
    });

    // Tính năng lock có hoạt động hay không?
    // bool isEnableLock
    test('to test the property `isEnableLock`', () async {
      // TODO
    });
  });
}
