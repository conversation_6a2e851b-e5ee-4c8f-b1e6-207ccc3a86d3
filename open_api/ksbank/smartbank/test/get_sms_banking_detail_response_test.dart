import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for GetSmsBankingDetailResponse
void main() {
  final instance = GetSmsBankingDetailResponseBuilder();
  // TODO add properties to the builder and call build()

  group(GetSmsBankingDetailResponse, () {
    // Đã kích hoạt hay chưa
    // bool enable
    test('to test the property `enable`', () async {
      // TODO
    });

    // Phí dịch vụ của khách hàng
    // num fee
    test('to test the property `fee`', () async {
      // TODO
    });

    // Danh sách số điện thoại
    // BuiltList<SmsPhone> smsPhone
    test('to test the property `smsPhone`', () async {
      // TODO
    });
  });
}
