import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for CheckCashLimitationVers2Request
void main() {
  final instance = CheckCashLimitationVers2RequestBuilder();
  // TODO add properties to the builder and call build()

  group(CheckCashLimitationVers2Request, () {
    // <PERSON><PERSON> giao dịch, đ<PERSON><PERSON><PERSON> tr<PERSON> về sau khi gọi verify face.
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Số tiền giao dịch
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // TransactionCategoriesType transactionCategoriesType
    test('to test the property `transactionCategoriesType`', () async {
      // TODO
    });

    // bool faceVerified
    test('to test the property `faceVerified`', () async {
      // TODO
    });
  });
}
