import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PaymentTopupResponse
void main() {
  final instance = PaymentTopupResponseBuilder();
  // TODO add properties to the builder and call build()

  group(PaymentTopupResponse, () {
    // Mã tra soát với khách hàng
    // String systemTrace
    test('to test the property `systemTrace`', () async {
      // TODO
    });

    // Mã hoạch toán corebank
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Số tiền khách hàng thanh toán
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Mã nhà cung cấp
    // String supplierCode
    test('to test the property `supplierCode`', () async {
      // TODO
    });

    // Tên nhà cung cấp
    // String supplierName
    test('to test the property `supplierName`', () async {
      // TODO
    });

    // Mã sản phẩm
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Tên sản phẩm
    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // Nội dung giao dịch
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // Chiết khấu của khách hàng
    // num discount
    test('to test the property `discount`', () async {
      // TODO
    });
  });
}
