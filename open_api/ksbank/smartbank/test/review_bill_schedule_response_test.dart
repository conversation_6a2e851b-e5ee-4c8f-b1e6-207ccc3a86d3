import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for ReviewBillScheduleResponse
void main() {
  final instance = ReviewBillScheduleResponseBuilder();
  // TODO add properties to the builder and call build()

  group(ReviewBillScheduleResponse, () {
    // Mã sản phẩm
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Mã khách hàng
    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // Tên khách hàng
    // String customerName
    test('to test the property `customerName`', () async {
      // TODO
    });

    // Địa chỉ của khách hàng
    // String customerAddress
    test('to test the property `customerAddress`', () async {
      // TODO
    });
  });
}
