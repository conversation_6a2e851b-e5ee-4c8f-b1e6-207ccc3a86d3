import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for CheckCashLimitationRequest
void main() {
  final instance = CheckCashLimitationRequestBuilder();
  // TODO add properties to the builder and call build()

  group(CheckCashLimitationRequest, () {
    // <PERSON><PERSON> giao dịch, đ<PERSON><PERSON><PERSON> tr<PERSON> về sau khi gọi verify face.
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Số tiền giao dịch
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // TransactionCategoriesType transactionCategoriesType
    test('to test the property `transactionCategoriesType`', () async {
      // TODO
    });

    // bool faceVerified
    test('to test the property `faceVerified`', () async {
      // TODO
    });
  });
}
