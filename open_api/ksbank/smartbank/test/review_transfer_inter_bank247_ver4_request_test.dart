import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for ReviewTransferInterBank247Ver4Request
void main() {
  final instance = ReviewTransferInterBank247Ver4RequestBuilder();
  // TODO add properties to the builder and call build()

  group(ReviewTransferInterBank247Ver4Request, () {
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // int transactionGroup
    test('to test the property `transactionGroup`', () async {
      // TODO
    });

    // String accountNoFrom
    test('to test the property `accountNoFrom`', () async {
      // TODO
    });

    // String accountNoTo
    test('to test the property `accountNoTo`', () async {
      // TODO
    });

    // int isAccount
    test('to test the property `isAccount`', () async {
      // TODO
    });

    // String targetBankCode
    test('to test the property `targetBankCode`', () async {
      // TODO
    });

    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // String channelCode
    test('to test the property `channelCode`', () async {
      // TODO
    });
  });
}
