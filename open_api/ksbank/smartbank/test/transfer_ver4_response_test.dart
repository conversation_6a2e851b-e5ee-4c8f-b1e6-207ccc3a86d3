import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for TransferVer4Response
void main() {
  final instance = TransferVer4ResponseBuilder();
  // TODO add properties to the builder and call build()

  group(TransferVer4Response, () {
    // Transaction transaction
    test('to test the property `transaction`', () async {
      // TODO
    });

    // AdvanceTransferResponse advanceTransferResponse
    test('to test the property `advanceTransferResponse`', () async {
      // TODO
    });
  });
}
