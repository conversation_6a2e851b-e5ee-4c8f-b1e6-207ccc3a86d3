import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for TopupValue
void main() {
  final instance = TopupValueBuilder();
  // TODO add properties to the builder and call build()

  group(TopupValue, () {
    // Mã sản phẩm
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Tên sản phẩm
    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // Giá trị sản phẩm
    // num productValue
    test('to test the property `productValue`', () async {
      // TODO
    });

    // Số tiền khách hàng cần thanh toán
    // num purchasingPrice
    test('to test the property `purchasingPrice`', () async {
      // TODO
    });

    // Phần trăm chiết khấu dành cho khách hàng
    // num discount
    test('to test the property `discount`', () async {
      // TODO
    });
  });
}
