import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for GetValuesTopupResponse
void main() {
  final instance = GetValuesTopupResponseBuilder();
  // TODO add properties to the builder and call build()

  group(GetValuesTopupResponse, () {
    // String phoneNo
    test('to test the property `phoneNo`', () async {
      // TODO
    });

    // String supplierCode
    test('to test the property `supplierCode`', () async {
      // TODO
    });

    // String supplierName
    test('to test the property `supplierName`', () async {
      // TODO
    });

    // String iconUrl
    test('to test the property `iconUrl`', () async {
      // TODO
    });

    // BuiltList<TopupValueDto> values
    test('to test the property `values`', () async {
      // TODO
    });
  });
}
