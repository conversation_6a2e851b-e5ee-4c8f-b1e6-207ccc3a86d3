import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for QueryBillResponse
void main() {
  final instance = QueryBillResponseBuilder();
  // TODO add properties to the builder and call build()

  group(QueryBillResponse, () {
    // Mã dịch vụ
    // String serviceCode
    test('to test the property `serviceCode`', () async {
      // TODO
    });

    // Tên dịch vụ
    // String serviceName
    test('to test the property `serviceName`', () async {
      // TODO
    });

    // Mã nhà cung cấp
    // String supplierCode
    test('to test the property `supplierCode`', () async {
      // TODO
    });

    // Tên nhà cung cấp
    // String supplierName
    test('to test the property `supplierName`', () async {
      // TODO
    });

    // Mã khách hàng
    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // Tên khách hàng
    // String customerName
    test('to test the property `customerName`', () async {
      // TODO
    });

    // Địa chỉ khách hàng
    // String customerAddress
    test('to test the property `customerAddress`', () async {
      // TODO
    });

    // Danh sách hóa đơn của khách hàng
    // BuiltList<BillInfoDto> bills
    test('to test the property `bills`', () async {
      // TODO
    });
  });
}
