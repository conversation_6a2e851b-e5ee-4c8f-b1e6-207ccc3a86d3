import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

/// tests for CardApi
void main() {
  final instance = KsbankApiSmartbank().getCardApi();

  group(CardApi, () {
    // Active thẻ credit
    //
    //Future<BaseResponseActiveCreditCardResponse> activeCreditCard(ActiveCreditCardRequest activeCreditCardRequest) async
    test('test activeCreditCard', () async {
      // TODO
    });

    // Active thẻ debit
    //
    //Future<BaseResponseActiveDebitCardResponse> activeDebitCard(ActiveDebitCardRequest activeDebitCardRequest) async
    test('test activeDebitCard', () async {
      // TODO
    });

    // [PIN-CARD] Thay đổi PIN KH
    //
    //Future<BaseResponseChangePINCardResponse> changePINCard(ChangePINCardRequest changePINCardRequest) async
    test('test changePINCard', () async {
      // TODO
    });

    // Kiểm tra thông tin thẻ trước khi active
    //
    //Future<BaseResponseCheckCardForActiveResponse> checkCardForActive(String idNbr, String fourLastDigit) async
    test('test checkCardForActive', () async {
      // TODO
    });

    // [PIN-CARD] Kiểm tra PIN cũ KH
    //
    //Future<BaseResponseCheckValidPINCardResponse> checkValidPINCard(CheckValidPINCardRequest checkValidPINCardRequest) async
    test('test checkValidPINCard', () async {
      // TODO
    });

    // Sao kê tập tin pdf
    //
    //Future<Uint8List> exportPdf(ExportPdfCardRequest exportPdfCardRequest) async
    test('test exportPdf', () async {
      // TODO
    });

    // Xuất danh sách giao dịch thẻ debit
    //
    //Future<Uint8List> exportPdfTransactionsDebitCard(ExportTransactionsCardDebitToPdfRequest exportTransactionsCardDebitToPdfRequest) async
    test('test exportPdfTransactionsDebitCard', () async {
      // TODO
    });

    // Lấy danh sách thẻ chờ mở
    //
    //Future<BaseResponseGetAllCardWaitOpenResponse> getAllCardWaitOpen() async
    test('test getAllCardWaitOpen', () async {
      // TODO
    });

    // Lấy thông tin trích nợ thẻ tự động
    //
    //Future<BaseResponseAutoPayCardStatusResponse> getAutoPaymentCardStatus(String cardNo, String cardId) async
    test('test getAutoPaymentCardStatus', () async {
      // TODO
    });

    // Thông tin chi tiết thẻ của khách hàng
    //
    //Future<ApiResponseDtoGetDetailCardResponse> getCardDetail(GetDetailCardRequest getDetailCardRequest) async
    test('test getCardDetail', () async {
      // TODO
    });

    // Danh sách thẻ của khách hàng
    //
    //Future<ApiResponseDtoGetCardsResponse> getCards(GetCardsRequest getCardsRequest) async
    test('test getCards', () async {
      // TODO
    });

    // Danh sách các chức năng của thẻ
    //
    //Future<ApiResponseDtoGetFunctionCardsResponse> getFunctions(GetFunctionCardsRequest getFunctionCardsRequest) async
    test('test getFunctions', () async {
      // TODO
    });

    // Lấy thông tin chủ thẻ
    //
    //Future<BaseResponseGetOwnerCardInfoResponse> getOwnerCardInfo(String cardNo) async
    test('test getOwnerCardInfo', () async {
      // TODO
    });

    // Chi tiết sản phẩm thẻ
    //
    //Future<ApiResponseDtoGetDetailProductCardResponse> getProductDetail(GetDetailProductCardRequest getDetailProductCardRequest) async
    test('test getProductDetail', () async {
      // TODO
    });

    // Danh sách kỳ sao sao kê của thẻ
    //
    //Future<ApiResponseDtoGetStatementListCardResponse> getStatementListCard(GetStatementListCardRequest getStatementListCardRequest) async
    test('test getStatementListCard', () async {
      // TODO
    });

    // Thông tin tổng hợp về thẻ
    //
    //Future<ApiResponseDtoGetSummaryCardsResponse> getSummaryCards(GetSummaryCardsRequest getSummaryCardsRequest) async
    test('test getSummaryCards', () async {
      // TODO
    });

    // Lịch sử giao dịch thẻ
    //
    //Future<ApiResponseDtoGetTransactionCardsResponse> getTransactions1(GetTransactionCardsResponse getTransactionCardsResponse) async
    test('test getTransactions1', () async {
      // TODO
    });

    // Khóa thẻ của khách hàng
    //
    //Future<ApiResponseDtoLockCardResponse> lockCard(LockCardRequest lockCardRequest) async
    test('test lockCard', () async {
      // TODO
    });

    // Thanh toán nợ thẻ (tín dụng...)
    //
    //Future<ApiResponseDtoPaymentCardResponse> payment(PaymentCardRequest paymentCardRequest) async
    test('test payment', () async {
      // TODO
    });

    // [PIN-CARD] PreCheck Reset PIN
    //
    //Future<BaseResponsePreCheckResetPINCardResponse> preCheckResetCountPINCard(PreCheckResetPINCardRequest preCheckResetPINCardRequest) async
    test('test preCheckResetCountPINCard', () async {
      // TODO
    });

    // [PIN-CARD] Reset số lần nhập sai PIN
    //
    //Future<BaseResponseResetCountPINCardResponse> resetCountPINCard(ResetCountPINCardRequest resetCountPINCardRequest) async
    test('test resetCountPINCard', () async {
      // TODO
    });

    // [PIN-CARD] Reset PIN
    //
    //Future<BaseResponseResetPINCardResponse> resetPINCard(ResetPINCardRequest resetPINCardRequest) async
    test('test resetPINCard', () async {
      // TODO
    });

    // Kiểm tra trước khi thanh toán nợ thẻ (tín dụng...)
    //
    //Future<ApiResponseDtoReviewPaymentCardResponse> reviewPayment1(ReviewPaymentCardRequest reviewPaymentCardRequest) async
    test('test reviewPayment1', () async {
      // TODO
    });

    // Mở khóa thẻ của khách hàng
    //
    //Future<ApiResponseDtoUnlockCardResponse> unlockCard(UnlockCardRequest unlockCardRequest) async
    test('test unlockCard', () async {
      // TODO
    });

    // Cập nhật thông tin trích nợ thẻ tự động
    //
    //Future<BaseResponseUpdateAutoPaymentCardStatusResponse> updateAutoPaymentCardStatus(UpdateAutoPaymentCardStatusRequest updateAutoPaymentCardStatusRequest) async
    test('test updateAutoPaymentCardStatus', () async {
      // TODO
    });

    // Khóa giao dịch online đối với thẻ ATM
    //
    //Future<BaseResponseUpdateEcomLockATMCardResponse> updateEcomLockATMCard(String cardNo, UpdateEcomLockATMCardRequest updateEcomLockATMCardRequest) async
    test('test updateEcomLockATMCard', () async {
      // TODO
    });

    // Khóa giao dịch online đối với thẻ tín dụng
    //
    //Future<BaseResponseUpdateEcomLockCreditCardResponse> updateEcomLockCreditCard(String cardNo, UpdateEcomLockCreditCardRequest updateEcomLockCreditCardRequest) async
    test('test updateEcomLockCreditCard', () async {
      // TODO
    });

    // Khóa và mở khóa các loại dịch vụ thẻ
    //
    //Future<BaseResponseUpdateLockServicesCardResponse> updateLockServicesCard(UpdateLockServicesCardRequest updateLockServicesCardRequest) async
    test('test updateLockServicesCard', () async {
      // TODO
    });
  });
}
