import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for GetInvoicesResponse
void main() {
  final instance = GetInvoicesResponseBuilder();
  // TODO add properties to the builder and call build()

  group(GetInvoicesResponse, () {
    // String supplierId
    test('to test the property `supplierId`', () async {
      // TODO
    });

    // String supplierName
    test('to test the property `supplierName`', () async {
      // TODO
    });

    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // String accountName
    test('to test the property `accountName`', () async {
      // TODO
    });

    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // String customerName
    test('to test the property `customerName`', () async {
      // TODO
    });

    // String address
    test('to test the property `address`', () async {
      // TODO
    });

    // num amountTotal
    test('to test the property `amountTotal`', () async {
      // TODO
    });

    // num fee
    test('to test the property `fee`', () async {
      // TODO
    });

    // bool canPaid
    test('to test the property `canPaid`', () async {
      // TODO
    });

    // String billIds
    test('to test the property `billIds`', () async {
      // TODO
    });

    // String serviceName
    test('to test the property `serviceName`', () async {
      // TODO
    });

    // String icon
    test('to test the property `icon`', () async {
      // TODO
    });

    // String cifNo
    test('to test the property `cifNo`', () async {
      // TODO
    });

    // BuiltList<InvoiceDto> bills
    test('to test the property `bills`', () async {
      // TODO
    });

    // String month
    test('to test the property `month`', () async {
      // TODO
    });

    // String paymentPeriods
    test('to test the property `paymentPeriods`', () async {
      // TODO
    });

    // num totalMoney
    test('to test the property `totalMoney`', () async {
      // TODO
    });
  });
}
