import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for GetFunctionCardsRequest
void main() {
  final instance = GetFunctionCardsRequestBuilder();
  // TODO add properties to the builder and call build()

  group(GetFunctionCardsRequest, () {
    // Mã loại sản phẩm
    // String productTypeCode
    test('to test the property `productTypeCode`', () async {
      // TODO
    });
  });
}
