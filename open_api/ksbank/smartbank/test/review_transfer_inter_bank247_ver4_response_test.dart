import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for ReviewTransferInterBank247Ver4Response
void main() {
  final instance = ReviewTransferInterBank247Ver4ResponseBuilder();
  // TODO add properties to the builder and call build()

  group(ReviewTransferInterBank247Ver4Response, () {
    // String cifNumber
    test('to test the property `cifNumber`', () async {
      // TODO
    });

    // String transactionNumber
    test('to test the property `transactionNumber`', () async {
      // TODO
    });

    // String transactionName
    test('to test the property `transactionName`', () async {
      // TODO
    });

    // int transactionGroup
    test('to test the property `transactionGroup`', () async {
      // TODO
    });

    // String sourceAccountNumber
    test('to test the property `sourceAccountNumber`', () async {
      // TODO
    });

    // String targetAccountNumber
    test('to test the property `targetAccountNumber`', () async {
      // TODO
    });

    // String targetAccountName
    test('to test the property `targetAccountName`', () async {
      // TODO
    });

    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // int fee
    test('to test the property `fee`', () async {
      // TODO
    });

    // int tax
    test('to test the property `tax`', () async {
      // TODO
    });

    // TransNextStep transNextStep
    test('to test the property `transNextStep`', () async {
      // TODO
    });

    // Nội dung content app show nếu có
    // String content
    test('to test the property `content`', () async {
      // TODO
    });

    // int targetAccountType
    test('to test the property `targetAccountType`', () async {
      // TODO
    });
  });
}
