import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

/// tests for PinCodeApi
void main() {
  final instance = KsbankApiSmartbank().getPinCodeApi();

  group(PinCodeApi, () {
    // Tr<PERSON> về số tiền cần thanh toán
    //
    //Future<BaseResponseGetPricePinCodeResponse> getPrice(String productCode, int quantity, String pinCodeType) async
    test('test getPrice', () async {
      // TODO
    });

    // L<PERSON>y danh sách nhà cung cấp, cùng với mệnh giá
    //
    //Future<BaseResponseGetSuppliersPinCodeResponse> getSuppliers1(String pinCodeType) async
    test('test getSuppliers1', () async {
      // TODO
    });

    // Thực hiện thanh toán cho khách hàng
    //
    //Future<BaseResponsePaymentPinCodeResponse> payment2(PaymentPinCodeRequest paymentPinCodeRequest) async
    test('test payment2', () async {
      // TODO
    });
  });
}
