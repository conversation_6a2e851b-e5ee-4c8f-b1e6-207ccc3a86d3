import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for BaseResponseTransferVer4Response
void main() {
  final instance = BaseResponseTransferVer4ResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BaseResponseTransferVer4Response, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // TransferVer4Response data
    test('to test the property `data`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });
  });
}
