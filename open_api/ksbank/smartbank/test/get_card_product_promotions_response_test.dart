import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for GetCardProductPromotionsResponse
void main() {
  final instance = GetCardProductPromotionsResponseBuilder();
  // TODO add properties to the builder and call build()

  group(GetCardProductPromotionsResponse, () {
    // BuiltList<CardProductPromotion> promotions
    test('to test the property `promotions`', () async {
      // TODO
    });

    // LoungePromotion lounges
    test('to test the property `lounges`', () async {
      // TODO
    });
  });
}
