import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PaymentTopupRequest
void main() {
  final instance = PaymentTopupRequestBuilder();
  // TODO add properties to the builder and call build()

  group(PaymentTopupRequest, () {
    // Số điện thoại cần nạp
    // String phoneNo
    test('to test the property `phoneNo`', () async {
      // TODO
    });

    // Số tài khoản thực hiện thanh toán
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // Số tiền thực hiện thanh toán
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Mệnh giá topup
    // num cardValue
    test('to test the property `cardValue`', () async {
      // TODO
    });

    // <PERSON><PERSON> sản phẩm cần thanh toán
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Loại thức nạp
    // String topupType
    test('to test the property `topupType`', () async {
      // TODO
    });

    // VerifySoftOtpRequest softOtp
    test('to test the property `softOtp`', () async {
      // TODO
    });
  });
}
