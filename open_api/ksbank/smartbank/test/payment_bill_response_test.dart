import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PaymentBillResponse
void main() {
  final instance = PaymentBillResponseBuilder();
  // TODO add properties to the builder and call build()

  group(PaymentBillResponse, () {
    // Mã tra soát
    // String systemTrace
    test('to test the property `systemTrace`', () async {
      // TODO
    });

    // Mã hoạch toán corebank
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Số tiền khách hàng thanh toán
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Nội dung giao dịch
    // String description
    test('to test the property `description`', () async {
      // TODO
    });
  });
}
