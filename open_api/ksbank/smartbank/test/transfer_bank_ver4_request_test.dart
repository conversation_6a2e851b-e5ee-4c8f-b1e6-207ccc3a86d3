import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for TransferBankVer4Request
void main() {
  final instance = TransferBankVer4RequestBuilder();
  // TODO add properties to the builder and call build()

  group(TransferBankVer4Request, () {
    // Mã giao dịch, được trả về khi gọi api review
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Ai là người trả phí: 1: người chuyển trả, 0 người nhận trả
    // int whoCharge
    test('to test the property `whoCharge`', () async {
      // TODO
    });

    // Số tiền phí giao dịch
    // int chargeAmount
    test('to test the property `chargeAmount`', () async {
      // TODO
    });

    // Chuyển khoảng sang tài khoản hay sang thẻ: 1: sang tà<PERSON> kho<PERSON>, 0 sang thẻ (hoặc nếu chuyển citad thì 0: CMND/Hộ chiếu)
    // int isAccount
    test('to test the property `isAccount`', () async {
      // TODO
    });

    // Mã bank code id trong bảng bank
    // String bankCodeId
    test('to test the property `bankCodeId`', () async {
      // TODO
    });

    // Tên gợi nhớ của người được chuyển đến
    // String aliasName
    test('to test the property `aliasName`', () async {
      // TODO
    });

    // Chuyển nhanh (NAPAS) hay chuyển chậm (CITAD), 1: Chuyển nhanh, 0: chuyển chậm
    // int is247
    test('to test the property `is247`', () async {
      // TODO
    });

    // 1: chuyển ngay, 0: đặt lịch
    // int isNow
    test('to test the property `isNow`', () async {
      // TODO
    });

    // Lưu mẫu giao dịch, 1: Lưu, 0: Không lưu
    // int isSaveToTemplate
    test('to test the property `isSaveToTemplate`', () async {
      // TODO
    });

    // Nội dung chuyển khoản
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // Phân loại giao dịch
    // int transactionCategoryId
    test('to test the property `transactionCategoryId`', () async {
      // TODO
    });

    // VerifySoftOtpRequest verifySoftOtp
    test('to test the property `verifySoftOtp`', () async {
      // TODO
    });

    // SchedulingObject schedule
    test('to test the property `schedule`', () async {
      // TODO
    });

    // Mã tỉnh thành phố - dùng khi chuyển Citad, bắt buộc
    // int regionId
    test('to test the property `regionId`', () async {
      // TODO
    });

    // Mã ngân hàng - dùng khi chuyển Citad, bắc buộc
    // int bankId
    test('to test the property `bankId`', () async {
      // TODO
    });

    // Mã chi nhánh - dùng khi chuyển Citad, bắt buộc
    // String branchId
    test('to test the property `branchId`', () async {
      // TODO
    });

    // Tên chi nhánh - dùng khi chuyển Citad, chỉ bắt buộc khi là ngân hàng nông nghiệp
    // String branchName
    test('to test the property `branchName`', () async {
      // TODO
    });

    // Tên người thụ hưởng - dùng khi chuyển Citad, bắt buộc
    // String beneficiaryName
    test('to test the property `beneficiaryName`', () async {
      // TODO
    });

    // Nhà cung cấp dịch vụ
    // String scheduleId
    test('to test the property `scheduleId`', () async {
      // TODO
    });

    // Item xác định xem chuyển tiền qua vietQr hay qua cái khác
    // String channelCode
    test('to test the property `channelCode`', () async {
      // TODO
    });

    // Số CMND - dùng khi chuyển Citad, bắt buộc nếu là chuyển theo CMND/Hộ chiếu
    // String idCard
    test('to test the property `idCard`', () async {
      // TODO
    });

    // Ngày cấp CMND - dùng khi chuyển Citad
    // Date issueDate
    test('to test the property `issueDate`', () async {
      // TODO
    });

    // Nơi cấp CMND - dùng khi chuyển Citad
    // String placeBy
    test('to test the property `placeBy`', () async {
      // TODO
    });
  });
}
