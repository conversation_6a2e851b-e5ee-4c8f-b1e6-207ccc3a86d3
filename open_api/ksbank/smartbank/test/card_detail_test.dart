import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for CardDetail
void main() {
  final instance = CardDetailBuilder();
  // TODO add properties to the builder and call build()

  group(CardDetail, () {
    // refCardId bên core bank
    // String refCardId
    test('to test the property `refCardId`', () async {
      // TODO
    });

    // Số thẻ
    // String cardNo
    test('to test the property `cardNo`', () async {
      // TODO
    });

    // Ảnh sản phẩm
    // String productImageUrl
    test('to test the property `productImageUrl`', () async {
      // TODO
    });

    // Mã sản phẩm thẻ
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Sản phẩm thẻ
    // String productName
    test('to test the property `productName`', () async {
      // TODO
    });

    // Tên chủ thẻ
    // String cardName
    test('to test the property `cardName`', () async {
      // TODO
    });

    // Loại thẻ: 1 - Thẻ chính; 2 - Thẻ phụ
    // String ownerType
    test('to test the property `ownerType`', () async {
      // TODO
    });

    // Mã trạng thái thẻ
    // String statusCode
    test('to test the property `statusCode`', () async {
      // TODO
    });

    // Tên trạng thái thẻ
    // String statusName
    test('to test the property `statusName`', () async {
      // TODO
    });

    // Mã trạng thái rút tiền qua ATM
    // String atmStatusCode
    test('to test the property `atmStatusCode`', () async {
      // TODO
    });

    // Mã trạng thái thanh toán qua POS
    // String posStatusCode
    test('to test the property `posStatusCode`', () async {
      // TODO
    });

    // Hạn mức tín dụng
    // String creditLimit
    test('to test the property `creditLimit`', () async {
      // TODO
    });

    // Số tài khoản
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // Hạn mức rút tiền mặt của 1 ngày
    // String dailyCashLimit
    test('to test the property `dailyCashLimit`', () async {
      // TODO
    });

    // Hạn mức giao dịch của 1 ngày khác tiền mặt
    // String dailyNonCashLimit
    test('to test the property `dailyNonCashLimit`', () async {
      // TODO
    });

    // Ngày bắt đầu có hiệu lực thẻ
    // String validFrom
    test('to test the property `validFrom`', () async {
      // TODO
    });

    // Ngày hết hạn thẻ
    // String validTo
    test('to test the property `validTo`', () async {
      // TODO
    });

    // Tổng dư nợ hiện tại
    // String outstandingBalance
    test('to test the property `outstandingBalance`', () async {
      // TODO
    });

    // Ngày mở thẻ
    // String anniversaryDate
    test('to test the property `anniversaryDate`', () async {
      // TODO
    });

    // Kỳ sao kê gần nhất
    // String lastStatementDate
    test('to test the property `lastStatementDate`', () async {
      // TODO
    });

    // Ngày đến hạn thanh toán
    // String paymentDueDate
    test('to test the property `paymentDueDate`', () async {
      // TODO
    });

    // Dư nợ trong kỳ cần thanh toán
    // String totalDueAmount
    test('to test the property `totalDueAmount`', () async {
      // TODO
    });

    // Số tiền thanh toán tối thiểu
    // String minimumPaymentAmount
    test('to test the property `minimumPaymentAmount`', () async {
      // TODO
    });

    // Số tiền chờ xử lý (Tín dụng)
    // String unsettleCash
    test('to test the property `unsettleCash`', () async {
      // TODO
    });

    // Số tiền còn lại tiền mặt ứng trước
    // String availCash
    test('to test the property `availCash`', () async {
      // TODO
    });

    // Ngày thanh toán gần nhất
    // String lastPaymentDate
    test('to test the property `lastPaymentDate`', () async {
      // TODO
    });

    // Số tiền thanh toán gần nhất
    // String lastPaymentAmount
    test('to test the property `lastPaymentAmount`', () async {
      // TODO
    });

    // Ngày giao dịch đầu tiền
    // String firstUseDate
    test('to test the property `firstUseDate`', () async {
      // TODO
    });

    // Ngày kích hoạt thẻ
    // String activatedDate
    test('to test the property `activatedDate`', () async {
      // TODO
    });

    // Ngày giao dịch/thanh toán gần nhất
    // String lastTransDate
    test('to test the property `lastTransDate`', () async {
      // TODO
    });

    // String availableLimit
    test('to test the property `availableLimit`', () async {
      // TODO
    });

    // String ecomStatusCode
    test('to test the property `ecomStatusCode`', () async {
      // TODO
    });

    // Đã thanh toán hay chưa?
    // bool isPaid
    test('to test the property `isPaid`', () async {
      // TODO
    });
  });
}
