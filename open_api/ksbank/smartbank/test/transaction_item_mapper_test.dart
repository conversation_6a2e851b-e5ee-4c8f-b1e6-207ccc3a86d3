import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for TransactionItemMapper
void main() {
  final instance = TransactionItemMapperBuilder();
  // TODO add properties to the builder and call build()

  group(TransactionItemMapper, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Số bankCif của người dùng
    // String cifNo
    test('to test the property `cifNo`', () async {
      // TODO
    });

    // Số tài khoản của người dùng
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // Số giao dịch
    // String transactionNo
    test('to test the property `transactionNo`', () async {
      // TODO
    });

    // Số giao dịch lấy từ core
    // String refTransactionNo
    test('to test the property `refTransactionNo`', () async {
      // TODO
    });

    // Ngày giao dịch
    // DateTime transactionAt
    test('to test the property `transactionAt`', () async {
      // TODO
    });

    // 1: tài khoản, 2: thẻ
    // int isAccount
    test('to test the property `isAccount`', () async {
      // TODO
    });

    // Số tiền giao dịch
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Giao dịch tăng hay giảm (CREDIT, DEBIT), 1: Giao dịch tăng, -1: Giao dịch giảm
    // int coefficient
    test('to test the property `coefficient`', () async {
      // TODO
    });

    // Mô tả giao dịch
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // Số tài khoản của người thụ hưởng
    // String partnerAccountNo
    test('to test the property `partnerAccountNo`', () async {
      // TODO
    });

    // Tên người thụ hưởng
    // String partnerAccountName
    test('to test the property `partnerAccountName`', () async {
      // TODO
    });

    // Tên bí danh
    // String partnerAccountAlias
    test('to test the property `partnerAccountAlias`', () async {
      // TODO
    });

    // Avatar
    // String partnerAccountAvatar
    test('to test the property `partnerAccountAvatar`', () async {
      // TODO
    });

    // Mã Ngân hàng  dùng để join các bản
    // String partnerBankcodeId
    test('to test the property `partnerBankcodeId`', () async {
      // TODO
    });

    // Mã Ngân hàng người thụ hưởng
    // String partnerBankIdNapas
    test('to test the property `partnerBankIdNapas`', () async {
      // TODO
    });

    // Mã ngân hàng trong trường hợp chuyển thường
    // int partnerCitadBankCode
    test('to test the property `partnerCitadBankCode`', () async {
      // TODO
    });

    // Tên Ngân hàng người thụ hưởng
    // String partnerBankName
    test('to test the property `partnerBankName`', () async {
      // TODO
    });

    // Tên rút gọn Ngân hàng người thụ hưởng
    // String partnerBankShortName
    test('to test the property `partnerBankShortName`', () async {
      // TODO
    });

    // Tên thường gọi Ngân hàng người thụ hưởng
    // String partnerBankCommonName
    test('to test the property `partnerBankCommonName`', () async {
      // TODO
    });

    // url icon của ngân hàng
    // String partnerBankUrl
    test('to test the property `partnerBankUrl`', () async {
      // TODO
    });

    // loại icon của ngân hàng
    // String partnerBankUrlType
    test('to test the property `partnerBankUrlType`', () async {
      // TODO
    });

    // Tên tài khoản
    // String accountName
    test('to test the property `accountName`', () async {
      // TODO
    });

    // Phân loại giao dịch
    // int transactionCategoryId
    test('to test the property `transactionCategoryId`', () async {
      // TODO
    });

    // Tên tài khoản
    // String transactionCategoryName
    test('to test the property `transactionCategoryName`', () async {
      // TODO
    });

    // icon link loại giao dịch
    // String transactionCategoryIconUrl
    test('to test the property `transactionCategoryIconUrl`', () async {
      // TODO
    });

    // Tên loại giao dịch
    // String transactionTypeName
    test('to test the property `transactionTypeName`', () async {
      // TODO
    });

    // Tên trạng thái
    // String statusName
    test('to test the property `statusName`', () async {
      // TODO
    });

    // Id của loại giao dịch
    // String transactionTypeId
    test('to test the property `transactionTypeId`', () async {
      // TODO
    });

    // Số thẻ
    // String cardNo
    test('to test the property `cardNo`', () async {
      // TODO
    });

    // Mã khách hàng thanh toán hóa đơn
    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // Mệnh giá thẻ nạp điện thoại
    // num phoneCardValue
    test('to test the property `phoneCardValue`', () async {
      // TODO
    });

    // Mã dịch vụ
    // String serviceId
    test('to test the property `serviceId`', () async {
      // TODO
    });

    // Tên dịch vụ
    // String serviceName
    test('to test the property `serviceName`', () async {
      // TODO
    });

    // Ảnh icon service
    // String serviceIconUrl
    test('to test the property `serviceIconUrl`', () async {
      // TODO
    });

    // Mã nhà cung cấp dịch vụ
    // String supplierId
    test('to test the property `supplierId`', () async {
      // TODO
    });

    // Loại tiền
    // int currency
    test('to test the property `currency`', () async {
      // TODO
    });

    // bool bigTransaction
    test('to test the property `bigTransaction`', () async {
      // TODO
    });
  });
}
