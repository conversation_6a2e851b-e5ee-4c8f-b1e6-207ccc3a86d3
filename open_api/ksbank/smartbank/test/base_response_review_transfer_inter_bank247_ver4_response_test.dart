import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for BaseResponseReviewTransferInterBank247Ver4Response
void main() {
  final instance = BaseResponseReviewTransferInterBank247Ver4ResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BaseResponseReviewTransferInterBank247Ver4Response, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // ReviewTransferInterBank247Ver4Response data
    test('to test the property `data`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });
  });
}
