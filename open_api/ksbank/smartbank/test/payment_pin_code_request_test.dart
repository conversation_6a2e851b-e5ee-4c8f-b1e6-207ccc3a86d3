import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PaymentPinCodeRequest
void main() {
  final instance = PaymentPinCodeRequestBuilder();
  // TODO add properties to the builder and call build()

  group(PaymentPinCodeRequest, () {
    // Số tài khoản thực hiện thanh toán
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // Mã sản phẩm cần thanh toán
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Số tiền thực hiện thanh toán
    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // Mệnh giá.
    // num cardValue
    test('to test the property `cardValue`', () async {
      // TODO
    });

    // <PERSON><PERSON> lượng
    // int quantity
    test('to test the property `quantity`', () async {
      // TODO
    });

    // Pin Code Type
    // String pinCodeType
    test('to test the property `pinCodeType`', () async {
      // TODO
    });

    // VerifySoftOtpRequest softOtp
    test('to test the property `softOtp`', () async {
      // TODO
    });
  });
}
