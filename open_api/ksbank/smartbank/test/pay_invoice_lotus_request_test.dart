import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PayInvoiceLotusRequest
void main() {
  final instance = PayInvoiceLotusRequestBuilder();
  // TODO add properties to the builder and call build()

  group(PayInvoiceLotusRequest, () {
    // Mã nhà cung cấp dịch vụ của hóa đơn : nước bến thành
    // String supplierId
    test('to test the property `supplierId`', () async {
      // TODO
    });

    // Mã khách hàng theo nhà cung cấp
    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // Tên khách hàng theo nhà cung cấp
    // String customerName
    test('to test the property `customerName`', () async {
      // TODO
    });

    // Địa chỉ khách hàng theo nhà cung cấp
    // String customerAddress
    test('to test the property `customerAddress`', () async {
      // TODO
    });

    // lưu cho lần thanh toán tiếp theo
    // bool isSavedNextTime
    test('to test the property `isSavedNextTime`', () async {
      // TODO
    });

    // Bankinf: Mã cif của khách hàng thuộc 1 ngân hàng cụ thể
    // String bankCif
    test('to test the property `bankCif`', () async {
      // TODO
    });

    // Bankinf: Số tài khoản ăn theo mã cif
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // num fee
    test('to test the property `fee`', () async {
      // TODO
    });

    // num amount
    test('to test the property `amount`', () async {
      // TODO
    });

    // String billIds
    test('to test the property `billIds`', () async {
      // TODO
    });
  });
}
