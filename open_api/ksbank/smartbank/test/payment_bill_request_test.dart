import 'package:test/test.dart';
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart';

// tests for PaymentBillRequest
void main() {
  final instance = PaymentBillRequestBuilder();
  // TODO add properties to the builder and call build()

  group(PaymentBillRequest, () {
    // Số tài khoản thực hiện thanh toán
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // Mã sản phẩm cần thanh toán
    // String productCode
    test('to test the property `productCode`', () async {
      // TODO
    });

    // Mã khách hàng
    // String customerCode
    test('to test the property `customerCode`', () async {
      // TODO
    });

    // Danh sách các kỳ định danh hóa đơn của mà khách hàng thanh toán
    // BuiltList<PaymentBillDto> bills
    test('to test the property `bills`', () async {
      // TODO
    });

    // VerifySoftOtpRequest softOtp
    test('to test the property `softOtp`', () async {
      // TODO
    });
  });
}
