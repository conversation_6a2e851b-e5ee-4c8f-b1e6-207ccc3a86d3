//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:ksbank_api_smartbank/src/date_serializer.dart';
import 'package:ksbank_api_smartbank/src/model/date.dart';

import 'package:ksbank_api_smartbank/src/model/acc_card_trans_info.dart';
import 'package:ksbank_api_smartbank/src/model/account_detail_request.dart';
import 'package:ksbank_api_smartbank/src/model/account_detail_response_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/account_inquiry_info.dart';
import 'package:ksbank_api_smartbank/src/model/account_item_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/account_number_info.dart';
import 'package:ksbank_api_smartbank/src/model/account_request.dart';
import 'package:ksbank_api_smartbank/src/model/account_response_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/account_statistical_request.dart';
import 'package:ksbank_api_smartbank/src/model/account_summary.dart';
import 'package:ksbank_api_smartbank/src/model/account_vip_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/active_credit_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/active_credit_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/active_debit_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/active_debit_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/active_virtual_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/active_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/add_transaction_template_request.dart';
import 'package:ksbank_api_smartbank/src/model/add_vvip_customer_request.dart';
import 'package:ksbank_api_smartbank/src/model/add_vvip_customer_response.dart';
import 'package:ksbank_api_smartbank/src/model/advance_e_banking_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/advance_fee_info.dart';
import 'package:ksbank_api_smartbank/src/model/advance_history_detail.dart';
import 'package:ksbank_api_smartbank/src/model/advance_history_info.dart';
import 'package:ksbank_api_smartbank/src/model/advance_request.dart';
import 'package:ksbank_api_smartbank/src/model/advance_transaction_command_response.dart';
import 'package:ksbank_api_smartbank/src/model/advance_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/alias_name_request.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_account_detail_response_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_account_response_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_account_summary.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_base_response_transaction_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_boolean.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_cal_total_saving_onl_rate_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_calculate_interest_online_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_calculate_interest_partial_withdraw_online_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_check_my_shop_payment_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_check_preferential_rate_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_close_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_close_target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_contacts_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_create_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_create_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_create_supplier_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_create_target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_customer.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_fire_schedule_pay_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_card_product_promotions_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_cities_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_detail_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_detail_product_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_disbursements_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_districts_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_function_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_interest_receives_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_list_my_shop_payment_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_list_services_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_list_suppliers_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_loan_process_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_loans_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_payment_accounts_response_v2.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_refund_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_refund_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_saving_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_saving_periods_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_saving_types_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_statement_list_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_summary_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_tele_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_get_transaction_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_bank_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_bank_support_scan_viet_qr_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_branch.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_contact_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_invoice_schedule_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_region.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_telecom_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_topup_value_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_transaction_group_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_transaction_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_transaction_statistical.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_list_transaction_template_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_loan_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_lock_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_online_saving_certificate_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_open_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_open_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_open_vip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_partial_withdraw_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_pay_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_pay_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_payment_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_registered_loan_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_remove_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_close_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_close_target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_open_target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_partial_withdraw_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_payment_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_payment_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_transfer_inter_bank247_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_transfer_inter_bank_citad_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_transfer_intra_bank_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_review_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_saving_account_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_target_saving_trans_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transaction_beneficiary_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transaction_item_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transaction_template_dto.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transfer_schedule_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_transfer_schedule_update_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_unlock_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_update_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_update_supplier_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_update_transaction_category_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_vip_account_info_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/api_response_dto_vip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/app_module_dto.dart';
import 'package:ksbank_api_smartbank/src/model/app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/attachment.dart';
import 'package:ksbank_api_smartbank/src/model/auto_pay_card_status_response.dart';
import 'package:ksbank_api_smartbank/src/model/bank_code_dto.dart';
import 'package:ksbank_api_smartbank/src/model/bank_fee.dart';
import 'package:ksbank_api_smartbank/src/model/bank_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_active_credit_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_active_debit_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_active_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_add_vvip_customer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_advance_fee_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_advance_history_detail.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_advance_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_auto_pay_card_status_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_cancel_transaction_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_change_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_card_for_active_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_is_vnp_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_open_vvip_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_pin_code_trans_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_term_and_condition_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_valid_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_check_verify_user_status_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_confirm_create_withdrawal_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_contact_created_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_advance_package_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_advance_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_customer_vn_post_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_transaction_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_transfer_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_create_withdrawal_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_current_package_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_delete_pin_code_trans_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_delete_transfer_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_ebanking_packages_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_employee_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_fire_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_gen_cvv_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_acc_card_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_acc_card_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_acc_trans_his_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_account_vip_info_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_advance_transaction_note_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_all_card_wait_open_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_all_province_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_app_module_by_id_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_bill_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_card_products_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_due_invoices_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_due_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_e_banking_package_by_customer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_in_progress_advance_transactions_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_invoice_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_list_branch_klb_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_list_inquiry_step_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_list_trans_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_loan_payment_methods_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_loan_profit_spreadsheet_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_loan_purpose_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_owner_card_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_payment_account_info_internal_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_phone_card_price_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_phone_card_providers_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_price_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_saving_accounts_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_services_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_services_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_share_bill_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_sms_banking_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_sms_banking_fee_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_suppliers_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_suppliers_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_suppliers_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_suppliers_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_telecom_providers_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_topup_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_topup_value_all_telco_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_topup_values_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_trans_inquiry_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_trans_inquiry_reason_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_transaction_categories_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_transaction_types_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_unpaid_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_values_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_version_configs_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_ignore_bill_reminder_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_ignore_invoice_debts_reminder_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_list_advance_history_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_list_provider_dto.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_list_user_enterprise_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_list_withdrawal_code_active_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_list_withdrawal_code_expired_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_login_app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_object.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_offline_saving_account_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_open_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_optional_number_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_dto_list_short_salary_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_support_get_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_support_get_paid_phone_card_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_support_get_pin_code_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_support_search_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_page_support_transfer_request_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_pay_invoice_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_payment_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_payment_phone_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_payment_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_payment_topup_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_payment_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_pre_check_issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_pre_check_reset_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_preview_update_sms_banking_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_query_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_re_issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_register_loan_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_remove_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_reset_count_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_reset_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_resolve_payment_data_app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_create_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_create_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_open_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_payment_invoice_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_inter_bank247_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_inter_bank247_ver3_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_inter_bank247_ver4_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_inter_bank_citad_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_intra_bank_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_review_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_share_bills_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transaction_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transaction_statistical_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transfer_info_qr_code_napas_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transfer_request_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transfer_ver3_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_transfer_ver4_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_tuition_fee_dto.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_auto_payment_card_status_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_ecom_lock_atm_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_ecom_lock_credit_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_lock_services_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_sms_banking_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_update_transaction_type_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_user_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_user_logwork_by_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_vnp_close_online_saving_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_vip_account_v1_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_virtual_to_physical_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_vnp_electric_water_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_vnp_review_electric_water_response.dart';
import 'package:ksbank_api_smartbank/src/model/bill_info_dto.dart';
import 'package:ksbank_api_smartbank/src/model/bill_schedule_dto.dart';
import 'package:ksbank_api_smartbank/src/model/branch.dart';
import 'package:ksbank_api_smartbank/src/model/branch_data.dart';
import 'package:ksbank_api_smartbank/src/model/branch_request.dart';
import 'package:ksbank_api_smartbank/src/model/cal_total_saving_onl_rate_request.dart';
import 'package:ksbank_api_smartbank/src/model/cal_total_saving_onl_rate_response.dart';
import 'package:ksbank_api_smartbank/src/model/calculate_interest_online_saving_request.dart';
import 'package:ksbank_api_smartbank/src/model/calculate_interest_online_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/calculate_interest_partial_withdraw_online_saving_request.dart';
import 'package:ksbank_api_smartbank/src/model/calculate_interest_partial_withdraw_online_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/cancel_transaction_inquiry_request.dart';
import 'package:ksbank_api_smartbank/src/model/cancel_transaction_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/card_detail.dart';
import 'package:ksbank_api_smartbank/src/model/card_inquiry_info.dart';
import 'package:ksbank_api_smartbank/src/model/card_list_item.dart';
import 'package:ksbank_api_smartbank/src/model/card_product_data.dart';
import 'package:ksbank_api_smartbank/src/model/card_product_promotion.dart';
import 'package:ksbank_api_smartbank/src/model/card_service_type.dart';
import 'package:ksbank_api_smartbank/src/model/card_wait_open_dto.dart';
import 'package:ksbank_api_smartbank/src/model/cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/change_pin_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/change_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_card_for_active_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_cash_limitation_request.dart';
import 'package:ksbank_api_smartbank/src/model/check_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_cash_limitation_vers2_request.dart';
import 'package:ksbank_api_smartbank/src/model/check_is_vnp_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_my_shop_payment_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_open_vvip_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_pin_code_trans_request.dart';
import 'package:ksbank_api_smartbank/src/model/check_pin_code_trans_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_preferential_rate_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_term_and_condition_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_valid_pin_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/check_valid_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/check_verify_user_status_response.dart';
import 'package:ksbank_api_smartbank/src/model/city_response.dart';
import 'package:ksbank_api_smartbank/src/model/close_online_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/close_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/close_target_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/close_target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/confirm_create_withdrawal_code_request.dart';
import 'package:ksbank_api_smartbank/src/model/confirm_create_withdrawal_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/contact_created_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/contacts_dto.dart';
import 'package:ksbank_api_smartbank/src/model/core_vnp_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_advance_package_request_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_advance_package_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_advance_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_bill_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_contact_list_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_contact_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_customer_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_customer_request_v2.dart';
import 'package:ksbank_api_smartbank/src/model/create_customer_vn_post_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_customer_vn_post_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_list_contact_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_service_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_supplier_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_supplier_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_target_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_transaction_inquiry_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_transaction_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_transfer_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/create_withdrawal_code_request.dart';
import 'package:ksbank_api_smartbank/src/model/create_withdrawal_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/current_package_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/customer.dart';
import 'package:ksbank_api_smartbank/src/model/delete_pin_code_trans_request.dart';
import 'package:ksbank_api_smartbank/src/model/delete_transfer_request_response.dart';
import 'package:ksbank_api_smartbank/src/model/detail_customer_request.dart';
import 'package:ksbank_api_smartbank/src/model/disbursement_response.dart';
import 'package:ksbank_api_smartbank/src/model/district_response.dart';
import 'package:ksbank_api_smartbank/src/model/e_banking_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/ebanking_packages_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/employee_info.dart';
import 'package:ksbank_api_smartbank/src/model/export_pdf_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/export_pdf_transaction_request.dart';
import 'package:ksbank_api_smartbank/src/model/export_transactions_card_debit_to_pdf_request.dart';
import 'package:ksbank_api_smartbank/src/model/fire_bill_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/fire_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/fire_schedule_pay_invoice_request.dart';
import 'package:ksbank_api_smartbank/src/model/fire_schedule_pay_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/flag.dart';
import 'package:ksbank_api_smartbank/src/model/function_list_item.dart';
import 'package:ksbank_api_smartbank/src/model/gen_cvv_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/gen_cvv_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_acc_card_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_acc_card_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_acc_trans_his_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_account_vip_info_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_advance_ebanking_limit_config_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_advance_transaction_note_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_all_card_wait_open_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_all_province_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_app_module_by_id_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_bill_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_card_product_promotions_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_card_product_promotions_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_card_products_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_card_products_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_cards_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_cities_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_detail_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_detail_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_detail_product_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_disbursements_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_districts_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_due_invoices_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_due_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_e_banking_package_by_customer_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_function_cards_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_function_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_in_progress_advance_transactions_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_interest_receives_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_invoice_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_branch_klb_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_inquiry_step_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_my_shop_payment_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_services_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_suppliers_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_list_trans_inquiry_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_loan_payment_methods_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_loan_process_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_loan_profit_spreadsheet_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_loan_purpose_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_loans_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_owner_card_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_paid_phone_card_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_payment_account_info_internal_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_payment_accounts_response_v2.dart';
import 'package:ksbank_api_smartbank/src/model/get_phone_card_price_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_phone_card_providers_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_pin_code_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_price_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_refund_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_refund_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_saving_accounts_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_saving_histories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_saving_periods_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_saving_types_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_services_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_services_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_share_bill_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_sms_banking_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_sms_banking_fee_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_statement_list_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_statement_list_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_summary_cards_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_summary_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_suppliers_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_suppliers_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_suppliers_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_suppliers_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_tele_info_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_telecom_providers_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_topup_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_topup_value_all_telco_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_topup_values_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_trans_inquiry_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_trans_inquiry_reason_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_transaction_cards_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_transaction_categories_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_transaction_history_ver2_request.dart';
import 'package:ksbank_api_smartbank/src/model/get_transaction_types_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_unpaid_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_values_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/get_version_configs_response.dart';
import 'package:ksbank_api_smartbank/src/model/ignore_bill_reminder_request.dart';
import 'package:ksbank_api_smartbank/src/model/ignore_invoice_debts_reminder_request.dart';
import 'package:ksbank_api_smartbank/src/model/in_progress_advance_transactions_response.dart';
import 'package:ksbank_api_smartbank/src/model/inquiry_customer_request.dart';
import 'package:ksbank_api_smartbank/src/model/inquiry_reason_dto.dart';
import 'package:ksbank_api_smartbank/src/model/interest_receives_response.dart';
import 'package:ksbank_api_smartbank/src/model/invoice_dto.dart';
import 'package:ksbank_api_smartbank/src/model/invoice_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/invoice_schedule_dto.dart';
import 'package:ksbank_api_smartbank/src/model/invoice_service_dto_base.dart';
import 'package:ksbank_api_smartbank/src/model/invoice_supplier_dto.dart';
import 'package:ksbank_api_smartbank/src/model/issue_virtual_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_bank_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_bank_support_scan_viet_qr_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_contact_request.dart';
import 'package:ksbank_api_smartbank/src/model/list_contact_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_transaction_group_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_transaction_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_transaction_template_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_withdrawal_code_active_response.dart';
import 'package:ksbank_api_smartbank/src/model/list_withdrawal_code_expired_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_payment_method_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_process_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_profit_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_purpose_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_refund_response.dart';
import 'package:ksbank_api_smartbank/src/model/loan_refund_schedule.dart';
import 'package:ksbank_api_smartbank/src/model/loan_response.dart';
import 'package:ksbank_api_smartbank/src/model/lock_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/login_app_module_request.dart';
import 'package:ksbank_api_smartbank/src/model/login_app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/lounge_items.dart';
import 'package:ksbank_api_smartbank/src/model/lounge_promotion.dart';
import 'package:ksbank_api_smartbank/src/model/null_type.dart';
import 'package:ksbank_api_smartbank/src/model/offline_saving_account_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/online_saving_certificate_response.dart';
import 'package:ksbank_api_smartbank/src/model/open_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/open_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/open_online_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/open_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/open_vip_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/open_vip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/open_vvip_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/open_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/optional_number_response.dart';
import 'package:ksbank_api_smartbank/src/model/page_dto_list_short_salary_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/page_support_get_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/page_support_get_paid_phone_card_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/page_support_get_pin_code_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/page_support_search_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/page_support_transfer_request_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/paid_phone_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/param.dart';
import 'package:ksbank_api_smartbank/src/model/partial_withdraw_online_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/partial_withdraw_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/pay_invoice_lotus_request.dart';
import 'package:ksbank_api_smartbank/src/model/pay_invoice_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/pay_invoice_request.dart';
import 'package:ksbank_api_smartbank/src/model/pay_invoices_response.dart';
import 'package:ksbank_api_smartbank/src/model/pay_topup_request.dart';
import 'package:ksbank_api_smartbank/src/model/pay_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_account_response_v2.dart';
import 'package:ksbank_api_smartbank/src/model/payment_bill_dto.dart';
import 'package:ksbank_api_smartbank/src/model/payment_bill_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_info.dart';
import 'package:ksbank_api_smartbank/src/model/payment_phone_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_phone_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_pin_code_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_pin_code_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_topup_lotus_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_topup_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/payment_topup_request.dart';
import 'package:ksbank_api_smartbank/src/model/payment_topup_response.dart';
import 'package:ksbank_api_smartbank/src/model/phone_card_providers_response.dart';
import 'package:ksbank_api_smartbank/src/model/phone_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/pin_code_paid_dto.dart';
import 'package:ksbank_api_smartbank/src/model/pin_code_value_dto.dart';
import 'package:ksbank_api_smartbank/src/model/pre_check_issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/pre_check_reset_pin_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/pre_check_reset_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/preview_update_sms_banking_request.dart';
import 'package:ksbank_api_smartbank/src/model/preview_update_sms_banking_response.dart';
import 'package:ksbank_api_smartbank/src/model/promotion_detail.dart';
import 'package:ksbank_api_smartbank/src/model/provider_dto.dart';
import 'package:ksbank_api_smartbank/src/model/province_data.dart';
import 'package:ksbank_api_smartbank/src/model/query_bill_response.dart';
import 'package:ksbank_api_smartbank/src/model/re_issue_virtual_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/re_issue_virtual_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/recent_transaction_request.dart';
import 'package:ksbank_api_smartbank/src/model/refund_loan_request.dart';
import 'package:ksbank_api_smartbank/src/model/refund_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/region.dart';
import 'package:ksbank_api_smartbank/src/model/region_request.dart';
import 'package:ksbank_api_smartbank/src/model/register_loan_request.dart';
import 'package:ksbank_api_smartbank/src/model/register_loan_response.dart';
import 'package:ksbank_api_smartbank/src/model/registered_loan_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/remove_bill_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/remove_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/remove_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/remove_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/reset_count_pin_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/reset_count_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/reset_pin_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/reset_pin_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/resolve_payment_data_app_module_request.dart';
import 'package:ksbank_api_smartbank/src/model/resolve_payment_data_app_module_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_advance_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_bill_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_bill_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_close_online_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_close_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_close_target_saving_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_close_target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_create_schedule_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_create_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_create_transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_create_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_open_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_open_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_open_target_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_open_target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_partial_withdraw_online_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_partial_withdraw_online_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_invoice_lotus_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_invoice_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_payment_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transaction_command_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_v2_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_v3_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_ver3_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_ver4_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank247_ver4_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank_citad_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank_citad_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_inter_bank_citad_v2_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_intra_bank_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_intra_bank_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_intra_bank_v2_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/review_transfer_v2_request.dart';
import 'package:ksbank_api_smartbank/src/model/review_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/salary_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/saving_account_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/saving_history_response.dart';
import 'package:ksbank_api_smartbank/src/model/saving_period_response.dart';
import 'package:ksbank_api_smartbank/src/model/saving_type_response.dart';
import 'package:ksbank_api_smartbank/src/model/schedule.dart';
import 'package:ksbank_api_smartbank/src/model/scheduling_object.dart';
import 'package:ksbank_api_smartbank/src/model/search_vvip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/service_dto.dart';
import 'package:ksbank_api_smartbank/src/model/share_bills_request.dart';
import 'package:ksbank_api_smartbank/src/model/shared_person.dart';
import 'package:ksbank_api_smartbank/src/model/short_salary_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/sms_phone.dart';
import 'package:ksbank_api_smartbank/src/model/statement_group.dart';
import 'package:ksbank_api_smartbank/src/model/statement_item.dart';
import 'package:ksbank_api_smartbank/src/model/summary_credit_card.dart';
import 'package:ksbank_api_smartbank/src/model/supplier_dto.dart';
import 'package:ksbank_api_smartbank/src/model/supplier_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/supplier_pin_code_dto.dart';
import 'package:ksbank_api_smartbank/src/model/supplier_product_dto.dart';
import 'package:ksbank_api_smartbank/src/model/target_saving_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/target_saving_response.dart';
import 'package:ksbank_api_smartbank/src/model/target_saving_trans_request.dart';
import 'package:ksbank_api_smartbank/src/model/target_saving_trans_response.dart';
import 'package:ksbank_api_smartbank/src/model/telecom_dto.dart';
import 'package:ksbank_api_smartbank/src/model/telecom_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/topup_service_dto.dart';
import 'package:ksbank_api_smartbank/src/model/topup_value.dart';
import 'package:ksbank_api_smartbank/src/model/topup_value_dto.dart';
import 'package:ksbank_api_smartbank/src/model/topup_value_lotus_response.dart';
import 'package:ksbank_api_smartbank/src/model/trans_inquiry_dto.dart';
import 'package:ksbank_api_smartbank/src/model/trans_inquiry_step_dto.dart';
import 'package:ksbank_api_smartbank/src/model/trans_next_step.dart';
import 'package:ksbank_api_smartbank/src/model/transaction.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_beneficiary_request.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_beneficiary_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_card.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_categories_type.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_category.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_category_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_item_mapper.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_request.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_schedule_dto.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_statistical.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_statistical_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_statistical_item_dto.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_template_dto.dart';
import 'package:ksbank_api_smartbank/src/model/transaction_type_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_bank_request.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_bank_v2_request.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_bank_ver3_request.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_bank_ver4_request.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_info_qr_code_napas_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_request_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_schedule_detail_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_schedule_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_schedule_update_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_ver3_response.dart';
import 'package:ksbank_api_smartbank/src/model/transfer_ver4_response.dart';
import 'package:ksbank_api_smartbank/src/model/tuition_fee_dto.dart';
import 'package:ksbank_api_smartbank/src/model/unlock_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_auto_payment_card_status_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_contact_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_customer_logging_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_customer_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_ecom_lock_atm_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_ecom_lock_credit_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_lock_services_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_service_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_service_response.dart';
import 'package:ksbank_api_smartbank/src/model/update_sms_banking_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_sms_banking_response.dart';
import 'package:ksbank_api_smartbank/src/model/update_supplier_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_supplier_response.dart';
import 'package:ksbank_api_smartbank/src/model/update_target_saving_account_request.dart';
import 'package:ksbank_api_smartbank/src/model/update_transaction_type_request.dart';
import 'package:ksbank_api_smartbank/src/model/user_cash_limitation_response.dart';
import 'package:ksbank_api_smartbank/src/model/user_enterprise_info.dart';
import 'package:ksbank_api_smartbank/src/model/user_logwork_by_period_info.dart';
import 'package:ksbank_api_smartbank/src/model/user_logwork_item.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_close_online_saving_transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_close_online_saving_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:ksbank_api_smartbank/src/model/version_configs_response.dart';
import 'package:ksbank_api_smartbank/src/model/vip_account_info_dto.dart';
import 'package:ksbank_api_smartbank/src/model/vip_account_info_list_response.dart';
import 'package:ksbank_api_smartbank/src/model/vip_account_response.dart';
import 'package:ksbank_api_smartbank/src/model/vip_account_v1_response.dart';
import 'package:ksbank_api_smartbank/src/model/virtual_to_physical_card_request.dart';
import 'package:ksbank_api_smartbank/src/model/virtual_to_physical_card_response.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_bill_info.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_customer_bill_info.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_electric_water_transfer_request.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_electric_water_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_review_electric_water_request.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_review_electric_water_response.dart';
import 'package:ksbank_api_smartbank/src/model/vnp_service_info.dart';
import 'package:ksbank_api_smartbank/src/model/vvip_open_method.dart';
import 'package:ksbank_api_smartbank/src/model/withdrawal_code_response.dart';

part 'serializers.g.dart';

@SerializersFor([
  AccCardTransInfo,
  AccountDetailRequest,
  AccountDetailResponseMapper,
  AccountInquiryInfo,
  AccountItemMapper,
  AccountNumberInfo,
  AccountRequest,
  AccountResponseMapper,
  AccountStatisticalRequest,
  AccountSummary,
  AccountVipInfoResponse,
  ActiveCreditCardRequest,
  ActiveCreditCardResponse,
  ActiveDebitCardRequest,
  ActiveDebitCardResponse,
  ActiveVirtualCardRequest,
  ActiveVirtualCardResponse,
  AddTransactionTemplateRequest,
  AddVvipCustomerRequest,
  AddVvipCustomerResponse,
  AdvanceEBankingCashLimitationResponse,
  AdvanceFeeInfo,
  AdvanceHistoryDetail,
  AdvanceHistoryInfo,
  AdvanceRequest,
  AdvanceTransactionCommandResponse,
  AdvanceTransferResponse,
  AliasNameRequest,
  ApiResponseDto,
  ApiResponseDtoAccountDetailResponseMapper,
  ApiResponseDtoAccountResponseMapper,
  ApiResponseDtoAccountSummary,
  ApiResponseDtoBaseResponseTransactionScheduleResponse,
  ApiResponseDtoBoolean,
  ApiResponseDtoCalTotalSavingOnlRateResponse,
  ApiResponseDtoCalculateInterestOnlineSavingResponse,
  ApiResponseDtoCalculateInterestPartialWithdrawOnlineSavingResponse,
  ApiResponseDtoCheckMyShopPaymentAccountResponse,
  ApiResponseDtoCheckPreferentialRateResponse,
  ApiResponseDtoCloseOnlineSavingAccountResponse,
  ApiResponseDtoCloseTargetSavingAccountResponse,
  ApiResponseDtoContactsDto,
  ApiResponseDtoCreateScheduleResponse,
  ApiResponseDtoCreateServiceResponse,
  ApiResponseDtoCreateSupplierResponse,
  ApiResponseDtoCreateTargetSavingResponse,
  ApiResponseDtoCustomer,
  ApiResponseDtoFireSchedulePayInvoicesResponse,
  ApiResponseDtoGetCardProductPromotionsResponse,
  ApiResponseDtoGetCardsResponse,
  ApiResponseDtoGetCitiesResponse,
  ApiResponseDtoGetDetailCardResponse,
  ApiResponseDtoGetDetailProductCardResponse,
  ApiResponseDtoGetDisbursementsResponse,
  ApiResponseDtoGetDistrictsResponse,
  ApiResponseDtoGetFunctionCardsResponse,
  ApiResponseDtoGetInterestReceivesResponse,
  ApiResponseDtoGetInvoicesResponse,
  ApiResponseDtoGetListMyShopPaymentAccountResponse,
  ApiResponseDtoGetListServicesResponse,
  ApiResponseDtoGetListSuppliersResponse,
  ApiResponseDtoGetLoanProcessHistoriesResponse,
  ApiResponseDtoGetLoansResponse,
  ApiResponseDtoGetPaymentAccountsResponseV2,
  ApiResponseDtoGetRefundHistoriesResponse,
  ApiResponseDtoGetRefundScheduleResponse,
  ApiResponseDtoGetSavingHistoriesResponse,
  ApiResponseDtoGetSavingPeriodsResponse,
  ApiResponseDtoGetSavingTypesResponse,
  ApiResponseDtoGetStatementListCardResponse,
  ApiResponseDtoGetSummaryCardsResponse,
  ApiResponseDtoGetTeleInfoResponse,
  ApiResponseDtoGetTransactionCardsResponse,
  ApiResponseDtoListBankCodeResponse,
  ApiResponseDtoListBankSupportScanVietQrResponse,
  ApiResponseDtoListBranch,
  ApiResponseDtoListContactResponse,
  ApiResponseDtoListInvoiceScheduleDto,
  ApiResponseDtoListRegion,
  ApiResponseDtoListTelecomDto,
  ApiResponseDtoListTopupValueDto,
  ApiResponseDtoListTransactionGroupResponse,
  ApiResponseDtoListTransactionScheduleResponse,
  ApiResponseDtoListTransactionStatistical,
  ApiResponseDtoListTransactionTemplateResponse,
  ApiResponseDtoLoanDetailResponse,
  ApiResponseDtoLockCardResponse,
  ApiResponseDtoOnlineSavingCertificateResponse,
  ApiResponseDtoOpenAccountResponse,
  ApiResponseDtoOpenOnlineSavingAccountResponse,
  ApiResponseDtoOpenVipAccountResponse,
  ApiResponseDtoPartialWithdrawOnlineSavingAccountResponse,
  ApiResponseDtoPayInvoicesResponse,
  ApiResponseDtoPayTopupResponse,
  ApiResponseDtoPaymentCardResponse,
  ApiResponseDtoRegisteredLoanDetailResponse,
  ApiResponseDtoRemoveScheduleResponse,
  ApiResponseDtoReviewCloseOnlineSavingAccountResponse,
  ApiResponseDtoReviewCloseTargetSavingResponse,
  ApiResponseDtoReviewOnlineSavingAccountResponse,
  ApiResponseDtoReviewOpenTargetSavingAccountResponse,
  ApiResponseDtoReviewPartialWithdrawOnlineSavingAccountResponse,
  ApiResponseDtoReviewPaymentCardResponse,
  ApiResponseDtoReviewPaymentResponse,
  ApiResponseDtoReviewTransferInterBank247Response,
  ApiResponseDtoReviewTransferInterBankCitadResponse,
  ApiResponseDtoReviewTransferIntraBankResponse,
  ApiResponseDtoReviewTransferResponse,
  ApiResponseDtoSavingAccountDetailResponse,
  ApiResponseDtoTargetSavingResponse,
  ApiResponseDtoTargetSavingTransResponse,
  ApiResponseDtoTransactionBeneficiaryResponse,
  ApiResponseDtoTransactionItemMapper,
  ApiResponseDtoTransactionResponse,
  ApiResponseDtoTransactionTemplateDto,
  ApiResponseDtoTransferResponse,
  ApiResponseDtoTransferScheduleDetailResponse,
  ApiResponseDtoTransferScheduleUpdateResponse,
  ApiResponseDtoUnlockCardResponse,
  ApiResponseDtoUpdateServiceResponse,
  ApiResponseDtoUpdateSupplierResponse,
  ApiResponseDtoUpdateTransactionCategoryResponse,
  ApiResponseDtoVipAccountInfoListResponse,
  ApiResponseDtoVipAccountResponse,
  AppModuleDto,
  AppModuleResponse,
  Attachment,
  AutoPayCardStatusResponse,
  BankCodeDto,
  BankFee,
  BankInfo,
  BaseResponseActiveCreditCardResponse,
  BaseResponseActiveDebitCardResponse,
  BaseResponseActiveVirtualCardResponse,
  BaseResponseAddVvipCustomerResponse,
  BaseResponseAdvanceFeeInfo,
  BaseResponseAdvanceHistoryDetail,
  BaseResponseAdvanceTransferResponse,
  BaseResponseAppModuleResponse,
  BaseResponseAutoPayCardStatusResponse,
  BaseResponseCancelTransactionInquiryResponse,
  BaseResponseChangePINCardResponse,
  BaseResponseCheckCardForActiveResponse,
  BaseResponseCheckCashLimitationResponse,
  BaseResponseCheckIsVNPAccountResponse,
  BaseResponseCheckOpenVvipResponse,
  BaseResponseCheckPinCodeTransResponse,
  BaseResponseCheckTermAndConditionResponse,
  BaseResponseCheckValidPINCardResponse,
  BaseResponseCheckVerifyUserStatusResponse,
  BaseResponseConfirmCreateWithdrawalCodeResponse,
  BaseResponseContactCreatedListResponse,
  BaseResponseCreateAdvancePackageRequestResponse,
  BaseResponseCreateAdvanceResponse,
  BaseResponseCreateBillScheduleResponse,
  BaseResponseCreateCustomerVNPostResponse,
  BaseResponseCreateTransactionInquiryResponse,
  BaseResponseCreateTransferRequestResponse,
  BaseResponseCreateTransferResponse,
  BaseResponseCreateWithdrawalCodeResponse,
  BaseResponseCurrentPackageInfoResponse,
  BaseResponseDeletePinCodeTransResponse,
  BaseResponseDeleteTransferRequestResponse,
  BaseResponseEbankingPackagesInfoResponse,
  BaseResponseEmployeeInfo,
  BaseResponseFireBillScheduleResponse,
  BaseResponseGenCVVCardResponse,
  BaseResponseGetAccCardInquiryResponse,
  BaseResponseGetAccCardTransactionResponse,
  BaseResponseGetAccTransHisDetailResponse,
  BaseResponseGetAccountVipInfoListResponse,
  BaseResponseGetAdvanceTransactionNoteResponse,
  BaseResponseGetAllCardWaitOpenResponse,
  BaseResponseGetAllProvinceResponse,
  BaseResponseGetAppModuleByIdResponse,
  BaseResponseGetBillHistoriesResponse,
  BaseResponseGetBillScheduleResponse,
  BaseResponseGetCardProductsResponse,
  BaseResponseGetDueInvoicesLotusResponse,
  BaseResponseGetDueInvoicesResponse,
  BaseResponseGetEBankingPackageByCustomerResponse,
  BaseResponseGetInProgressAdvanceTransactionsResponse,
  BaseResponseGetInvoiceHistoriesResponse,
  BaseResponseGetListBranchKLBResponse,
  BaseResponseGetListInquiryStepResponse,
  BaseResponseGetListTransInquiryResponse,
  BaseResponseGetLoanPaymentMethodsResponse,
  BaseResponseGetLoanProfitSpreadsheetResponse,
  BaseResponseGetLoanPurposeResponse,
  BaseResponseGetOwnerCardInfoResponse,
  BaseResponseGetPaymentAccountInfoInternalResponse,
  BaseResponseGetPhoneCardPriceResponse,
  BaseResponseGetPhoneCardProvidersResponse,
  BaseResponseGetPricePinCodeResponse,
  BaseResponseGetSavingAccountsResponse,
  BaseResponseGetServicesBillResponse,
  BaseResponseGetServicesLotusResponse,
  BaseResponseGetShareBillTransferResponse,
  BaseResponseGetSmsBankingDetailResponse,
  BaseResponseGetSmsBankingFeeResponse,
  BaseResponseGetSuppliersBillResponse,
  BaseResponseGetSuppliersLotusResponse,
  BaseResponseGetSuppliersPinCodeResponse,
  BaseResponseGetSuppliersTopupResponse,
  BaseResponseGetTelecomProvidersLotusResponse,
  BaseResponseGetTopupServiceResponse,
  BaseResponseGetTopupValueAllTelcoResponse,
  BaseResponseGetTopupValuesLotusResponse,
  BaseResponseGetTransInquiryDetailResponse,
  BaseResponseGetTransInquiryReasonResponse,
  BaseResponseGetTransactionCategoriesResponse,
  BaseResponseGetTransactionTypesResponse,
  BaseResponseGetUnpaidBillResponse,
  BaseResponseGetValuesTopupResponse,
  BaseResponseGetVersionConfigsResponse,
  BaseResponseIgnoreBillReminderResponse,
  BaseResponseIgnoreInvoiceDebtsReminderResponse,
  BaseResponseIssueVirtualCardResponse,
  BaseResponseListAdvanceHistoryInfo,
  BaseResponseListProviderDto,
  BaseResponseListUserEnterpriseInfo,
  BaseResponseListWithdrawalCodeActiveResponse,
  BaseResponseListWithdrawalCodeExpiredResponse,
  BaseResponseLoginAppModuleResponse,
  BaseResponseObject,
  BaseResponseOfflineSavingAccountDetailResponse,
  BaseResponseOpenVvipAccountResponse,
  BaseResponseOptionalNumberResponse,
  BaseResponsePageDtoListShortSalaryPeriodInfo,
  BaseResponsePageSupportGetOnlineSavingAccountResponse,
  BaseResponsePageSupportGetPaidPhoneCardHistoryResponse,
  BaseResponsePageSupportGetPinCodeHistoryResponse,
  BaseResponsePageSupportSearchVvipAccountResponse,
  BaseResponsePageSupportTransferRequestDetailResponse,
  BaseResponsePayInvoiceLotusResponse,
  BaseResponsePaymentBillResponse,
  BaseResponsePaymentPhoneCardResponse,
  BaseResponsePaymentPinCodeResponse,
  BaseResponsePaymentTopupLotusResponse,
  BaseResponsePaymentTopupResponse,
  BaseResponsePreCheckIssueVirtualCardResponse,
  BaseResponsePreCheckResetPINCardResponse,
  BaseResponsePreviewUpdateSmsBankingResponse,
  BaseResponseQueryBillResponse,
  BaseResponseReIssueVirtualCardResponse,
  BaseResponseRegisterLoanResponse,
  BaseResponseRemoveBillScheduleResponse,
  BaseResponseResetCountPINCardResponse,
  BaseResponseResetPINCardResponse,
  BaseResponseResolvePaymentDataAppModuleResponse,
  BaseResponseReviewBillScheduleResponse,
  BaseResponseReviewCreateScheduleResponse,
  BaseResponseReviewCreateTransferResponse,
  BaseResponseReviewOpenCardResponse,
  BaseResponseReviewPaymentInvoiceLotusResponse,
  BaseResponseReviewTransferInterBank247Response,
  BaseResponseReviewTransferInterBank247Ver3Response,
  BaseResponseReviewTransferInterBank247Ver4Response,
  BaseResponseReviewTransferInterBankCitadResponse,
  BaseResponseReviewTransferIntraBankResponse,
  BaseResponseReviewTransferResponse,
  BaseResponseReviewVvipAccountResponse,
  BaseResponseShareBillsResponse,
  BaseResponseTransactionResponse,
  BaseResponseTransactionScheduleResponse,
  BaseResponseTransactionStatisticalDetailResponse,
  BaseResponseTransferInfoQrCodeNapasResponse,
  BaseResponseTransferRequestDetailResponse,
  BaseResponseTransferResponse,
  BaseResponseTransferVer3Response,
  BaseResponseTransferVer4Response,
  BaseResponseTuitionFeeDto,
  BaseResponseUpdateAutoPaymentCardStatusResponse,
  BaseResponseUpdateEcomLockATMCardResponse,
  BaseResponseUpdateEcomLockCreditCardResponse,
  BaseResponseUpdateLockServicesCardResponse,
  BaseResponseUpdateSmsBankingResponse,
  BaseResponseUpdateTargetSavingAccountResponse,
  BaseResponseUpdateTransactionTypeResponse,
  BaseResponseUserCashLimitationResponse,
  BaseResponseUserLogworkByPeriodInfo,
  BaseResponseVNPCloseOnlineSavingTransferResponse,
  BaseResponseVipAccountV1Response,
  BaseResponseVirtualToPhysicalCardResponse,
  BaseResponseVnpElectricWaterTransferResponse,
  BaseResponseVnpReviewElectricWaterResponse,
  BillInfoDto,
  BillScheduleDto,
  Branch,
  BranchData,
  BranchRequest,
  CalTotalSavingOnlRateRequest,
  CalTotalSavingOnlRateResponse,
  CalculateInterestOnlineSavingRequest,
  CalculateInterestOnlineSavingResponse,
  CalculateInterestPartialWithdrawOnlineSavingRequest,
  CalculateInterestPartialWithdrawOnlineSavingResponse,
  CancelTransactionInquiryRequest,
  CancelTransactionInquiryResponse,
  CardDetail,
  CardInquiryInfo,
  CardListItem,
  CardProductData,
  CardProductPromotion,
  CardServiceType,
  CardWaitOpenDto,
  CashLimitationResponse,
  ChangePINCardRequest,
  ChangePINCardResponse,
  CheckCardForActiveResponse,
  CheckCashLimitationRequest,
  CheckCashLimitationResponse,
  CheckCashLimitationVers2Request,
  CheckIsVNPAccountResponse,
  CheckMyShopPaymentAccountResponse,
  CheckOpenVvipResponse,
  CheckPinCodeTransRequest,
  CheckPinCodeTransResponse,
  CheckPreferentialRateResponse,
  CheckTermAndConditionResponse,
  CheckValidPINCardRequest,
  CheckValidPINCardResponse,
  CheckVerifyUserStatusResponse,
  CityResponse,
  CloseOnlineSavingAccountRequest,
  CloseOnlineSavingAccountResponse,
  CloseTargetSavingAccountRequest,
  CloseTargetSavingAccountResponse,
  ConfirmCreateWithdrawalCodeRequest,
  ConfirmCreateWithdrawalCodeResponse,
  ContactCreatedListResponse,
  ContactsDto,
  CoreVnpTransactionResponse,
  CreateAdvancePackageRequestRequest,
  CreateAdvancePackageRequestResponse,
  CreateAdvanceResponse,
  CreateBillScheduleRequest,
  CreateBillScheduleResponse,
  CreateContactListRequest,
  CreateContactRequest,
  CreateCustomerRequest,
  CreateCustomerRequestV2,
  CreateCustomerVNPostRequest,
  CreateCustomerVNPostResponse,
  CreateListContactRequest,
  CreateScheduleRequest,
  CreateScheduleResponse,
  CreateServiceRequest,
  CreateServiceResponse,
  CreateSupplierRequest,
  CreateSupplierResponse,
  CreateTargetSavingAccountRequest,
  CreateTargetSavingResponse,
  CreateTransactionInquiryRequest,
  CreateTransactionInquiryResponse,
  CreateTransferRequest,
  CreateTransferRequestResponse,
  CreateTransferResponse,
  CreateWithdrawalCodeRequest,
  CreateWithdrawalCodeResponse,
  CurrentPackageInfoResponse,
  Customer,
  DeletePinCodeTransRequest,
  DeleteTransferRequestResponse,
  DetailCustomerRequest,
  DisbursementResponse,
  DistrictResponse,
  EBankingCashLimitationResponse,
  EbankingPackagesInfoResponse,
  EmployeeInfo,
  ExportPdfCardRequest,
  ExportPdfTransactionRequest,
  ExportTransactionsCardDebitToPdfRequest,
  FireBillScheduleRequest,
  FireBillScheduleResponse,
  FireSchedulePayInvoiceRequest,
  FireSchedulePayInvoicesResponse,
  Flag,
  FunctionListItem,
  GenCVVCardRequest,
  GenCVVCardResponse,
  GetAccCardInquiryResponse,
  GetAccCardTransactionResponse,
  GetAccTransHisDetailResponse,
  GetAccountVipInfoListResponse,
  GetAdvanceEbankingLimitConfigResponse,
  GetAdvanceTransactionNoteResponse,
  GetAllCardWaitOpenResponse,
  GetAllProvinceResponse,
  GetAppModuleByIdResponse,
  GetBillHistoriesResponse,
  GetBillScheduleResponse,
  GetCardProductPromotionsRequest,
  GetCardProductPromotionsResponse,
  GetCardProductsRequest,
  GetCardProductsResponse,
  GetCardsRequest,
  GetCardsResponse,
  GetCitiesResponse,
  GetDetailCardRequest,
  GetDetailCardResponse,
  GetDetailProductCardRequest,
  GetDisbursementsResponse,
  GetDistrictsResponse,
  GetDueInvoicesLotusResponse,
  GetDueInvoicesResponse,
  GetEBankingPackageByCustomerResponse,
  GetFunctionCardsRequest,
  GetFunctionCardsResponse,
  GetInProgressAdvanceTransactionsResponse,
  GetInterestReceivesResponse,
  GetInvoiceHistoriesResponse,
  GetInvoicesResponse,
  GetListBranchKLBResponse,
  GetListInquiryStepResponse,
  GetListMyShopPaymentAccountResponse,
  GetListServicesResponse,
  GetListSuppliersResponse,
  GetListTransInquiryResponse,
  GetLoanPaymentMethodsResponse,
  GetLoanProcessHistoriesResponse,
  GetLoanProfitSpreadsheetResponse,
  GetLoanPurposeResponse,
  GetLoansResponse,
  GetOnlineSavingAccountResponse,
  GetOwnerCardInfoResponse,
  GetPaidPhoneCardHistoryResponse,
  GetPaymentAccountInfoInternalResponse,
  GetPaymentAccountsResponseV2,
  GetPhoneCardPriceResponse,
  GetPhoneCardProvidersResponse,
  GetPinCodeHistoryResponse,
  GetPricePinCodeResponse,
  GetRefundHistoriesResponse,
  GetRefundScheduleResponse,
  GetSavingAccountsResponse,
  GetSavingHistoriesResponse,
  GetSavingPeriodsResponse,
  GetSavingTypesResponse,
  GetServicesBillResponse,
  GetServicesLotusResponse,
  GetShareBillTransferResponse,
  GetSmsBankingDetailResponse,
  GetSmsBankingFeeResponse,
  GetStatementListCardRequest,
  GetStatementListCardResponse,
  GetSummaryCardsRequest,
  GetSummaryCardsResponse,
  GetSuppliersBillResponse,
  GetSuppliersLotusResponse,
  GetSuppliersPinCodeResponse,
  GetSuppliersTopupResponse,
  GetTeleInfoResponse,
  GetTelecomProvidersLotusResponse,
  GetTopupServiceResponse,
  GetTopupValueAllTelcoResponse,
  GetTopupValuesLotusResponse,
  GetTransInquiryDetailResponse,
  GetTransInquiryReasonResponse,
  GetTransactionCardsResponse,
  GetTransactionCategoriesResponse,
  GetTransactionHistoryVer2Request,
  GetTransactionTypesResponse,
  GetUnpaidBillResponse,
  GetValuesTopupResponse,
  GetVersionConfigsResponse,
  IgnoreBillReminderRequest,
  IgnoreInvoiceDebtsReminderRequest,
  InProgressAdvanceTransactionsResponse,
  InquiryCustomerRequest,
  InquiryReasonDto,
  InterestReceivesResponse,
  InvoiceDto,
  InvoiceHistoryResponse,
  InvoiceScheduleDto,
  InvoiceServiceDtoBase,
  InvoiceSupplierDto,
  IssueVirtualCardRequest,
  IssueVirtualCardResponse,
  ListBankCodeResponse,
  ListBankSupportScanVietQrResponse,
  ListContactRequest,
  ListContactResponse,
  ListTransactionGroupResponse,
  ListTransactionScheduleResponse,
  ListTransactionTemplateResponse,
  ListWithdrawalCodeActiveResponse,
  ListWithdrawalCodeExpiredResponse,
  LoanDetailResponse,
  LoanPaymentMethodResponse,
  LoanProcessHistoryResponse,
  LoanProfitResponse,
  LoanPurposeResponse,
  LoanRefundResponse,
  LoanRefundSchedule,
  LoanResponse,
  LockCardRequest,
  LoginAppModuleRequest,
  LoginAppModuleResponse,
  LoungeItems,
  LoungePromotion,
  NullType,
  OfflineSavingAccountDetailResponse,
  OnlineSavingCertificateResponse,
  OpenAccountRequest,
  OpenAccountResponse,
  OpenOnlineSavingAccountRequest,
  OpenOnlineSavingAccountResponse,
  OpenVipAccountRequest,
  OpenVipAccountResponse,
  OpenVvipAccountRequest,
  OpenVvipAccountResponse,
  OptionalNumberResponse,
  PageDtoListShortSalaryPeriodInfo,
  PageSupportGetOnlineSavingAccountResponse,
  PageSupportGetPaidPhoneCardHistoryResponse,
  PageSupportGetPinCodeHistoryResponse,
  PageSupportSearchVvipAccountResponse,
  PageSupportTransferRequestDetailResponse,
  PaidPhoneCardResponse,
  Param,
  PartialWithdrawOnlineSavingAccountRequest,
  PartialWithdrawOnlineSavingAccountResponse,
  PayInvoiceLotusRequest,
  PayInvoiceLotusResponse,
  PayInvoiceRequest,
  PayInvoicesResponse,
  PayTopupRequest,
  PayTopupResponse,
  PaymentAccountResponseV2,
  PaymentBillDto,
  PaymentBillRequest,
  PaymentBillResponse,
  PaymentCardRequest,
  PaymentCardResponse,
  PaymentInfo,
  PaymentPhoneCardRequest,
  PaymentPhoneCardResponse,
  PaymentPinCodeRequest,
  PaymentPinCodeResponse,
  PaymentTopupLotusRequest,
  PaymentTopupLotusResponse,
  PaymentTopupRequest,
  PaymentTopupResponse,
  PhoneCardProvidersResponse,
  PhoneCardResponse,
  PinCodePaidDto,
  PinCodeValueDto,
  PreCheckIssueVirtualCardResponse,
  PreCheckResetPINCardRequest,
  PreCheckResetPINCardResponse,
  PreviewUpdateSmsBankingRequest,
  PreviewUpdateSmsBankingResponse,
  PromotionDetail,
  ProviderDto,
  ProvinceData,
  QueryBillResponse,
  ReIssueVirtualCardRequest,
  ReIssueVirtualCardResponse,
  RecentTransactionRequest,
  RefundLoanRequest,
  RefundScheduleResponse,
  Region,
  RegionRequest,
  RegisterLoanRequest,
  RegisterLoanResponse,
  RegisteredLoanDetailResponse,
  RemoveBillScheduleRequest,
  RemoveBillScheduleResponse,
  RemoveScheduleRequest,
  RemoveScheduleResponse,
  ResetCountPINCardRequest,
  ResetCountPINCardResponse,
  ResetPINCardRequest,
  ResetPINCardResponse,
  ResolvePaymentDataAppModuleRequest,
  ResolvePaymentDataAppModuleResponse,
  ReviewAdvanceTransactionResponse,
  ReviewBillScheduleRequest,
  ReviewBillScheduleResponse,
  ReviewCloseOnlineSavingAccountRequest,
  ReviewCloseOnlineSavingAccountResponse,
  ReviewCloseTargetSavingRequest,
  ReviewCloseTargetSavingResponse,
  ReviewCreateScheduleRequest,
  ReviewCreateScheduleResponse,
  ReviewCreateTransferRequest,
  ReviewCreateTransferResponse,
  ReviewOnlineSavingAccountResponse,
  ReviewOpenCardRequest,
  ReviewOpenCardResponse,
  ReviewOpenTargetSavingAccountRequest,
  ReviewOpenTargetSavingAccountResponse,
  ReviewPartialWithdrawOnlineSavingAccountRequest,
  ReviewPartialWithdrawOnlineSavingAccountResponse,
  ReviewPaymentCardRequest,
  ReviewPaymentCardResponse,
  ReviewPaymentInvoiceLotusRequest,
  ReviewPaymentInvoiceLotusResponse,
  ReviewPaymentRequest,
  ReviewPaymentResponse,
  ReviewSavingAccountRequest,
  ReviewTransactionCommandResponse,
  ReviewTransferInterBank247Request,
  ReviewTransferInterBank247Response,
  ReviewTransferInterBank247V2Request,
  ReviewTransferInterBank247V3Request,
  ReviewTransferInterBank247Ver3Response,
  ReviewTransferInterBank247Ver4Request,
  ReviewTransferInterBank247Ver4Response,
  ReviewTransferInterBankCitadRequest,
  ReviewTransferInterBankCitadResponse,
  ReviewTransferInterBankCitadV2Request,
  ReviewTransferIntraBankRequest,
  ReviewTransferIntraBankResponse,
  ReviewTransferIntraBankV2Request,
  ReviewTransferRequest,
  ReviewTransferResponse,
  ReviewTransferV2Request,
  ReviewVvipAccountResponse,
  SalaryPeriodInfo,
  SavingAccountDetailResponse,
  SavingAccountResponse,
  SavingHistoryResponse,
  SavingPeriodResponse,
  SavingTypeResponse,
  Schedule,
  SchedulingObject,
  SearchVvipAccountResponse,
  ServiceDto,
  ShareBillsRequest,
  SharedPerson,
  ShortSalaryPeriodInfo,
  SmsPhone,
  StatementGroup,
  StatementItem,
  SummaryCreditCard,
  SupplierDto,
  SupplierLotusResponse,
  SupplierPinCodeDto,
  SupplierProductDto,
  TargetSavingAccountResponse,
  TargetSavingResponse,
  TargetSavingTransRequest,
  TargetSavingTransResponse,
  TelecomDto,
  TelecomLotusResponse,
  TopupServiceDto,
  TopupValue,
  TopupValueDto,
  TopupValueLotusResponse,
  TransInquiryDto,
  TransInquiryStepDto,
  TransNextStep,
  Transaction,
  TransactionBeneficiaryRequest,
  TransactionBeneficiaryResponse,
  TransactionCard,
  TransactionCategoriesType,
  TransactionCategory,
  TransactionCategoryResponse,
  TransactionItemMapper,
  TransactionRequest,
  TransactionResponse,
  TransactionScheduleDto,
  TransactionScheduleResponse,
  TransactionStatistical,
  TransactionStatisticalDetailResponse,
  TransactionStatisticalItemDto,
  TransactionTemplateDto,
  TransactionTypeResponse,
  TransferBankRequest,
  TransferBankV2Request,
  TransferBankVer3Request,
  TransferBankVer4Request,
  TransferInfoQrCodeNapasResponse,
  TransferRequest,
  TransferRequestDetailResponse,
  TransferResponse,
  TransferScheduleDetailResponse,
  TransferScheduleResponse,
  TransferScheduleUpdateResponse,
  TransferVer3Response,
  TransferVer4Response,
  TuitionFeeDto,
  UnlockCardRequest,
  UpdateAutoPaymentCardStatusRequest,
  UpdateContactRequest,
  UpdateCustomerLoggingRequest,
  UpdateCustomerRequest,
  UpdateEcomLockATMCardRequest,
  UpdateEcomLockCreditCardRequest,
  UpdateLockServicesCardRequest,
  UpdateServiceRequest,
  UpdateServiceResponse,
  UpdateSmsBankingRequest,
  UpdateSmsBankingResponse,
  UpdateSupplierRequest,
  UpdateSupplierResponse,
  UpdateTargetSavingAccountRequest,
  UpdateTransactionTypeRequest,
  UserCashLimitationResponse,
  UserEnterpriseInfo,
  UserLogworkByPeriodInfo,
  UserLogworkItem,
  VNPCloseOnlineSavingTransferRequest,
  VNPCloseOnlineSavingTransferResponse,
  VerifySoftOtpRequest,
  VersionConfigsResponse,
  VipAccountInfoDto,
  VipAccountInfoListResponse,
  VipAccountResponse,
  VipAccountV1Response,
  VirtualToPhysicalCardRequest,
  VirtualToPhysicalCardResponse,
  VnpBillInfo,
  VnpCustomerBillInfo,
  VnpElectricWaterTransferRequest,
  VnpElectricWaterTransferResponse,
  VnpReviewElectricWaterRequest,
  VnpReviewElectricWaterResponse,
  VnpServiceInfo,
  VvipOpenMethod,
  WithdrawalCodeResponse,
])
Serializers serializers = (_$serializers.toBuilder()
      ..addBuilderFactory(
        const FullType(BuiltSet, [FullType(String)]),
        () => SetBuilder<String>(),
      )
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer()))
    .build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
