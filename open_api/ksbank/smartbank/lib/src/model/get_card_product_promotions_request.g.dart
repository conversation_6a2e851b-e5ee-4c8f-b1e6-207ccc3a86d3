// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_card_product_promotions_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetCardProductPromotionsRequest
    extends GetCardProductPromotionsRequest {
  @override
  final String? productCode;

  factory _$GetCardProductPromotionsRequest(
          [void Function(GetCardProductPromotionsRequestBuilder)? updates]) =>
      (GetCardProductPromotionsRequestBuilder()..update(updates))._build();

  _$GetCardProductPromotionsRequest._({this.productCode}) : super._();
  @override
  GetCardProductPromotionsRequest rebuild(
          void Function(GetCardProductPromotionsRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetCardProductPromotionsRequestBuilder toBuilder() =>
      GetCardProductPromotionsRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetCardProductPromotionsRequest &&
        productCode == other.productCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetCardProductPromotionsRequest')
          ..add('productCode', productCode))
        .toString();
  }
}

class GetCardProductPromotionsRequestBuilder
    implements
        Builder<GetCardProductPromotionsRequest,
            GetCardProductPromotionsRequestBuilder> {
  _$GetCardProductPromotionsRequest? _$v;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  GetCardProductPromotionsRequestBuilder() {
    GetCardProductPromotionsRequest._defaults(this);
  }

  GetCardProductPromotionsRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _productCode = $v.productCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetCardProductPromotionsRequest other) {
    _$v = other as _$GetCardProductPromotionsRequest;
  }

  @override
  void update(void Function(GetCardProductPromotionsRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetCardProductPromotionsRequest build() => _build();

  _$GetCardProductPromotionsRequest _build() {
    final _$result = _$v ??
        _$GetCardProductPromotionsRequest._(
          productCode: productCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
