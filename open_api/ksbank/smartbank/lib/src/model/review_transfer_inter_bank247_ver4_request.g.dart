// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_inter_bank247_ver4_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferInterBank247Ver4Request
    extends ReviewTransferInterBank247Ver4Request {
  @override
  final String? description;
  @override
  final int? transactionGroup;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final int? isAccount;
  @override
  final String? targetBankCode;
  @override
  final num? amount;
  @override
  final String? channelCode;

  factory _$ReviewTransferInterBank247Ver4Request(
          [void Function(ReviewTransferInterBank247Ver4RequestBuilder)?
              updates]) =>
      (ReviewTransferInterBank247Ver4RequestBuilder()..update(updates))
          ._build();

  _$ReviewTransferInterBank247Ver4Request._(
      {this.description,
      this.transactionGroup,
      this.accountNoFrom,
      this.accountNoTo,
      this.isAccount,
      this.targetBankCode,
      this.amount,
      this.channelCode})
      : super._();
  @override
  ReviewTransferInterBank247Ver4Request rebuild(
          void Function(ReviewTransferInterBank247Ver4RequestBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferInterBank247Ver4RequestBuilder toBuilder() =>
      ReviewTransferInterBank247Ver4RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferInterBank247Ver4Request &&
        description == other.description &&
        transactionGroup == other.transactionGroup &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        isAccount == other.isAccount &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount &&
        channelCode == other.channelCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewTransferInterBank247Ver4Request')
          ..add('description', description)
          ..add('transactionGroup', transactionGroup)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('isAccount', isAccount)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount)
          ..add('channelCode', channelCode))
        .toString();
  }
}

class ReviewTransferInterBank247Ver4RequestBuilder
    implements
        Builder<ReviewTransferInterBank247Ver4Request,
            ReviewTransferInterBank247Ver4RequestBuilder> {
  _$ReviewTransferInterBank247Ver4Request? _$v;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  ReviewTransferInterBank247Ver4RequestBuilder() {
    ReviewTransferInterBank247Ver4Request._defaults(this);
  }

  ReviewTransferInterBank247Ver4RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _description = $v.description;
      _transactionGroup = $v.transactionGroup;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _isAccount = $v.isAccount;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _channelCode = $v.channelCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferInterBank247Ver4Request other) {
    _$v = other as _$ReviewTransferInterBank247Ver4Request;
  }

  @override
  void update(
      void Function(ReviewTransferInterBank247Ver4RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferInterBank247Ver4Request build() => _build();

  _$ReviewTransferInterBank247Ver4Request _build() {
    final _$result = _$v ??
        _$ReviewTransferInterBank247Ver4Request._(
          description: description,
          transactionGroup: transactionGroup,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          isAccount: isAccount,
          targetBankCode: targetBankCode,
          amount: amount,
          channelCode: channelCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
