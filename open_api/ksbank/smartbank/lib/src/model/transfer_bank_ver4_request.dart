//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_smartbank/src/model/date.dart';
import 'package:ksbank_api_smartbank/src/model/scheduling_object.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'transfer_bank_ver4_request.g.dart';

/// TransferBankVer4Request
///
/// Properties:
/// * [transactionNo] - <PERSON><PERSON> giao dịch, được trả về khi gọi api review
/// * [whoCharge] - Ai là người trả phí: 1: người chuyển trả, 0 người nhận trả
/// * [chargeAmount] - Số tiền phí giao dịch
/// * [isAccount] - Chuy<PERSON><PERSON> khoảng sang tài khoản hay sang thẻ: 1: sang tà<PERSON> kho<PERSON>, 0 sang thẻ (hoặc nếu chuyển citad thì 0: CMND/Hộ chiếu)
/// * [bankCodeId] - Mã bank code id trong bảng bank
/// * [aliasName] - Tên gợi nhớ của người được chuyển đến
/// * [is247] - Chuyển nhanh (NAPAS) hay chuyển chậm (CITAD), 1: Chuyển nhanh, 0: chuyển chậm
/// * [isNow] - 1: chuyển ngay, 0: đặt lịch
/// * [isSaveToTemplate] - Lưu mẫu giao dịch, 1: Lưu, 0: Không lưu
/// * [description] - Nội dung chuyển khoản
/// * [transactionCategoryId] - Phân loại giao dịch
/// * [verifySoftOtp]
/// * [schedule]
/// * [regionId] - Mã tỉnh thành phố - dùng khi chuyển Citad, bắt buộc
/// * [bankId] - Mã ngân hàng - dùng khi chuyển Citad, bắc buộc
/// * [branchId] - Mã chi nhánh - dùng khi chuyển Citad, bắt buộc
/// * [branchName] - Tên chi nhánh - dùng khi chuyển Citad, chỉ bắt buộc khi là ngân hàng nông nghiệp
/// * [beneficiaryName] - Tên người thụ hưởng - dùng khi chuyển Citad, bắt buộc
/// * [scheduleId] - Nhà cung cấp dịch vụ
/// * [channelCode] - Item xác định xem chuyển tiền qua vietQr hay qua cái khác
/// * [idCard] - Số CMND - dùng khi chuyển Citad, bắt buộc nếu là chuyển theo CMND/Hộ chiếu
/// * [issueDate] - Ngày cấp CMND - dùng khi chuyển Citad
/// * [placeBy] - Nơi cấp CMND - dùng khi chuyển Citad
@BuiltValue()
abstract class TransferBankVer4Request
    implements Built<TransferBankVer4Request, TransferBankVer4RequestBuilder> {
  /// Mã giao dịch, được trả về khi gọi api review
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Ai là người trả phí: 1: người chuyển trả, 0 người nhận trả
  @BuiltValueField(wireName: r'whoCharge')
  int? get whoCharge;

  /// Số tiền phí giao dịch
  @BuiltValueField(wireName: r'chargeAmount')
  int? get chargeAmount;

  /// Chuyển khoảng sang tài khoản hay sang thẻ: 1: sang tài khoản, 0 sang thẻ (hoặc nếu chuyển citad thì 0: CMND/Hộ chiếu)
  @BuiltValueField(wireName: r'isAccount')
  int? get isAccount;

  /// Mã bank code id trong bảng bank
  @BuiltValueField(wireName: r'bankCodeId')
  String? get bankCodeId;

  /// Tên gợi nhớ của người được chuyển đến
  @BuiltValueField(wireName: r'aliasName')
  String? get aliasName;

  /// Chuyển nhanh (NAPAS) hay chuyển chậm (CITAD), 1: Chuyển nhanh, 0: chuyển chậm
  @BuiltValueField(wireName: r'is247')
  int? get is247;

  /// 1: chuyển ngay, 0: đặt lịch
  @BuiltValueField(wireName: r'isNow')
  int? get isNow;

  /// Lưu mẫu giao dịch, 1: Lưu, 0: Không lưu
  @BuiltValueField(wireName: r'isSaveToTemplate')
  int? get isSaveToTemplate;

  /// Nội dung chuyển khoản
  @BuiltValueField(wireName: r'description')
  String? get description;

  /// Phân loại giao dịch
  @BuiltValueField(wireName: r'transactionCategoryId')
  int? get transactionCategoryId;

  @BuiltValueField(wireName: r'verifySoftOtp')
  VerifySoftOtpRequest? get verifySoftOtp;

  @BuiltValueField(wireName: r'schedule')
  SchedulingObject? get schedule;

  /// Mã tỉnh thành phố - dùng khi chuyển Citad, bắt buộc
  @BuiltValueField(wireName: r'regionId')
  int? get regionId;

  /// Mã ngân hàng - dùng khi chuyển Citad, bắc buộc
  @BuiltValueField(wireName: r'bankId')
  int? get bankId;

  /// Mã chi nhánh - dùng khi chuyển Citad, bắt buộc
  @BuiltValueField(wireName: r'branchId')
  String? get branchId;

  /// Tên chi nhánh - dùng khi chuyển Citad, chỉ bắt buộc khi là ngân hàng nông nghiệp
  @BuiltValueField(wireName: r'branchName')
  String? get branchName;

  /// Tên người thụ hưởng - dùng khi chuyển Citad, bắt buộc
  @BuiltValueField(wireName: r'beneficiaryName')
  String? get beneficiaryName;

  /// Nhà cung cấp dịch vụ
  @BuiltValueField(wireName: r'scheduleId')
  String? get scheduleId;

  /// Item xác định xem chuyển tiền qua vietQr hay qua cái khác
  @BuiltValueField(wireName: r'channelCode')
  String? get channelCode;

  /// Số CMND - dùng khi chuyển Citad, bắt buộc nếu là chuyển theo CMND/Hộ chiếu
  @BuiltValueField(wireName: r'idCard')
  String? get idCard;

  /// Ngày cấp CMND - dùng khi chuyển Citad
  @BuiltValueField(wireName: r'issueDate')
  Date? get issueDate;

  /// Nơi cấp CMND - dùng khi chuyển Citad
  @BuiltValueField(wireName: r'placeBy')
  String? get placeBy;

  TransferBankVer4Request._();

  factory TransferBankVer4Request(
          [void updates(TransferBankVer4RequestBuilder b)]) =
      _$TransferBankVer4Request;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TransferBankVer4RequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TransferBankVer4Request> get serializer =>
      _$TransferBankVer4RequestSerializer();
}

class _$TransferBankVer4RequestSerializer
    implements PrimitiveSerializer<TransferBankVer4Request> {
  @override
  final Iterable<Type> types = const [
    TransferBankVer4Request,
    _$TransferBankVer4Request
  ];

  @override
  final String wireName = r'TransferBankVer4Request';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TransferBankVer4Request object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.whoCharge != null) {
      yield r'whoCharge';
      yield serializers.serialize(
        object.whoCharge,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.chargeAmount != null) {
      yield r'chargeAmount';
      yield serializers.serialize(
        object.chargeAmount,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.isAccount != null) {
      yield r'isAccount';
      yield serializers.serialize(
        object.isAccount,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.bankCodeId != null) {
      yield r'bankCodeId';
      yield serializers.serialize(
        object.bankCodeId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.aliasName != null) {
      yield r'aliasName';
      yield serializers.serialize(
        object.aliasName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.is247 != null) {
      yield r'is247';
      yield serializers.serialize(
        object.is247,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.isNow != null) {
      yield r'isNow';
      yield serializers.serialize(
        object.isNow,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.isSaveToTemplate != null) {
      yield r'isSaveToTemplate';
      yield serializers.serialize(
        object.isSaveToTemplate,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionCategoryId != null) {
      yield r'transactionCategoryId';
      yield serializers.serialize(
        object.transactionCategoryId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.verifySoftOtp != null) {
      yield r'verifySoftOtp';
      yield serializers.serialize(
        object.verifySoftOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
    if (object.schedule != null) {
      yield r'schedule';
      yield serializers.serialize(
        object.schedule,
        specifiedType: const FullType.nullable(SchedulingObject),
      );
    }
    if (object.regionId != null) {
      yield r'regionId';
      yield serializers.serialize(
        object.regionId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.bankId != null) {
      yield r'bankId';
      yield serializers.serialize(
        object.bankId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.branchId != null) {
      yield r'branchId';
      yield serializers.serialize(
        object.branchId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.branchName != null) {
      yield r'branchName';
      yield serializers.serialize(
        object.branchName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.beneficiaryName != null) {
      yield r'beneficiaryName';
      yield serializers.serialize(
        object.beneficiaryName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.scheduleId != null) {
      yield r'scheduleId';
      yield serializers.serialize(
        object.scheduleId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.channelCode != null) {
      yield r'channelCode';
      yield serializers.serialize(
        object.channelCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCard != null) {
      yield r'idCard';
      yield serializers.serialize(
        object.idCard,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.issueDate != null) {
      yield r'issueDate';
      yield serializers.serialize(
        object.issueDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.placeBy != null) {
      yield r'placeBy';
      yield serializers.serialize(
        object.placeBy,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TransferBankVer4Request object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TransferBankVer4RequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'whoCharge':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.whoCharge = valueDes;
          break;
        case r'chargeAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.chargeAmount = valueDes;
          break;
        case r'isAccount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isAccount = valueDes;
          break;
        case r'bankCodeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCodeId = valueDes;
          break;
        case r'aliasName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aliasName = valueDes;
          break;
        case r'is247':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.is247 = valueDes;
          break;
        case r'isNow':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isNow = valueDes;
          break;
        case r'isSaveToTemplate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isSaveToTemplate = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'transactionCategoryId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.transactionCategoryId = valueDes;
          break;
        case r'verifySoftOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.verifySoftOtp.replace(valueDes);
          break;
        case r'schedule':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(SchedulingObject),
          ) as SchedulingObject?;
          if (valueDes == null) continue;
          result.schedule.replace(valueDes);
          break;
        case r'regionId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.regionId = valueDes;
          break;
        case r'bankId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.bankId = valueDes;
          break;
        case r'branchId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.branchId = valueDes;
          break;
        case r'branchName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.branchName = valueDes;
          break;
        case r'beneficiaryName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.beneficiaryName = valueDes;
          break;
        case r'scheduleId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.scheduleId = valueDes;
          break;
        case r'channelCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.channelCode = valueDes;
          break;
        case r'idCard':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCard = valueDes;
          break;
        case r'issueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.issueDate = valueDes;
          break;
        case r'placeBy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.placeBy = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TransferBankVer4Request deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TransferBankVer4RequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
