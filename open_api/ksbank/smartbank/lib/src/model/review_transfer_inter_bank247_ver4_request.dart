//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_transfer_inter_bank247_ver4_request.g.dart';

/// ReviewTransferInterBank247Ver4Request
///
/// Properties:
/// * [description]
/// * [transactionGroup]
/// * [accountNoFrom]
/// * [accountNoTo]
/// * [isAccount]
/// * [targetBankCode]
/// * [amount]
/// * [channelCode]
@BuiltValue()
abstract class ReviewTransferInterBank247Ver4Request
    implements
        Built<ReviewTransferInterBank247Ver4Request,
            ReviewTransferInterBank247Ver4RequestBuilder> {
  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'transactionGroup')
  int? get transactionGroup;

  @BuiltValueField(wireName: r'accountNoFrom')
  String? get accountNoFrom;

  @BuiltValueField(wireName: r'accountNoTo')
  String? get accountNoTo;

  @BuiltValueField(wireName: r'isAccount')
  int? get isAccount;

  @BuiltValueField(wireName: r'targetBankCode')
  String? get targetBankCode;

  @BuiltValueField(wireName: r'amount')
  num? get amount;

  @BuiltValueField(wireName: r'channelCode')
  String? get channelCode;

  ReviewTransferInterBank247Ver4Request._();

  factory ReviewTransferInterBank247Ver4Request(
          [void updates(ReviewTransferInterBank247Ver4RequestBuilder b)]) =
      _$ReviewTransferInterBank247Ver4Request;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewTransferInterBank247Ver4RequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewTransferInterBank247Ver4Request> get serializer =>
      _$ReviewTransferInterBank247Ver4RequestSerializer();
}

class _$ReviewTransferInterBank247Ver4RequestSerializer
    implements PrimitiveSerializer<ReviewTransferInterBank247Ver4Request> {
  @override
  final Iterable<Type> types = const [
    ReviewTransferInterBank247Ver4Request,
    _$ReviewTransferInterBank247Ver4Request
  ];

  @override
  final String wireName = r'ReviewTransferInterBank247Ver4Request';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewTransferInterBank247Ver4Request object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionGroup != null) {
      yield r'transactionGroup';
      yield serializers.serialize(
        object.transactionGroup,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.accountNoFrom != null) {
      yield r'accountNoFrom';
      yield serializers.serialize(
        object.accountNoFrom,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNoTo != null) {
      yield r'accountNoTo';
      yield serializers.serialize(
        object.accountNoTo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.isAccount != null) {
      yield r'isAccount';
      yield serializers.serialize(
        object.isAccount,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.targetBankCode != null) {
      yield r'targetBankCode';
      yield serializers.serialize(
        object.targetBankCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.channelCode != null) {
      yield r'channelCode';
      yield serializers.serialize(
        object.channelCode,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewTransferInterBank247Ver4Request object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewTransferInterBank247Ver4RequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'transactionGroup':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.transactionGroup = valueDes;
          break;
        case r'accountNoFrom':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNoFrom = valueDes;
          break;
        case r'accountNoTo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNoTo = valueDes;
          break;
        case r'isAccount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.isAccount = valueDes;
          break;
        case r'targetBankCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.targetBankCode = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'channelCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.channelCode = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewTransferInterBank247Ver4Request deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewTransferInterBank247Ver4RequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
