//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_smartbank/src/model/advance_transfer_response.dart';
import 'package:ksbank_api_smartbank/src/model/transaction.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'transfer_ver4_response.g.dart';

/// TransferVer4Response
///
/// Properties:
/// * [transaction]
/// * [advanceTransferResponse]
@BuiltValue()
abstract class TransferVer4Response
    implements Built<TransferVer4Response, TransferVer4ResponseBuilder> {
  @BuiltValueField(wireName: r'transaction')
  Transaction? get transaction;

  @BuiltValueField(wireName: r'advanceTransferResponse')
  AdvanceTransferResponse? get advanceTransferResponse;

  TransferVer4Response._();

  factory TransferVer4Response([void updates(TransferVer4ResponseBuilder b)]) =
      _$TransferVer4Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TransferVer4ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TransferVer4Response> get serializer =>
      _$TransferVer4ResponseSerializer();
}

class _$TransferVer4ResponseSerializer
    implements PrimitiveSerializer<TransferVer4Response> {
  @override
  final Iterable<Type> types = const [
    TransferVer4Response,
    _$TransferVer4Response
  ];

  @override
  final String wireName = r'TransferVer4Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TransferVer4Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transaction != null) {
      yield r'transaction';
      yield serializers.serialize(
        object.transaction,
        specifiedType: const FullType.nullable(Transaction),
      );
    }
    if (object.advanceTransferResponse != null) {
      yield r'advanceTransferResponse';
      yield serializers.serialize(
        object.advanceTransferResponse,
        specifiedType: const FullType.nullable(AdvanceTransferResponse),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TransferVer4Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TransferVer4ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transaction':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Transaction),
          ) as Transaction?;
          if (valueDes == null) continue;
          result.transaction.replace(valueDes);
          break;
        case r'advanceTransferResponse':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(AdvanceTransferResponse),
          ) as AdvanceTransferResponse?;
          if (valueDes == null) continue;
          result.advanceTransferResponse.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TransferVer4Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TransferVer4ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
