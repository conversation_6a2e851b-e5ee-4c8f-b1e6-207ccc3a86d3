//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_smartbank/src/model/review_advance_transaction_response.dart';
import 'package:ksbank_api_smartbank/src/model/trans_next_step.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_transfer_inter_bank247_ver4_response.g.dart';

/// ReviewTransferInterBank247Ver4Response
///
/// Properties:
/// * [cifNumber]
/// * [transactionNumber]
/// * [transactionName]
/// * [transactionGroup]
/// * [sourceAccountNumber]
/// * [targetAccountNumber]
/// * [targetAccountName]
/// * [advanceTransaction]
/// * [amount]
/// * [fee]
/// * [tax]
/// * [transNextStep]
/// * [content] - Nội dung content app show nếu có
/// * [targetAccountType]
@BuiltValue()
abstract class ReviewTransferInterBank247Ver4Response
    implements
        Built<ReviewTransferInterBank247Ver4Response,
            ReviewTransferInterBank247Ver4ResponseBuilder> {
  @BuiltValueField(wireName: r'cifNumber')
  String? get cifNumber;

  @BuiltValueField(wireName: r'transactionNumber')
  String? get transactionNumber;

  @BuiltValueField(wireName: r'transactionName')
  String? get transactionName;

  @BuiltValueField(wireName: r'transactionGroup')
  int? get transactionGroup;

  @BuiltValueField(wireName: r'sourceAccountNumber')
  String? get sourceAccountNumber;

  @BuiltValueField(wireName: r'targetAccountNumber')
  String? get targetAccountNumber;

  @BuiltValueField(wireName: r'targetAccountName')
  String? get targetAccountName;

  @BuiltValueField(wireName: r'advanceTransaction')
  ReviewAdvanceTransactionResponse? get advanceTransaction;

  @BuiltValueField(wireName: r'amount')
  num? get amount;

  @BuiltValueField(wireName: r'fee')
  int? get fee;

  @BuiltValueField(wireName: r'tax')
  int? get tax;

  @BuiltValueField(wireName: r'transNextStep')
  TransNextStep? get transNextStep;
  // enum transNextStepEnum {  NONE,  ENABLE_FACE_ID,  SHOW_GUIDE,  SHOW_GUIDE_GTTT_EXPIRED,  };

  /// Nội dung content app show nếu có
  @BuiltValueField(wireName: r'content')
  String? get content;

  @BuiltValueField(wireName: r'targetAccountType')
  int? get targetAccountType;

  ReviewTransferInterBank247Ver4Response._();

  factory ReviewTransferInterBank247Ver4Response(
          [void updates(ReviewTransferInterBank247Ver4ResponseBuilder b)]) =
      _$ReviewTransferInterBank247Ver4Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewTransferInterBank247Ver4ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewTransferInterBank247Ver4Response> get serializer =>
      _$ReviewTransferInterBank247Ver4ResponseSerializer();
}

class _$ReviewTransferInterBank247Ver4ResponseSerializer
    implements PrimitiveSerializer<ReviewTransferInterBank247Ver4Response> {
  @override
  final Iterable<Type> types = const [
    ReviewTransferInterBank247Ver4Response,
    _$ReviewTransferInterBank247Ver4Response
  ];

  @override
  final String wireName = r'ReviewTransferInterBank247Ver4Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewTransferInterBank247Ver4Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.cifNumber != null) {
      yield r'cifNumber';
      yield serializers.serialize(
        object.cifNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNumber != null) {
      yield r'transactionNumber';
      yield serializers.serialize(
        object.transactionNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionName != null) {
      yield r'transactionName';
      yield serializers.serialize(
        object.transactionName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionGroup != null) {
      yield r'transactionGroup';
      yield serializers.serialize(
        object.transactionGroup,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.sourceAccountNumber != null) {
      yield r'sourceAccountNumber';
      yield serializers.serialize(
        object.sourceAccountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.targetAccountNumber != null) {
      yield r'targetAccountNumber';
      yield serializers.serialize(
        object.targetAccountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.targetAccountName != null) {
      yield r'targetAccountName';
      yield serializers.serialize(
        object.targetAccountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.advanceTransaction != null) {
      yield r'advanceTransaction';
      yield serializers.serialize(
        object.advanceTransaction,
        specifiedType:
            const FullType.nullable(ReviewAdvanceTransactionResponse),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.fee != null) {
      yield r'fee';
      yield serializers.serialize(
        object.fee,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.tax != null) {
      yield r'tax';
      yield serializers.serialize(
        object.tax,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.transNextStep != null) {
      yield r'transNextStep';
      yield serializers.serialize(
        object.transNextStep,
        specifiedType: const FullType.nullable(TransNextStep),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.targetAccountType != null) {
      yield r'targetAccountType';
      yield serializers.serialize(
        object.targetAccountType,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewTransferInterBank247Ver4Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewTransferInterBank247Ver4ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cifNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cifNumber = valueDes;
          break;
        case r'transactionNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNumber = valueDes;
          break;
        case r'transactionName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionName = valueDes;
          break;
        case r'transactionGroup':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.transactionGroup = valueDes;
          break;
        case r'sourceAccountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sourceAccountNumber = valueDes;
          break;
        case r'targetAccountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.targetAccountNumber = valueDes;
          break;
        case r'targetAccountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.targetAccountName = valueDes;
          break;
        case r'advanceTransaction':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(ReviewAdvanceTransactionResponse),
          ) as ReviewAdvanceTransactionResponse?;
          if (valueDes == null) continue;
          result.advanceTransaction.replace(valueDes);
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.fee = valueDes;
          break;
        case r'tax':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.tax = valueDes;
          break;
        case r'transNextStep':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(TransNextStep),
          ) as TransNextStep?;
          if (valueDes == null) continue;
          result.transNextStep = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.content = valueDes;
          break;
        case r'targetAccountType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.targetAccountType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewTransferInterBank247Ver4Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewTransferInterBank247Ver4ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
