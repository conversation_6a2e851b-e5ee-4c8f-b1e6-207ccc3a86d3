// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_ver4_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferVer4Response extends TransferVer4Response {
  @override
  final Transaction? transaction;
  @override
  final AdvanceTransferResponse? advanceTransferResponse;

  factory _$TransferVer4Response(
          [void Function(TransferVer4ResponseBuilder)? updates]) =>
      (TransferVer4ResponseBuilder()..update(updates))._build();

  _$TransferVer4Response._({this.transaction, this.advanceTransferResponse})
      : super._();
  @override
  TransferVer4Response rebuild(
          void Function(TransferVer4ResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferVer4ResponseBuilder toBuilder() =>
      TransferVer4ResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferVer4Response &&
        transaction == other.transaction &&
        advanceTransferResponse == other.advanceTransferResponse;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transaction.hashCode);
    _$hash = $jc(_$hash, advanceTransferResponse.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferVer4Response')
          ..add('transaction', transaction)
          ..add('advanceTransferResponse', advanceTransferResponse))
        .toString();
  }
}

class TransferVer4ResponseBuilder
    implements Builder<TransferVer4Response, TransferVer4ResponseBuilder> {
  _$TransferVer4Response? _$v;

  TransactionBuilder? _transaction;
  TransactionBuilder get transaction =>
      _$this._transaction ??= TransactionBuilder();
  set transaction(TransactionBuilder? transaction) =>
      _$this._transaction = transaction;

  AdvanceTransferResponseBuilder? _advanceTransferResponse;
  AdvanceTransferResponseBuilder get advanceTransferResponse =>
      _$this._advanceTransferResponse ??= AdvanceTransferResponseBuilder();
  set advanceTransferResponse(
          AdvanceTransferResponseBuilder? advanceTransferResponse) =>
      _$this._advanceTransferResponse = advanceTransferResponse;

  TransferVer4ResponseBuilder() {
    TransferVer4Response._defaults(this);
  }

  TransferVer4ResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transaction = $v.transaction?.toBuilder();
      _advanceTransferResponse = $v.advanceTransferResponse?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferVer4Response other) {
    _$v = other as _$TransferVer4Response;
  }

  @override
  void update(void Function(TransferVer4ResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferVer4Response build() => _build();

  _$TransferVer4Response _build() {
    _$TransferVer4Response _$result;
    try {
      _$result = _$v ??
          _$TransferVer4Response._(
            transaction: _transaction?.build(),
            advanceTransferResponse: _advanceTransferResponse?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transaction';
        _transaction?.build();
        _$failedField = 'advanceTransferResponse';
        _advanceTransferResponse?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferVer4Response', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
