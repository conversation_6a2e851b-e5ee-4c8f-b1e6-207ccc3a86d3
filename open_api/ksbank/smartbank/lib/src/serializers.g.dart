// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (Serializers().toBuilder()
      ..add(AccCardTransInfo.serializer)
      ..add(AccountDetailRequest.serializer)
      ..add(AccountDetailResponseMapper.serializer)
      ..add(AccountInquiryInfo.serializer)
      ..add(AccountItemMapper.serializer)
      ..add(AccountNumberInfo.serializer)
      ..add(AccountRequest.serializer)
      ..add(AccountResponseMapper.serializer)
      ..add(AccountStatisticalRequest.serializer)
      ..add(AccountSummary.serializer)
      ..add(AccountVipInfoResponse.serializer)
      ..add(ActiveCreditCardRequest.serializer)
      ..add(ActiveCreditCardResponse.serializer)
      ..add(ActiveDebitCardRequest.serializer)
      ..add(ActiveDebitCardResponse.serializer)
      ..add(ActiveVirtualCardRequest.serializer)
      ..add(ActiveVirtualCardResponse.serializer)
      ..add(AddTransactionTemplateRequest.serializer)
      ..add(AddVvipCustomerRequest.serializer)
      ..add(AddVvipCustomerResponse.serializer)
      ..add(AdvanceEBankingCashLimitationResponse.serializer)
      ..add(AdvanceFeeInfo.serializer)
      ..add(AdvanceHistoryDetail.serializer)
      ..add(AdvanceHistoryDetailCurrencyEnum.serializer)
      ..add(AdvanceHistoryDetailPaymentStatusEnum.serializer)
      ..add(AdvanceHistoryDetailStatusEnum.serializer)
      ..add(AdvanceHistoryInfo.serializer)
      ..add(AdvanceHistoryInfoCurrencyEnum.serializer)
      ..add(AdvanceHistoryInfoPaymentStatusEnum.serializer)
      ..add(AdvanceHistoryInfoStatusEnum.serializer)
      ..add(AdvanceRequest.serializer)
      ..add(AdvanceTransactionCommandResponse.serializer)
      ..add(AdvanceTransactionCommandResponseCommandStatusEnum.serializer)
      ..add(AdvanceTransferResponse.serializer)
      ..add(AdvanceTransferResponseStatusEnum.serializer)
      ..add(AliasNameRequest.serializer)
      ..add(ApiResponseDto.serializer)
      ..add(ApiResponseDtoAccountDetailResponseMapper.serializer)
      ..add(ApiResponseDtoAccountResponseMapper.serializer)
      ..add(ApiResponseDtoAccountSummary.serializer)
      ..add(ApiResponseDtoBaseResponseTransactionScheduleResponse.serializer)
      ..add(ApiResponseDtoBoolean.serializer)
      ..add(ApiResponseDtoCalTotalSavingOnlRateResponse.serializer)
      ..add(ApiResponseDtoCalculateInterestOnlineSavingResponse.serializer)
      ..add(ApiResponseDtoCalculateInterestPartialWithdrawOnlineSavingResponse
          .serializer)
      ..add(ApiResponseDtoCheckMyShopPaymentAccountResponse.serializer)
      ..add(ApiResponseDtoCheckPreferentialRateResponse.serializer)
      ..add(ApiResponseDtoCloseOnlineSavingAccountResponse.serializer)
      ..add(ApiResponseDtoCloseTargetSavingAccountResponse.serializer)
      ..add(ApiResponseDtoContactsDto.serializer)
      ..add(ApiResponseDtoCreateScheduleResponse.serializer)
      ..add(ApiResponseDtoCreateServiceResponse.serializer)
      ..add(ApiResponseDtoCreateSupplierResponse.serializer)
      ..add(ApiResponseDtoCreateTargetSavingResponse.serializer)
      ..add(ApiResponseDtoCustomer.serializer)
      ..add(ApiResponseDtoFireSchedulePayInvoicesResponse.serializer)
      ..add(ApiResponseDtoGetCardProductPromotionsResponse.serializer)
      ..add(ApiResponseDtoGetCardsResponse.serializer)
      ..add(ApiResponseDtoGetCitiesResponse.serializer)
      ..add(ApiResponseDtoGetDetailCardResponse.serializer)
      ..add(ApiResponseDtoGetDetailProductCardResponse.serializer)
      ..add(ApiResponseDtoGetDisbursementsResponse.serializer)
      ..add(ApiResponseDtoGetDistrictsResponse.serializer)
      ..add(ApiResponseDtoGetFunctionCardsResponse.serializer)
      ..add(ApiResponseDtoGetInterestReceivesResponse.serializer)
      ..add(ApiResponseDtoGetInvoicesResponse.serializer)
      ..add(ApiResponseDtoGetListMyShopPaymentAccountResponse.serializer)
      ..add(ApiResponseDtoGetListServicesResponse.serializer)
      ..add(ApiResponseDtoGetListSuppliersResponse.serializer)
      ..add(ApiResponseDtoGetLoanProcessHistoriesResponse.serializer)
      ..add(ApiResponseDtoGetLoansResponse.serializer)
      ..add(ApiResponseDtoGetPaymentAccountsResponseV2.serializer)
      ..add(ApiResponseDtoGetRefundHistoriesResponse.serializer)
      ..add(ApiResponseDtoGetRefundScheduleResponse.serializer)
      ..add(ApiResponseDtoGetSavingHistoriesResponse.serializer)
      ..add(ApiResponseDtoGetSavingPeriodsResponse.serializer)
      ..add(ApiResponseDtoGetSavingTypesResponse.serializer)
      ..add(ApiResponseDtoGetStatementListCardResponse.serializer)
      ..add(ApiResponseDtoGetSummaryCardsResponse.serializer)
      ..add(ApiResponseDtoGetTeleInfoResponse.serializer)
      ..add(ApiResponseDtoGetTransactionCardsResponse.serializer)
      ..add(ApiResponseDtoListBankCodeResponse.serializer)
      ..add(ApiResponseDtoListBankSupportScanVietQrResponse.serializer)
      ..add(ApiResponseDtoListBranch.serializer)
      ..add(ApiResponseDtoListContactResponse.serializer)
      ..add(ApiResponseDtoListInvoiceScheduleDto.serializer)
      ..add(ApiResponseDtoListRegion.serializer)
      ..add(ApiResponseDtoListTelecomDto.serializer)
      ..add(ApiResponseDtoListTopupValueDto.serializer)
      ..add(ApiResponseDtoListTransactionGroupResponse.serializer)
      ..add(ApiResponseDtoListTransactionScheduleResponse.serializer)
      ..add(ApiResponseDtoListTransactionStatistical.serializer)
      ..add(ApiResponseDtoListTransactionTemplateResponse.serializer)
      ..add(ApiResponseDtoLoanDetailResponse.serializer)
      ..add(ApiResponseDtoLockCardResponse.serializer)
      ..add(ApiResponseDtoOnlineSavingCertificateResponse.serializer)
      ..add(ApiResponseDtoOpenAccountResponse.serializer)
      ..add(ApiResponseDtoOpenOnlineSavingAccountResponse.serializer)
      ..add(ApiResponseDtoOpenVipAccountResponse.serializer)
      ..add(ApiResponseDtoPartialWithdrawOnlineSavingAccountResponse.serializer)
      ..add(ApiResponseDtoPayInvoicesResponse.serializer)
      ..add(ApiResponseDtoPayTopupResponse.serializer)
      ..add(ApiResponseDtoPaymentCardResponse.serializer)
      ..add(ApiResponseDtoRegisteredLoanDetailResponse.serializer)
      ..add(ApiResponseDtoRemoveScheduleResponse.serializer)
      ..add(ApiResponseDtoReviewCloseOnlineSavingAccountResponse.serializer)
      ..add(ApiResponseDtoReviewCloseTargetSavingResponse.serializer)
      ..add(ApiResponseDtoReviewOnlineSavingAccountResponse.serializer)
      ..add(ApiResponseDtoReviewOpenTargetSavingAccountResponse.serializer)
      ..add(ApiResponseDtoReviewPartialWithdrawOnlineSavingAccountResponse
          .serializer)
      ..add(ApiResponseDtoReviewPaymentCardResponse.serializer)
      ..add(ApiResponseDtoReviewPaymentResponse.serializer)
      ..add(ApiResponseDtoReviewTransferInterBank247Response.serializer)
      ..add(ApiResponseDtoReviewTransferInterBankCitadResponse.serializer)
      ..add(ApiResponseDtoReviewTransferIntraBankResponse.serializer)
      ..add(ApiResponseDtoReviewTransferResponse.serializer)
      ..add(ApiResponseDtoSavingAccountDetailResponse.serializer)
      ..add(ApiResponseDtoTargetSavingResponse.serializer)
      ..add(ApiResponseDtoTargetSavingTransResponse.serializer)
      ..add(ApiResponseDtoTransactionBeneficiaryResponse.serializer)
      ..add(ApiResponseDtoTransactionItemMapper.serializer)
      ..add(ApiResponseDtoTransactionResponse.serializer)
      ..add(ApiResponseDtoTransactionTemplateDto.serializer)
      ..add(ApiResponseDtoTransferResponse.serializer)
      ..add(ApiResponseDtoTransferScheduleDetailResponse.serializer)
      ..add(ApiResponseDtoTransferScheduleUpdateResponse.serializer)
      ..add(ApiResponseDtoUnlockCardResponse.serializer)
      ..add(ApiResponseDtoUpdateServiceResponse.serializer)
      ..add(ApiResponseDtoUpdateSupplierResponse.serializer)
      ..add(ApiResponseDtoUpdateTransactionCategoryResponse.serializer)
      ..add(ApiResponseDtoVipAccountInfoListResponse.serializer)
      ..add(ApiResponseDtoVipAccountResponse.serializer)
      ..add(AppModuleDto.serializer)
      ..add(AppModuleResponse.serializer)
      ..add(Attachment.serializer)
      ..add(AutoPayCardStatusResponse.serializer)
      ..add(AutoPayCardStatusResponseAutoPayCreditCardStatusEnum.serializer)
      ..add(BankCodeDto.serializer)
      ..add(BankFee.serializer)
      ..add(BankInfo.serializer)
      ..add(BaseResponseActiveCreditCardResponse.serializer)
      ..add(BaseResponseActiveDebitCardResponse.serializer)
      ..add(BaseResponseActiveVirtualCardResponse.serializer)
      ..add(BaseResponseAddVvipCustomerResponse.serializer)
      ..add(BaseResponseAdvanceFeeInfo.serializer)
      ..add(BaseResponseAdvanceHistoryDetail.serializer)
      ..add(BaseResponseAdvanceTransferResponse.serializer)
      ..add(BaseResponseAppModuleResponse.serializer)
      ..add(BaseResponseAutoPayCardStatusResponse.serializer)
      ..add(BaseResponseCancelTransactionInquiryResponse.serializer)
      ..add(BaseResponseChangePINCardResponse.serializer)
      ..add(BaseResponseCheckCardForActiveResponse.serializer)
      ..add(BaseResponseCheckCashLimitationResponse.serializer)
      ..add(BaseResponseCheckIsVNPAccountResponse.serializer)
      ..add(BaseResponseCheckOpenVvipResponse.serializer)
      ..add(BaseResponseCheckPinCodeTransResponse.serializer)
      ..add(BaseResponseCheckTermAndConditionResponse.serializer)
      ..add(BaseResponseCheckValidPINCardResponse.serializer)
      ..add(BaseResponseCheckVerifyUserStatusResponse.serializer)
      ..add(BaseResponseConfirmCreateWithdrawalCodeResponse.serializer)
      ..add(BaseResponseContactCreatedListResponse.serializer)
      ..add(BaseResponseCreateAdvancePackageRequestResponse.serializer)
      ..add(BaseResponseCreateAdvanceResponse.serializer)
      ..add(BaseResponseCreateBillScheduleResponse.serializer)
      ..add(BaseResponseCreateCustomerVNPostResponse.serializer)
      ..add(BaseResponseCreateTransactionInquiryResponse.serializer)
      ..add(BaseResponseCreateTransferRequestResponse.serializer)
      ..add(BaseResponseCreateTransferResponse.serializer)
      ..add(BaseResponseCreateWithdrawalCodeResponse.serializer)
      ..add(BaseResponseCurrentPackageInfoResponse.serializer)
      ..add(BaseResponseDeletePinCodeTransResponse.serializer)
      ..add(BaseResponseDeleteTransferRequestResponse.serializer)
      ..add(BaseResponseEbankingPackagesInfoResponse.serializer)
      ..add(BaseResponseEmployeeInfo.serializer)
      ..add(BaseResponseFireBillScheduleResponse.serializer)
      ..add(BaseResponseGenCVVCardResponse.serializer)
      ..add(BaseResponseGetAccCardInquiryResponse.serializer)
      ..add(BaseResponseGetAccCardTransactionResponse.serializer)
      ..add(BaseResponseGetAccTransHisDetailResponse.serializer)
      ..add(BaseResponseGetAccountVipInfoListResponse.serializer)
      ..add(BaseResponseGetAdvanceTransactionNoteResponse.serializer)
      ..add(BaseResponseGetAllCardWaitOpenResponse.serializer)
      ..add(BaseResponseGetAllProvinceResponse.serializer)
      ..add(BaseResponseGetAppModuleByIdResponse.serializer)
      ..add(BaseResponseGetBillHistoriesResponse.serializer)
      ..add(BaseResponseGetBillScheduleResponse.serializer)
      ..add(BaseResponseGetCardProductsResponse.serializer)
      ..add(BaseResponseGetDueInvoicesLotusResponse.serializer)
      ..add(BaseResponseGetDueInvoicesResponse.serializer)
      ..add(BaseResponseGetEBankingPackageByCustomerResponse.serializer)
      ..add(BaseResponseGetInProgressAdvanceTransactionsResponse.serializer)
      ..add(BaseResponseGetInvoiceHistoriesResponse.serializer)
      ..add(BaseResponseGetListBranchKLBResponse.serializer)
      ..add(BaseResponseGetListInquiryStepResponse.serializer)
      ..add(BaseResponseGetListTransInquiryResponse.serializer)
      ..add(BaseResponseGetLoanPaymentMethodsResponse.serializer)
      ..add(BaseResponseGetLoanProfitSpreadsheetResponse.serializer)
      ..add(BaseResponseGetLoanPurposeResponse.serializer)
      ..add(BaseResponseGetOwnerCardInfoResponse.serializer)
      ..add(BaseResponseGetPaymentAccountInfoInternalResponse.serializer)
      ..add(BaseResponseGetPhoneCardPriceResponse.serializer)
      ..add(BaseResponseGetPhoneCardProvidersResponse.serializer)
      ..add(BaseResponseGetPricePinCodeResponse.serializer)
      ..add(BaseResponseGetSavingAccountsResponse.serializer)
      ..add(BaseResponseGetServicesBillResponse.serializer)
      ..add(BaseResponseGetServicesLotusResponse.serializer)
      ..add(BaseResponseGetShareBillTransferResponse.serializer)
      ..add(BaseResponseGetSmsBankingDetailResponse.serializer)
      ..add(BaseResponseGetSmsBankingFeeResponse.serializer)
      ..add(BaseResponseGetSuppliersBillResponse.serializer)
      ..add(BaseResponseGetSuppliersLotusResponse.serializer)
      ..add(BaseResponseGetSuppliersPinCodeResponse.serializer)
      ..add(BaseResponseGetSuppliersTopupResponse.serializer)
      ..add(BaseResponseGetTelecomProvidersLotusResponse.serializer)
      ..add(BaseResponseGetTopupServiceResponse.serializer)
      ..add(BaseResponseGetTopupValueAllTelcoResponse.serializer)
      ..add(BaseResponseGetTopupValuesLotusResponse.serializer)
      ..add(BaseResponseGetTransInquiryDetailResponse.serializer)
      ..add(BaseResponseGetTransInquiryReasonResponse.serializer)
      ..add(BaseResponseGetTransactionCategoriesResponse.serializer)
      ..add(BaseResponseGetTransactionTypesResponse.serializer)
      ..add(BaseResponseGetUnpaidBillResponse.serializer)
      ..add(BaseResponseGetValuesTopupResponse.serializer)
      ..add(BaseResponseGetVersionConfigsResponse.serializer)
      ..add(BaseResponseIgnoreBillReminderResponse.serializer)
      ..add(BaseResponseIgnoreInvoiceDebtsReminderResponse.serializer)
      ..add(BaseResponseIssueVirtualCardResponse.serializer)
      ..add(BaseResponseListAdvanceHistoryInfo.serializer)
      ..add(BaseResponseListProviderDto.serializer)
      ..add(BaseResponseListUserEnterpriseInfo.serializer)
      ..add(BaseResponseListWithdrawalCodeActiveResponse.serializer)
      ..add(BaseResponseListWithdrawalCodeExpiredResponse.serializer)
      ..add(BaseResponseLoginAppModuleResponse.serializer)
      ..add(BaseResponseObject.serializer)
      ..add(BaseResponseOfflineSavingAccountDetailResponse.serializer)
      ..add(BaseResponseOpenVvipAccountResponse.serializer)
      ..add(BaseResponseOptionalNumberResponse.serializer)
      ..add(BaseResponsePageDtoListShortSalaryPeriodInfo.serializer)
      ..add(BaseResponsePageSupportGetOnlineSavingAccountResponse.serializer)
      ..add(BaseResponsePageSupportGetPaidPhoneCardHistoryResponse.serializer)
      ..add(BaseResponsePageSupportGetPinCodeHistoryResponse.serializer)
      ..add(BaseResponsePageSupportSearchVvipAccountResponse.serializer)
      ..add(BaseResponsePageSupportTransferRequestDetailResponse.serializer)
      ..add(BaseResponsePayInvoiceLotusResponse.serializer)
      ..add(BaseResponsePaymentBillResponse.serializer)
      ..add(BaseResponsePaymentPhoneCardResponse.serializer)
      ..add(BaseResponsePaymentPinCodeResponse.serializer)
      ..add(BaseResponsePaymentTopupLotusResponse.serializer)
      ..add(BaseResponsePaymentTopupResponse.serializer)
      ..add(BaseResponsePreCheckIssueVirtualCardResponse.serializer)
      ..add(BaseResponsePreCheckResetPINCardResponse.serializer)
      ..add(BaseResponsePreviewUpdateSmsBankingResponse.serializer)
      ..add(BaseResponseQueryBillResponse.serializer)
      ..add(BaseResponseReIssueVirtualCardResponse.serializer)
      ..add(BaseResponseRegisterLoanResponse.serializer)
      ..add(BaseResponseRemoveBillScheduleResponse.serializer)
      ..add(BaseResponseResetCountPINCardResponse.serializer)
      ..add(BaseResponseResetPINCardResponse.serializer)
      ..add(BaseResponseResolvePaymentDataAppModuleResponse.serializer)
      ..add(BaseResponseReviewBillScheduleResponse.serializer)
      ..add(BaseResponseReviewCreateScheduleResponse.serializer)
      ..add(BaseResponseReviewCreateTransferResponse.serializer)
      ..add(BaseResponseReviewOpenCardResponse.serializer)
      ..add(BaseResponseReviewPaymentInvoiceLotusResponse.serializer)
      ..add(BaseResponseReviewTransferInterBank247Response.serializer)
      ..add(BaseResponseReviewTransferInterBank247Ver3Response.serializer)
      ..add(BaseResponseReviewTransferInterBank247Ver4Response.serializer)
      ..add(BaseResponseReviewTransferInterBankCitadResponse.serializer)
      ..add(BaseResponseReviewTransferIntraBankResponse.serializer)
      ..add(BaseResponseReviewTransferResponse.serializer)
      ..add(BaseResponseReviewVvipAccountResponse.serializer)
      ..add(BaseResponseShareBillsResponse.serializer)
      ..add(BaseResponseTransactionResponse.serializer)
      ..add(BaseResponseTransactionScheduleResponse.serializer)
      ..add(BaseResponseTransactionStatisticalDetailResponse.serializer)
      ..add(BaseResponseTransferInfoQrCodeNapasResponse.serializer)
      ..add(BaseResponseTransferRequestDetailResponse.serializer)
      ..add(BaseResponseTransferResponse.serializer)
      ..add(BaseResponseTransferVer3Response.serializer)
      ..add(BaseResponseTransferVer4Response.serializer)
      ..add(BaseResponseTuitionFeeDto.serializer)
      ..add(BaseResponseUpdateAutoPaymentCardStatusResponse.serializer)
      ..add(BaseResponseUpdateEcomLockATMCardResponse.serializer)
      ..add(BaseResponseUpdateEcomLockCreditCardResponse.serializer)
      ..add(BaseResponseUpdateLockServicesCardResponse.serializer)
      ..add(BaseResponseUpdateSmsBankingResponse.serializer)
      ..add(BaseResponseUpdateTargetSavingAccountResponse.serializer)
      ..add(BaseResponseUpdateTransactionTypeResponse.serializer)
      ..add(BaseResponseUserCashLimitationResponse.serializer)
      ..add(BaseResponseUserLogworkByPeriodInfo.serializer)
      ..add(BaseResponseVNPCloseOnlineSavingTransferResponse.serializer)
      ..add(BaseResponseVipAccountV1Response.serializer)
      ..add(BaseResponseVirtualToPhysicalCardResponse.serializer)
      ..add(BaseResponseVnpElectricWaterTransferResponse.serializer)
      ..add(BaseResponseVnpReviewElectricWaterResponse.serializer)
      ..add(BillInfoDto.serializer)
      ..add(BillScheduleDto.serializer)
      ..add(Branch.serializer)
      ..add(BranchData.serializer)
      ..add(BranchRequest.serializer)
      ..add(CalTotalSavingOnlRateRequest.serializer)
      ..add(CalTotalSavingOnlRateResponse.serializer)
      ..add(CalculateInterestOnlineSavingRequest.serializer)
      ..add(CalculateInterestOnlineSavingResponse.serializer)
      ..add(CalculateInterestPartialWithdrawOnlineSavingRequest.serializer)
      ..add(CalculateInterestPartialWithdrawOnlineSavingResponse.serializer)
      ..add(CancelTransactionInquiryRequest.serializer)
      ..add(CancelTransactionInquiryResponse.serializer)
      ..add(CardDetail.serializer)
      ..add(CardInquiryInfo.serializer)
      ..add(CardListItem.serializer)
      ..add(CardProductData.serializer)
      ..add(CardProductPromotion.serializer)
      ..add(CardServiceType.serializer)
      ..add(CardWaitOpenDto.serializer)
      ..add(CashLimitationResponse.serializer)
      ..add(ChangePINCardRequest.serializer)
      ..add(ChangePINCardResponse.serializer)
      ..add(CheckCardForActiveResponse.serializer)
      ..add(CheckCashLimitationRequest.serializer)
      ..add(CheckCashLimitationResponse.serializer)
      ..add(CheckCashLimitationVers2Request.serializer)
      ..add(CheckIsVNPAccountResponse.serializer)
      ..add(CheckMyShopPaymentAccountResponse.serializer)
      ..add(CheckOpenVvipResponse.serializer)
      ..add(CheckPinCodeTransRequest.serializer)
      ..add(CheckPinCodeTransResponse.serializer)
      ..add(CheckPreferentialRateResponse.serializer)
      ..add(CheckTermAndConditionResponse.serializer)
      ..add(CheckValidPINCardRequest.serializer)
      ..add(CheckValidPINCardResponse.serializer)
      ..add(CheckVerifyUserStatusResponse.serializer)
      ..add(CityResponse.serializer)
      ..add(CloseOnlineSavingAccountRequest.serializer)
      ..add(CloseOnlineSavingAccountRequestCurrencyEnum.serializer)
      ..add(CloseOnlineSavingAccountResponse.serializer)
      ..add(CloseTargetSavingAccountRequest.serializer)
      ..add(CloseTargetSavingAccountResponse.serializer)
      ..add(ConfirmCreateWithdrawalCodeRequest.serializer)
      ..add(ConfirmCreateWithdrawalCodeResponse.serializer)
      ..add(ContactCreatedListResponse.serializer)
      ..add(ContactsDto.serializer)
      ..add(CoreVnpTransactionResponse.serializer)
      ..add(CreateAdvancePackageRequestRequest.serializer)
      ..add(CreateAdvancePackageRequestResponse.serializer)
      ..add(CreateAdvanceResponse.serializer)
      ..add(CreateAdvanceResponseStatusEnum.serializer)
      ..add(CreateBillScheduleRequest.serializer)
      ..add(CreateBillScheduleResponse.serializer)
      ..add(CreateContactListRequest.serializer)
      ..add(CreateContactRequest.serializer)
      ..add(CreateCustomerRequest.serializer)
      ..add(CreateCustomerRequestV2.serializer)
      ..add(CreateCustomerVNPostRequest.serializer)
      ..add(CreateCustomerVNPostResponse.serializer)
      ..add(CreateListContactRequest.serializer)
      ..add(CreateScheduleRequest.serializer)
      ..add(CreateScheduleResponse.serializer)
      ..add(CreateServiceRequest.serializer)
      ..add(CreateServiceResponse.serializer)
      ..add(CreateSupplierRequest.serializer)
      ..add(CreateSupplierResponse.serializer)
      ..add(CreateTargetSavingAccountRequest.serializer)
      ..add(CreateTargetSavingAccountRequestCurrencyEnum.serializer)
      ..add(CreateTargetSavingResponse.serializer)
      ..add(CreateTransactionInquiryRequest.serializer)
      ..add(CreateTransactionInquiryResponse.serializer)
      ..add(CreateTransferRequest.serializer)
      ..add(CreateTransferRequestResponse.serializer)
      ..add(CreateTransferResponse.serializer)
      ..add(CreateWithdrawalCodeRequest.serializer)
      ..add(CreateWithdrawalCodeResponse.serializer)
      ..add(CurrentPackageInfoResponse.serializer)
      ..add(Customer.serializer)
      ..add(DeletePinCodeTransRequest.serializer)
      ..add(DeleteTransferRequestResponse.serializer)
      ..add(DetailCustomerRequest.serializer)
      ..add(DisbursementResponse.serializer)
      ..add(DistrictResponse.serializer)
      ..add(EBankingCashLimitationResponse.serializer)
      ..add(EbankingPackagesInfoResponse.serializer)
      ..add(EmployeeInfo.serializer)
      ..add(ExportPdfCardRequest.serializer)
      ..add(ExportPdfTransactionRequest.serializer)
      ..add(ExportTransactionsCardDebitToPdfRequest.serializer)
      ..add(FireBillScheduleRequest.serializer)
      ..add(FireBillScheduleResponse.serializer)
      ..add(FireSchedulePayInvoiceRequest.serializer)
      ..add(FireSchedulePayInvoicesResponse.serializer)
      ..add(Flag.serializer)
      ..add(FunctionListItem.serializer)
      ..add(GenCVVCardRequest.serializer)
      ..add(GenCVVCardResponse.serializer)
      ..add(GetAccCardInquiryResponse.serializer)
      ..add(GetAccCardTransactionResponse.serializer)
      ..add(GetAccTransHisDetailResponse.serializer)
      ..add(GetAccountVipInfoListResponse.serializer)
      ..add(GetAdvanceEbankingLimitConfigResponse.serializer)
      ..add(GetAdvanceTransactionNoteResponse.serializer)
      ..add(GetAllCardWaitOpenResponse.serializer)
      ..add(GetAllProvinceResponse.serializer)
      ..add(GetAppModuleByIdResponse.serializer)
      ..add(GetBillHistoriesResponse.serializer)
      ..add(GetBillScheduleResponse.serializer)
      ..add(GetCardProductPromotionsRequest.serializer)
      ..add(GetCardProductPromotionsResponse.serializer)
      ..add(GetCardProductsRequest.serializer)
      ..add(GetCardProductsResponse.serializer)
      ..add(GetCardsRequest.serializer)
      ..add(GetCardsResponse.serializer)
      ..add(GetCitiesResponse.serializer)
      ..add(GetDetailCardRequest.serializer)
      ..add(GetDetailCardResponse.serializer)
      ..add(GetDetailProductCardRequest.serializer)
      ..add(GetDisbursementsResponse.serializer)
      ..add(GetDistrictsResponse.serializer)
      ..add(GetDueInvoicesLotusResponse.serializer)
      ..add(GetDueInvoicesResponse.serializer)
      ..add(GetEBankingPackageByCustomerResponse.serializer)
      ..add(GetFunctionCardsRequest.serializer)
      ..add(GetFunctionCardsResponse.serializer)
      ..add(GetInProgressAdvanceTransactionsResponse.serializer)
      ..add(GetInterestReceivesResponse.serializer)
      ..add(GetInvoiceHistoriesResponse.serializer)
      ..add(GetInvoicesResponse.serializer)
      ..add(GetListBranchKLBResponse.serializer)
      ..add(GetListInquiryStepResponse.serializer)
      ..add(GetListMyShopPaymentAccountResponse.serializer)
      ..add(GetListServicesResponse.serializer)
      ..add(GetListSuppliersResponse.serializer)
      ..add(GetListTransInquiryResponse.serializer)
      ..add(GetLoanPaymentMethodsResponse.serializer)
      ..add(GetLoanPaymentMethodsResponseLoanPaymentMethodsEnum.serializer)
      ..add(GetLoanProcessHistoriesResponse.serializer)
      ..add(GetLoanProfitSpreadsheetResponse.serializer)
      ..add(GetLoanPurposeResponse.serializer)
      ..add(GetLoansResponse.serializer)
      ..add(GetOnlineSavingAccountResponse.serializer)
      ..add(GetOwnerCardInfoResponse.serializer)
      ..add(GetPaidPhoneCardHistoryResponse.serializer)
      ..add(GetPaidPhoneCardHistoryResponseTelephoneProviderEnum.serializer)
      ..add(GetPaymentAccountInfoInternalResponse.serializer)
      ..add(GetPaymentAccountsResponseV2.serializer)
      ..add(GetPhoneCardPriceResponse.serializer)
      ..add(GetPhoneCardProvidersResponse.serializer)
      ..add(GetPinCodeHistoryResponse.serializer)
      ..add(GetPricePinCodeResponse.serializer)
      ..add(GetRefundHistoriesResponse.serializer)
      ..add(GetRefundScheduleResponse.serializer)
      ..add(GetSavingAccountsResponse.serializer)
      ..add(GetSavingHistoriesResponse.serializer)
      ..add(GetSavingPeriodsResponse.serializer)
      ..add(GetSavingTypesResponse.serializer)
      ..add(GetServicesBillResponse.serializer)
      ..add(GetServicesLotusResponse.serializer)
      ..add(GetShareBillTransferResponse.serializer)
      ..add(GetSmsBankingDetailResponse.serializer)
      ..add(GetSmsBankingFeeResponse.serializer)
      ..add(GetStatementListCardRequest.serializer)
      ..add(GetStatementListCardResponse.serializer)
      ..add(GetSummaryCardsRequest.serializer)
      ..add(GetSummaryCardsResponse.serializer)
      ..add(GetSuppliersBillResponse.serializer)
      ..add(GetSuppliersLotusResponse.serializer)
      ..add(GetSuppliersPinCodeResponse.serializer)
      ..add(GetSuppliersTopupResponse.serializer)
      ..add(GetTeleInfoResponse.serializer)
      ..add(GetTelecomProvidersLotusResponse.serializer)
      ..add(GetTopupServiceResponse.serializer)
      ..add(GetTopupValueAllTelcoResponse.serializer)
      ..add(GetTopupValuesLotusResponse.serializer)
      ..add(GetTransInquiryDetailResponse.serializer)
      ..add(GetTransInquiryReasonResponse.serializer)
      ..add(GetTransactionCardsResponse.serializer)
      ..add(GetTransactionCategoriesResponse.serializer)
      ..add(GetTransactionHistoryVer2Request.serializer)
      ..add(GetTransactionTypesResponse.serializer)
      ..add(GetUnpaidBillResponse.serializer)
      ..add(GetValuesTopupResponse.serializer)
      ..add(GetVersionConfigsResponse.serializer)
      ..add(IgnoreBillReminderRequest.serializer)
      ..add(IgnoreInvoiceDebtsReminderRequest.serializer)
      ..add(IgnoreInvoiceDebtsReminderRequestProviderEnum.serializer)
      ..add(InProgressAdvanceTransactionsResponse.serializer)
      ..add(InProgressAdvanceTransactionsResponseStatusEnum.serializer)
      ..add(InquiryCustomerRequest.serializer)
      ..add(InquiryReasonDto.serializer)
      ..add(InterestReceivesResponse.serializer)
      ..add(InvoiceDto.serializer)
      ..add(InvoiceHistoryResponse.serializer)
      ..add(InvoiceScheduleDto.serializer)
      ..add(InvoiceServiceDtoBase.serializer)
      ..add(InvoiceSupplierDto.serializer)
      ..add(IssueVirtualCardRequest.serializer)
      ..add(IssueVirtualCardResponse.serializer)
      ..add(ListBankCodeResponse.serializer)
      ..add(ListBankSupportScanVietQrResponse.serializer)
      ..add(ListContactRequest.serializer)
      ..add(ListContactResponse.serializer)
      ..add(ListTransactionGroupResponse.serializer)
      ..add(ListTransactionScheduleResponse.serializer)
      ..add(ListTransactionTemplateResponse.serializer)
      ..add(ListWithdrawalCodeActiveResponse.serializer)
      ..add(ListWithdrawalCodeExpiredResponse.serializer)
      ..add(LoanDetailResponse.serializer)
      ..add(LoanDetailResponseCurrencyEnum.serializer)
      ..add(LoanPaymentMethodResponse.serializer)
      ..add(LoanPaymentMethodResponseLoanPaymentMethodEnum.serializer)
      ..add(LoanProcessHistoryResponse.serializer)
      ..add(LoanProfitResponse.serializer)
      ..add(LoanPurposeResponse.serializer)
      ..add(LoanRefundResponse.serializer)
      ..add(LoanRefundSchedule.serializer)
      ..add(LoanResponse.serializer)
      ..add(LockCardRequest.serializer)
      ..add(LoginAppModuleRequest.serializer)
      ..add(LoginAppModuleResponse.serializer)
      ..add(LoungeItems.serializer)
      ..add(LoungePromotion.serializer)
      ..add(NullType.serializer)
      ..add(OfflineSavingAccountDetailResponse.serializer)
      ..add(OnlineSavingCertificateResponse.serializer)
      ..add(OpenAccountRequest.serializer)
      ..add(OpenAccountResponse.serializer)
      ..add(OpenOnlineSavingAccountRequest.serializer)
      ..add(OpenOnlineSavingAccountRequestCurrencyEnum.serializer)
      ..add(OpenOnlineSavingAccountResponse.serializer)
      ..add(OpenVipAccountRequest.serializer)
      ..add(OpenVipAccountResponse.serializer)
      ..add(OpenVvipAccountRequest.serializer)
      ..add(OpenVvipAccountResponse.serializer)
      ..add(OptionalNumberResponse.serializer)
      ..add(PageDtoListShortSalaryPeriodInfo.serializer)
      ..add(PageSupportGetOnlineSavingAccountResponse.serializer)
      ..add(PageSupportGetPaidPhoneCardHistoryResponse.serializer)
      ..add(PageSupportGetPinCodeHistoryResponse.serializer)
      ..add(PageSupportSearchVvipAccountResponse.serializer)
      ..add(PageSupportTransferRequestDetailResponse.serializer)
      ..add(PaidPhoneCardResponse.serializer)
      ..add(Param.serializer)
      ..add(PartialWithdrawOnlineSavingAccountRequest.serializer)
      ..add(PartialWithdrawOnlineSavingAccountResponse.serializer)
      ..add(PayInvoiceLotusRequest.serializer)
      ..add(PayInvoiceLotusResponse.serializer)
      ..add(PayInvoiceRequest.serializer)
      ..add(PayInvoicesResponse.serializer)
      ..add(PayTopupRequest.serializer)
      ..add(PayTopupRequestProviderEnum.serializer)
      ..add(PayTopupRequestTopupTypeEnum.serializer)
      ..add(PayTopupResponse.serializer)
      ..add(PaymentAccountResponseV2.serializer)
      ..add(PaymentBillDto.serializer)
      ..add(PaymentBillRequest.serializer)
      ..add(PaymentBillResponse.serializer)
      ..add(PaymentCardRequest.serializer)
      ..add(PaymentCardResponse.serializer)
      ..add(PaymentInfo.serializer)
      ..add(PaymentPhoneCardRequest.serializer)
      ..add(PaymentPhoneCardRequestTelephoneProviderEnum.serializer)
      ..add(PaymentPhoneCardResponse.serializer)
      ..add(PaymentPinCodeRequest.serializer)
      ..add(PaymentPinCodeRequestPinCodeTypeEnum.serializer)
      ..add(PaymentPinCodeResponse.serializer)
      ..add(PaymentTopupLotusRequest.serializer)
      ..add(PaymentTopupLotusRequestTelecomProviderEnum.serializer)
      ..add(PaymentTopupLotusRequestTopupTypeEnum.serializer)
      ..add(PaymentTopupLotusResponse.serializer)
      ..add(PaymentTopupRequest.serializer)
      ..add(PaymentTopupRequestTopupTypeEnum.serializer)
      ..add(PaymentTopupResponse.serializer)
      ..add(PhoneCardProvidersResponse.serializer)
      ..add(PhoneCardProvidersResponseTelephoneProviderEnum.serializer)
      ..add(PhoneCardResponse.serializer)
      ..add(PinCodePaidDto.serializer)
      ..add(PinCodeValueDto.serializer)
      ..add(PreCheckIssueVirtualCardResponse.serializer)
      ..add(PreCheckResetPINCardRequest.serializer)
      ..add(PreCheckResetPINCardResponse.serializer)
      ..add(PreviewUpdateSmsBankingRequest.serializer)
      ..add(PreviewUpdateSmsBankingRequestActionEnum.serializer)
      ..add(PreviewUpdateSmsBankingResponse.serializer)
      ..add(PreviewUpdateSmsBankingResponseAuthTypeEnum.serializer)
      ..add(PromotionDetail.serializer)
      ..add(ProviderDto.serializer)
      ..add(ProvinceData.serializer)
      ..add(QueryBillResponse.serializer)
      ..add(ReIssueVirtualCardRequest.serializer)
      ..add(ReIssueVirtualCardResponse.serializer)
      ..add(RecentTransactionRequest.serializer)
      ..add(RefundLoanRequest.serializer)
      ..add(RefundScheduleResponse.serializer)
      ..add(Region.serializer)
      ..add(RegionRequest.serializer)
      ..add(RegisterLoanRequest.serializer)
      ..add(RegisterLoanResponse.serializer)
      ..add(RegisteredLoanDetailResponse.serializer)
      ..add(RemoveBillScheduleRequest.serializer)
      ..add(RemoveBillScheduleResponse.serializer)
      ..add(RemoveScheduleRequest.serializer)
      ..add(RemoveScheduleResponse.serializer)
      ..add(ResetCountPINCardRequest.serializer)
      ..add(ResetCountPINCardResponse.serializer)
      ..add(ResetPINCardRequest.serializer)
      ..add(ResetPINCardResponse.serializer)
      ..add(ResolvePaymentDataAppModuleRequest.serializer)
      ..add(ResolvePaymentDataAppModuleResponse.serializer)
      ..add(ReviewAdvanceTransactionResponse.serializer)
      ..add(ReviewBillScheduleRequest.serializer)
      ..add(ReviewBillScheduleResponse.serializer)
      ..add(ReviewCloseOnlineSavingAccountRequest.serializer)
      ..add(ReviewCloseOnlineSavingAccountRequestCurrencyEnum.serializer)
      ..add(ReviewCloseOnlineSavingAccountResponse.serializer)
      ..add(ReviewCloseTargetSavingRequest.serializer)
      ..add(ReviewCloseTargetSavingResponse.serializer)
      ..add(ReviewCloseTargetSavingResponseCurrencyEnum.serializer)
      ..add(ReviewCreateScheduleRequest.serializer)
      ..add(ReviewCreateScheduleRequestProviderEnum.serializer)
      ..add(ReviewCreateScheduleResponse.serializer)
      ..add(ReviewCreateTransferRequest.serializer)
      ..add(ReviewCreateTransferResponse.serializer)
      ..add(ReviewOnlineSavingAccountResponse.serializer)
      ..add(ReviewOpenCardRequest.serializer)
      ..add(ReviewOpenCardResponse.serializer)
      ..add(ReviewOpenTargetSavingAccountRequest.serializer)
      ..add(ReviewOpenTargetSavingAccountRequestCurrencyEnum.serializer)
      ..add(ReviewOpenTargetSavingAccountResponse.serializer)
      ..add(ReviewPartialWithdrawOnlineSavingAccountRequest.serializer)
      ..add(ReviewPartialWithdrawOnlineSavingAccountResponse.serializer)
      ..add(ReviewPaymentCardRequest.serializer)
      ..add(ReviewPaymentCardResponse.serializer)
      ..add(ReviewPaymentInvoiceLotusRequest.serializer)
      ..add(ReviewPaymentInvoiceLotusResponse.serializer)
      ..add(ReviewPaymentRequest.serializer)
      ..add(ReviewPaymentResponse.serializer)
      ..add(ReviewSavingAccountRequest.serializer)
      ..add(ReviewSavingAccountRequestCurrencyEnum.serializer)
      ..add(ReviewTransactionCommandResponse.serializer)
      ..add(ReviewTransferInterBank247Request.serializer)
      ..add(ReviewTransferInterBank247Response.serializer)
      ..add(ReviewTransferInterBank247V2Request.serializer)
      ..add(ReviewTransferInterBank247V3Request.serializer)
      ..add(ReviewTransferInterBank247Ver3Response.serializer)
      ..add(ReviewTransferInterBank247Ver4Request.serializer)
      ..add(ReviewTransferInterBank247Ver4Response.serializer)
      ..add(ReviewTransferInterBankCitadRequest.serializer)
      ..add(ReviewTransferInterBankCitadResponse.serializer)
      ..add(ReviewTransferInterBankCitadV2Request.serializer)
      ..add(ReviewTransferIntraBankRequest.serializer)
      ..add(ReviewTransferIntraBankResponse.serializer)
      ..add(ReviewTransferIntraBankV2Request.serializer)
      ..add(ReviewTransferRequest.serializer)
      ..add(ReviewTransferResponse.serializer)
      ..add(ReviewTransferV2Request.serializer)
      ..add(ReviewVvipAccountResponse.serializer)
      ..add(ReviewVvipAccountResponseCheckCodeEnum.serializer)
      ..add(SalaryPeriodInfo.serializer)
      ..add(SavingAccountDetailResponse.serializer)
      ..add(SavingAccountResponse.serializer)
      ..add(SavingHistoryResponse.serializer)
      ..add(SavingHistoryResponseCurrencyEnum.serializer)
      ..add(SavingHistoryResponseSavingTypeEnum.serializer)
      ..add(SavingPeriodResponse.serializer)
      ..add(SavingTypeResponse.serializer)
      ..add(Schedule.serializer)
      ..add(SchedulingObject.serializer)
      ..add(SearchVvipAccountResponse.serializer)
      ..add(ServiceDto.serializer)
      ..add(ShareBillsRequest.serializer)
      ..add(SharedPerson.serializer)
      ..add(ShortSalaryPeriodInfo.serializer)
      ..add(SmsPhone.serializer)
      ..add(StatementGroup.serializer)
      ..add(StatementItem.serializer)
      ..add(SummaryCreditCard.serializer)
      ..add(SupplierDto.serializer)
      ..add(SupplierLotusResponse.serializer)
      ..add(SupplierPinCodeDto.serializer)
      ..add(SupplierProductDto.serializer)
      ..add(TargetSavingAccountResponse.serializer)
      ..add(TargetSavingResponse.serializer)
      ..add(TargetSavingTransRequest.serializer)
      ..add(TargetSavingTransRequestTranTypeEnum.serializer)
      ..add(TargetSavingTransResponse.serializer)
      ..add(TelecomDto.serializer)
      ..add(TelecomLotusResponse.serializer)
      ..add(TopupServiceDto.serializer)
      ..add(TopupValue.serializer)
      ..add(TopupValueDto.serializer)
      ..add(TopupValueDtoProviderEnum.serializer)
      ..add(TopupValueLotusResponse.serializer)
      ..add(TransInquiryDto.serializer)
      ..add(TransInquiryStepDto.serializer)
      ..add(TransNextStep.serializer)
      ..add(Transaction.serializer)
      ..add(TransactionBeneficiaryRequest.serializer)
      ..add(TransactionBeneficiaryResponse.serializer)
      ..add(TransactionCard.serializer)
      ..add(TransactionCategoriesType.serializer)
      ..add(TransactionCategory.serializer)
      ..add(TransactionCategoryResponse.serializer)
      ..add(TransactionCategoryResponseCashFlowEnum.serializer)
      ..add(TransactionItemMapper.serializer)
      ..add(TransactionRequest.serializer)
      ..add(TransactionResponse.serializer)
      ..add(TransactionScheduleDto.serializer)
      ..add(TransactionScheduleResponse.serializer)
      ..add(TransactionStatistical.serializer)
      ..add(TransactionStatisticalDetailResponse.serializer)
      ..add(TransactionStatisticalItemDto.serializer)
      ..add(TransactionTemplateDto.serializer)
      ..add(TransactionTypeResponse.serializer)
      ..add(TransferBankRequest.serializer)
      ..add(TransferBankV2Request.serializer)
      ..add(TransferBankVer3Request.serializer)
      ..add(TransferBankVer4Request.serializer)
      ..add(TransferInfoQrCodeNapasResponse.serializer)
      ..add(TransferRequest.serializer)
      ..add(TransferRequestDetailResponse.serializer)
      ..add(TransferResponse.serializer)
      ..add(TransferScheduleDetailResponse.serializer)
      ..add(TransferScheduleResponse.serializer)
      ..add(TransferScheduleUpdateResponse.serializer)
      ..add(TransferVer3Response.serializer)
      ..add(TransferVer4Response.serializer)
      ..add(TuitionFeeDto.serializer)
      ..add(UnlockCardRequest.serializer)
      ..add(UpdateAutoPaymentCardStatusRequest.serializer)
      ..add(UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
          .serializer)
      ..add(UpdateContactRequest.serializer)
      ..add(UpdateCustomerLoggingRequest.serializer)
      ..add(UpdateCustomerRequest.serializer)
      ..add(UpdateEcomLockATMCardRequest.serializer)
      ..add(UpdateEcomLockCreditCardRequest.serializer)
      ..add(UpdateLockServicesCardRequest.serializer)
      ..add(UpdateServiceRequest.serializer)
      ..add(UpdateServiceResponse.serializer)
      ..add(UpdateSmsBankingRequest.serializer)
      ..add(UpdateSmsBankingResponse.serializer)
      ..add(UpdateSmsBankingResponseActionEnum.serializer)
      ..add(UpdateSupplierRequest.serializer)
      ..add(UpdateSupplierResponse.serializer)
      ..add(UpdateTargetSavingAccountRequest.serializer)
      ..add(UpdateTransactionTypeRequest.serializer)
      ..add(UserCashLimitationResponse.serializer)
      ..add(UserEnterpriseInfo.serializer)
      ..add(UserLogworkByPeriodInfo.serializer)
      ..add(UserLogworkItem.serializer)
      ..add(UserLogworkItemTypeEnum.serializer)
      ..add(VNPCloseOnlineSavingTransferRequest.serializer)
      ..add(VNPCloseOnlineSavingTransferRequestCurrencyEnum.serializer)
      ..add(VNPCloseOnlineSavingTransferResponse.serializer)
      ..add(VerifySoftOtpRequest.serializer)
      ..add(VersionConfigsResponse.serializer)
      ..add(VipAccountInfoDto.serializer)
      ..add(VipAccountInfoListResponse.serializer)
      ..add(VipAccountResponse.serializer)
      ..add(VipAccountV1Response.serializer)
      ..add(VirtualToPhysicalCardRequest.serializer)
      ..add(VirtualToPhysicalCardResponse.serializer)
      ..add(VnpBillInfo.serializer)
      ..add(VnpCustomerBillInfo.serializer)
      ..add(VnpElectricWaterTransferRequest.serializer)
      ..add(VnpElectricWaterTransferResponse.serializer)
      ..add(VnpReviewElectricWaterRequest.serializer)
      ..add(VnpReviewElectricWaterResponse.serializer)
      ..add(VnpServiceInfo.serializer)
      ..add(VvipOpenMethod.serializer)
      ..add(WithdrawalCodeResponse.serializer)
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AccCardTransInfo)]),
          () => ListBuilder<AccCardTransInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AccountInquiryInfo)]),
          () => ListBuilder<AccountInquiryInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CardInquiryInfo)]),
          () => ListBuilder<CardInquiryInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AccountItemMapper)]),
          () => ListBuilder<AccountItemMapper>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AccountNumberInfo)]),
          () => ListBuilder<AccountNumberInfo>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(AccountResponseMapper)]),
          () => ListBuilder<AccountResponseMapper>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(AccountVipInfoResponse)]),
          () => ListBuilder<AccountVipInfoResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(AdvanceEBankingCashLimitationResponse)]),
          () => ListBuilder<AdvanceEBankingCashLimitationResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AdvanceHistoryInfo)]),
          () => ListBuilder<AdvanceHistoryInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(AdvanceTransactionCommandResponse)]),
          () => ListBuilder<AdvanceTransactionCommandResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AppModuleDto)]),
          () => ListBuilder<AppModuleDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Attachment)]),
          () => ListBuilder<Attachment>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Attachment)]),
          () => ListBuilder<Attachment>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransInquiryStepDto)]),
          () => ListBuilder<TransInquiryStepDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BankCodeDto)]),
          () => ListBuilder<BankCodeDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BankFee)]),
          () => ListBuilder<BankFee>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BankInfo)]),
          () => ListBuilder<BankInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BillInfoDto)]),
          () => ListBuilder<BillInfoDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BillScheduleDto)]),
          () => ListBuilder<BillScheduleDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Branch)]),
          () => ListBuilder<Branch>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(BranchData)]),
          () => ListBuilder<BranchData>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CardListItem)]),
          () => ListBuilder<CardListItem>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CardProductData)]),
          () => ListBuilder<CardProductData>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(CardProductPromotion)]),
          () => ListBuilder<CardProductPromotion>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CardWaitOpenDto)]),
          () => ListBuilder<CardWaitOpenDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(CashLimitationResponse)]),
          () => ListBuilder<CashLimitationResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(CityResponse)]),
          () => ListBuilder<CityResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ContactsDto)]),
          () => ListBuilder<ContactsDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ContactsDto)]),
          () => ListBuilder<ContactsDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(CoreVnpTransactionResponse)]),
          () => ListBuilder<CoreVnpTransactionResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(CreateContactListRequest)]),
          () => ListBuilder<CreateContactListRequest>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(DisbursementResponse)]),
          () => ListBuilder<DisbursementResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(DistrictResponse)]),
          () => ListBuilder<DistrictResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(EBankingCashLimitationResponse)]),
          () => ListBuilder<EBankingCashLimitationResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(FunctionListItem)]),
          () => ListBuilder<FunctionListItem>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GetInvoicesResponse)]),
          () => ListBuilder<GetInvoicesResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(GetOnlineSavingAccountResponse)]),
          () => ListBuilder<GetOnlineSavingAccountResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(GetPaidPhoneCardHistoryResponse)]),
          () => ListBuilder<GetPaidPhoneCardHistoryResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GetPinCodeHistoryResponse)]),
          () => ListBuilder<GetPinCodeHistoryResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(GetPinCodeHistoryResponse)]),
          () => ListBuilder<GetPinCodeHistoryResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(InProgressAdvanceTransactionsResponse)]),
          () => ListBuilder<InProgressAdvanceTransactionsResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(InquiryReasonDto)]),
          () => ListBuilder<InquiryReasonDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(InterestReceivesResponse)]),
          () => ListBuilder<InterestReceivesResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(InvoiceDto)]),
          () => ListBuilder<InvoiceDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(InvoiceDto)]),
          () => ListBuilder<InvoiceDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(InvoiceHistoryResponse)]),
          () => ListBuilder<InvoiceHistoryResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(InvoiceHistoryResponse)]),
          () => ListBuilder<InvoiceHistoryResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(InvoiceScheduleDto)]),
          () => ListBuilder<InvoiceScheduleDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(InvoiceServiceDtoBase)]),
          () => ListBuilder<InvoiceServiceDtoBase>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(InvoiceServiceDtoBase)]),
          () => ListBuilder<InvoiceServiceDtoBase>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(InvoiceSupplierDto)]),
          () => ListBuilder<InvoiceSupplierDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(LoanPaymentMethodResponse)]),
          () => ListBuilder<LoanPaymentMethodResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [
            const FullType(GetLoanPaymentMethodsResponseLoanPaymentMethodsEnum)
          ]),
          () => ListBuilder<
              GetLoanPaymentMethodsResponseLoanPaymentMethodsEnum>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(LoanProcessHistoryResponse)]),
          () => ListBuilder<LoanProcessHistoryResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoanProfitResponse)]),
          () => ListBuilder<LoanProfitResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(LoanPurposeResponse)]),
          () => ListBuilder<LoanPurposeResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoanRefundResponse)]),
          () => ListBuilder<LoanRefundResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoanResponse)]),
          () => ListBuilder<LoanResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoanResponse)]),
          () => ListBuilder<LoanResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(PaidPhoneCardResponse)]),
          () => ListBuilder<PaidPhoneCardResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Param)]),
          () => ListBuilder<Param>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(PaymentAccountResponseV2)]),
          () => ListBuilder<PaymentAccountResponseV2>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(PaymentBillDto)]),
          () => ListBuilder<PaymentBillDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(PhoneCardProvidersResponse)]),
          () => ListBuilder<PhoneCardProvidersResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(PhoneCardResponse)]),
          () => ListBuilder<PhoneCardResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(PinCodePaidDto)]),
          () => ListBuilder<PinCodePaidDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(PinCodeValueDto)]),
          () => ListBuilder<PinCodeValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(PromotionDetail)]),
          () => ListBuilder<PromotionDetail>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ProviderDto)]),
          () => ListBuilder<ProviderDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ProvinceData)]),
          () => ListBuilder<ProvinceData>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(QueryBillResponse)]),
          () => ListBuilder<QueryBillResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(RefundScheduleResponse)]),
          () => ListBuilder<RefundScheduleResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoanRefundSchedule)]),
          () => ListBuilder<LoanRefundSchedule>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(Region)]),
          () => ListBuilder<Region>())
      ..addBuilderFactory(
          const FullType(BuiltList,
              const [const FullType(ReviewTransactionCommandResponse)]),
          () => ListBuilder<ReviewTransactionCommandResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SavingAccountResponse)]),
          () => ListBuilder<SavingAccountResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TargetSavingAccountResponse)]),
          () => ListBuilder<TargetSavingAccountResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SavingAccountResponse)]),
          () => ListBuilder<SavingAccountResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SavingAccountResponse)]),
          () => ListBuilder<SavingAccountResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SavingHistoryResponse)]),
          () => ListBuilder<SavingHistoryResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SavingPeriodResponse)]),
          () => ListBuilder<SavingPeriodResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SavingTypeResponse)]),
          () => ListBuilder<SavingTypeResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SearchVvipAccountResponse)]),
          () => ListBuilder<SearchVvipAccountResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ServiceDto)]),
          () => ListBuilder<ServiceDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SharedPerson)]),
          () => ListBuilder<SharedPerson>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(ShortSalaryPeriodInfo)]),
          () => ListBuilder<ShortSalaryPeriodInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SmsPhone)]),
          () => ListBuilder<SmsPhone>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(StatementGroup)]),
          () => ListBuilder<StatementGroup>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(StatementItem)]),
          () => ListBuilder<StatementItem>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(LoungeItems)]),
          () => ListBuilder<LoungeItems>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SupplierDto)]),
          () => ListBuilder<SupplierDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(SupplierLotusResponse)]),
          () => ListBuilder<SupplierLotusResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SupplierPinCodeDto)]),
          () => ListBuilder<SupplierPinCodeDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SupplierProductDto)]),
          () => ListBuilder<SupplierProductDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TelecomDto)]),
          () => ListBuilder<TelecomDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TelecomLotusResponse)]),
          () => ListBuilder<TelecomLotusResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupServiceDto)]),
          () => ListBuilder<TopupServiceDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValue)]),
          () => ListBuilder<TopupValue>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TopupValueDto)]),
          () => ListBuilder<TopupValueDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TopupValueLotusResponse)]),
          () => ListBuilder<TopupValueLotusResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TransInquiryDto)]),
          () => ListBuilder<TransInquiryDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransInquiryStepDto)]),
          () => ListBuilder<TransInquiryStepDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(TransactionCard)]),
          () => ListBuilder<TransactionCard>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionCategory)]),
          () => ListBuilder<TransactionCategory>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionCategoryResponse)]),
          () => ListBuilder<TransactionCategoryResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionItemMapper)]),
          () => ListBuilder<TransactionItemMapper>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionStatistical)]),
          () => ListBuilder<TransactionStatistical>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionStatisticalItemDto)]),
          () => ListBuilder<TransactionStatisticalItemDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionTemplateDto)]),
          () => ListBuilder<TransactionTemplateDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransactionTypeResponse)]),
          () => ListBuilder<TransactionTypeResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransferRequestDetailResponse)]),
          () => ListBuilder<TransferRequestDetailResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(TransferScheduleResponse)]),
          () => ListBuilder<TransferScheduleResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(UserEnterpriseInfo)]),
          () => ListBuilder<UserEnterpriseInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(UserLogworkItem)]),
          () => ListBuilder<UserLogworkItem>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(VersionConfigsResponse)]),
          () => ListBuilder<VersionConfigsResponse>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(VipAccountInfoDto)]),
          () => ListBuilder<VipAccountInfoDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(VnpBillInfo)]),
          () => ListBuilder<VnpBillInfo>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(VnpServiceInfo)]),
          () => ListBuilder<VnpServiceInfo>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(WithdrawalCodeResponse)]),
          () => ListBuilder<WithdrawalCodeResponse>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(WithdrawalCodeResponse)]),
          () => ListBuilder<WithdrawalCodeResponse>())
      ..addBuilderFactory(
          const FullType(BuiltSet, const [const FullType(String)]),
          () => SetBuilder<String>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
