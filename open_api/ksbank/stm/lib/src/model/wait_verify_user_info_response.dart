//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'wait_verify_user_info_response.g.dart';

/// WaitVerifyUserInfoResponse
///
/// Properties:
/// * [openAccountStatus]
/// * [agentId]
/// * [agentName]
@BuiltValue()
abstract class WaitVerifyUserInfoResponse
    implements
        Built<WaitVerifyUserInfoResponse, WaitVerifyUserInfoResponseBuilder> {
  @BuiltValueField(wireName: r'openAccountStatus')
  WaitVerifyUserInfoResponseOpenAccountStatusEnum? get openAccountStatus;
  // enum openAccountStatusEnum {  PENDING,  VERIFIED_OTP,  VERIFIED_USER_INFO,  VERIFY_USER_INFO_FAILED,  VERIFIED_CONTRACT,  RESCAN_CONTRACT,  COMPLETED,  CANCELED,  REPRINT_CONTRACT,  };

  @BuiltValueField(wireName: r'agentId')
  String? get agentId;

  @BuiltValueField(wireName: r'agentName')
  String? get agentName;

  WaitVerifyUserInfoResponse._();

  factory WaitVerifyUserInfoResponse(
          [void updates(WaitVerifyUserInfoResponseBuilder b)]) =
      _$WaitVerifyUserInfoResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(WaitVerifyUserInfoResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<WaitVerifyUserInfoResponse> get serializer =>
      _$WaitVerifyUserInfoResponseSerializer();
}

class _$WaitVerifyUserInfoResponseSerializer
    implements PrimitiveSerializer<WaitVerifyUserInfoResponse> {
  @override
  final Iterable<Type> types = const [
    WaitVerifyUserInfoResponse,
    _$WaitVerifyUserInfoResponse
  ];

  @override
  final String wireName = r'WaitVerifyUserInfoResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    WaitVerifyUserInfoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.openAccountStatus != null) {
      yield r'openAccountStatus';
      yield serializers.serialize(
        object.openAccountStatus,
        specifiedType: const FullType.nullable(
            WaitVerifyUserInfoResponseOpenAccountStatusEnum),
      );
    }
    if (object.agentId != null) {
      yield r'agentId';
      yield serializers.serialize(
        object.agentId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agentName != null) {
      yield r'agentName';
      yield serializers.serialize(
        object.agentName,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    WaitVerifyUserInfoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required WaitVerifyUserInfoResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'openAccountStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                WaitVerifyUserInfoResponseOpenAccountStatusEnum),
          ) as WaitVerifyUserInfoResponseOpenAccountStatusEnum?;
          if (valueDes == null) continue;
          result.openAccountStatus = valueDes;
          break;
        case r'agentId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentId = valueDes;
          break;
        case r'agentName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  WaitVerifyUserInfoResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = WaitVerifyUserInfoResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class WaitVerifyUserInfoResponseOpenAccountStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum PENDING =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_PENDING;
  @BuiltValueEnumConst(wireName: r'VERIFIED_OTP')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum VERIFIED_OTP =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_OTP;
  @BuiltValueEnumConst(wireName: r'VERIFIED_USER_INFO')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum
      VERIFIED_USER_INFO =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_USER_INFO;
  @BuiltValueEnumConst(wireName: r'VERIFY_USER_INFO_FAILED')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum
      VERIFY_USER_INFO_FAILED =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED;
  @BuiltValueEnumConst(wireName: r'VERIFIED_CONTRACT')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum
      VERIFIED_CONTRACT =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_CONTRACT;
  @BuiltValueEnumConst(wireName: r'RESCAN_CONTRACT')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum RESCAN_CONTRACT =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_RESCAN_CONTRACT;
  @BuiltValueEnumConst(wireName: r'COMPLETED')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum COMPLETED =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_COMPLETED;
  @BuiltValueEnumConst(wireName: r'CANCELED')
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum CANCELED =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_CANCELED;
  @BuiltValueEnumConst(wireName: r'REPRINT_CONTRACT', fallback: true)
  static const WaitVerifyUserInfoResponseOpenAccountStatusEnum
      REPRINT_CONTRACT =
      _$waitVerifyUserInfoResponseOpenAccountStatusEnum_REPRINT_CONTRACT;

  static Serializer<WaitVerifyUserInfoResponseOpenAccountStatusEnum>
      get serializer =>
          _$waitVerifyUserInfoResponseOpenAccountStatusEnumSerializer;

  const WaitVerifyUserInfoResponseOpenAccountStatusEnum._(String name)
      : super(name);

  static BuiltSet<WaitVerifyUserInfoResponseOpenAccountStatusEnum> get values =>
      _$waitVerifyUserInfoResponseOpenAccountStatusEnumValues;
  static WaitVerifyUserInfoResponseOpenAccountStatusEnum valueOf(String name) =>
      _$waitVerifyUserInfoResponseOpenAccountStatusEnumValueOf(name);
}
