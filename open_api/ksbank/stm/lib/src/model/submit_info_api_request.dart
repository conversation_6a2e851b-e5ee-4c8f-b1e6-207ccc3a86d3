//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_stm/src/model/date.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'submit_info_api_request.g.dart';

/// SubmitInfoApiRequest
///
/// Properties:
/// * [idCardIssuePlc]
/// * [gender]
/// * [idCardImageDown]
/// * [idCardImageUp]
/// * [city]
/// * [state]
/// * [contactAddress]
/// * [permanentAddress]
/// * [issueDate]
/// * [email]
/// * [hometown]
/// * [phoneNumber]
/// * [nationality]
/// * [dob]
/// * [idCardNumber]
/// * [name]
@BuiltValue()
abstract class SubmitInfoApiRequest
    implements Built<SubmitInfoApiRequest, SubmitInfoApiRequestBuilder> {
  @BuiltValueField(wireName: r'idCardIssuePlc')
  String? get idCardIssuePlc;

  @BuiltValueField(wireName: r'gender')
  SubmitInfoApiRequestGenderEnum? get gender;
  // enum genderEnum {  M,  F,  };

  @BuiltValueField(wireName: r'idCardImageDown')
  String? get idCardImageDown;

  @BuiltValueField(wireName: r'idCardImageUp')
  String? get idCardImageUp;

  @BuiltValueField(wireName: r'city')
  String? get city;

  @BuiltValueField(wireName: r'state')
  String? get state;

  @BuiltValueField(wireName: r'contactAddress')
  String? get contactAddress;

  @BuiltValueField(wireName: r'permanentAddress')
  String? get permanentAddress;

  @BuiltValueField(wireName: r'issueDate')
  Date? get issueDate;

  @BuiltValueField(wireName: r'email')
  String? get email;

  @BuiltValueField(wireName: r'hometown')
  String? get hometown;

  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  @BuiltValueField(wireName: r'nationality')
  SubmitInfoApiRequestNationalityEnum? get nationality;
  // enum nationalityEnum {  VN,  };

  @BuiltValueField(wireName: r'dob')
  Date? get dob;

  @BuiltValueField(wireName: r'idCardNumber')
  String? get idCardNumber;

  @BuiltValueField(wireName: r'name')
  String? get name;

  SubmitInfoApiRequest._();

  factory SubmitInfoApiRequest([void updates(SubmitInfoApiRequestBuilder b)]) =
      _$SubmitInfoApiRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SubmitInfoApiRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SubmitInfoApiRequest> get serializer =>
      _$SubmitInfoApiRequestSerializer();
}

class _$SubmitInfoApiRequestSerializer
    implements PrimitiveSerializer<SubmitInfoApiRequest> {
  @override
  final Iterable<Type> types = const [
    SubmitInfoApiRequest,
    _$SubmitInfoApiRequest
  ];

  @override
  final String wireName = r'SubmitInfoApiRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SubmitInfoApiRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.idCardIssuePlc != null) {
      yield r'idCardIssuePlc';
      yield serializers.serialize(
        object.idCardIssuePlc,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.gender != null) {
      yield r'gender';
      yield serializers.serialize(
        object.gender,
        specifiedType: const FullType.nullable(SubmitInfoApiRequestGenderEnum),
      );
    }
    if (object.idCardImageDown != null) {
      yield r'idCardImageDown';
      yield serializers.serialize(
        object.idCardImageDown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardImageUp != null) {
      yield r'idCardImageUp';
      yield serializers.serialize(
        object.idCardImageUp,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.city != null) {
      yield r'city';
      yield serializers.serialize(
        object.city,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.state != null) {
      yield r'state';
      yield serializers.serialize(
        object.state,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contactAddress != null) {
      yield r'contactAddress';
      yield serializers.serialize(
        object.contactAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.permanentAddress != null) {
      yield r'permanentAddress';
      yield serializers.serialize(
        object.permanentAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.issueDate != null) {
      yield r'issueDate';
      yield serializers.serialize(
        object.issueDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.hometown != null) {
      yield r'hometown';
      yield serializers.serialize(
        object.hometown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.nationality != null) {
      yield r'nationality';
      yield serializers.serialize(
        object.nationality,
        specifiedType:
            const FullType.nullable(SubmitInfoApiRequestNationalityEnum),
      );
    }
    if (object.dob != null) {
      yield r'dob';
      yield serializers.serialize(
        object.dob,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.idCardNumber != null) {
      yield r'idCardNumber';
      yield serializers.serialize(
        object.idCardNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    SubmitInfoApiRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SubmitInfoApiRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'idCardIssuePlc':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardIssuePlc = valueDes;
          break;
        case r'gender':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(SubmitInfoApiRequestGenderEnum),
          ) as SubmitInfoApiRequestGenderEnum?;
          if (valueDes == null) continue;
          result.gender = valueDes;
          break;
        case r'idCardImageDown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardImageDown = valueDes;
          break;
        case r'idCardImageUp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardImageUp = valueDes;
          break;
        case r'city':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.city = valueDes;
          break;
        case r'state':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.state = valueDes;
          break;
        case r'contactAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contactAddress = valueDes;
          break;
        case r'permanentAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.permanentAddress = valueDes;
          break;
        case r'issueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.issueDate = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        case r'hometown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hometown = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'nationality':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(SubmitInfoApiRequestNationalityEnum),
          ) as SubmitInfoApiRequestNationalityEnum?;
          if (valueDes == null) continue;
          result.nationality = valueDes;
          break;
        case r'dob':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.dob = valueDes;
          break;
        case r'idCardNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardNumber = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SubmitInfoApiRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SubmitInfoApiRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class SubmitInfoApiRequestGenderEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'M')
  static const SubmitInfoApiRequestGenderEnum M =
      _$submitInfoApiRequestGenderEnum_M;
  @BuiltValueEnumConst(wireName: r'F', fallback: true)
  static const SubmitInfoApiRequestGenderEnum F =
      _$submitInfoApiRequestGenderEnum_F;

  static Serializer<SubmitInfoApiRequestGenderEnum> get serializer =>
      _$submitInfoApiRequestGenderEnumSerializer;

  const SubmitInfoApiRequestGenderEnum._(String name) : super(name);

  static BuiltSet<SubmitInfoApiRequestGenderEnum> get values =>
      _$submitInfoApiRequestGenderEnumValues;
  static SubmitInfoApiRequestGenderEnum valueOf(String name) =>
      _$submitInfoApiRequestGenderEnumValueOf(name);
}

class SubmitInfoApiRequestNationalityEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'VN', fallback: true)
  static const SubmitInfoApiRequestNationalityEnum VN =
      _$submitInfoApiRequestNationalityEnum_VN;

  static Serializer<SubmitInfoApiRequestNationalityEnum> get serializer =>
      _$submitInfoApiRequestNationalityEnumSerializer;

  const SubmitInfoApiRequestNationalityEnum._(String name) : super(name);

  static BuiltSet<SubmitInfoApiRequestNationalityEnum> get values =>
      _$submitInfoApiRequestNationalityEnumValues;
  static SubmitInfoApiRequestNationalityEnum valueOf(String name) =>
      _$submitInfoApiRequestNationalityEnumValueOf(name);
}
