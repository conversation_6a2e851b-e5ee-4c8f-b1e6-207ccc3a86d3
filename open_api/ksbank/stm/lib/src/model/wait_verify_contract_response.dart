//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'wait_verify_contract_response.g.dart';

/// WaitVerifyContractResponse
///
/// Properties:
/// * [openAccountStatus]
@BuiltValue()
abstract class WaitVerifyContractResponse
    implements
        Built<WaitVerifyContractResponse, WaitVerifyContractResponseBuilder> {
  @BuiltValueField(wireName: r'openAccountStatus')
  WaitVerifyContractResponseOpenAccountStatusEnum? get openAccountStatus;
  // enum openAccountStatusEnum {  PENDING,  VERIFIED_OTP,  VERIFIED_USER_INFO,  VERIFY_USER_INFO_FAILED,  VERIFIED_CONTRACT,  RESCAN_CONTRACT,  COMPLETED,  CANCELED,  REPRINT_CONTRACT,  };

  WaitVerifyContractResponse._();

  factory WaitVerifyContractResponse(
          [void updates(WaitVerifyContractResponseBuilder b)]) =
      _$WaitVerifyContractResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(WaitVerifyContractResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<WaitVerifyContractResponse> get serializer =>
      _$WaitVerifyContractResponseSerializer();
}

class _$WaitVerifyContractResponseSerializer
    implements PrimitiveSerializer<WaitVerifyContractResponse> {
  @override
  final Iterable<Type> types = const [
    WaitVerifyContractResponse,
    _$WaitVerifyContractResponse
  ];

  @override
  final String wireName = r'WaitVerifyContractResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    WaitVerifyContractResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.openAccountStatus != null) {
      yield r'openAccountStatus';
      yield serializers.serialize(
        object.openAccountStatus,
        specifiedType: const FullType.nullable(
            WaitVerifyContractResponseOpenAccountStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    WaitVerifyContractResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required WaitVerifyContractResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'openAccountStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                WaitVerifyContractResponseOpenAccountStatusEnum),
          ) as WaitVerifyContractResponseOpenAccountStatusEnum?;
          if (valueDes == null) continue;
          result.openAccountStatus = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  WaitVerifyContractResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = WaitVerifyContractResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class WaitVerifyContractResponseOpenAccountStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const WaitVerifyContractResponseOpenAccountStatusEnum PENDING =
      _$waitVerifyContractResponseOpenAccountStatusEnum_PENDING;
  @BuiltValueEnumConst(wireName: r'VERIFIED_OTP')
  static const WaitVerifyContractResponseOpenAccountStatusEnum VERIFIED_OTP =
      _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_OTP;
  @BuiltValueEnumConst(wireName: r'VERIFIED_USER_INFO')
  static const WaitVerifyContractResponseOpenAccountStatusEnum
      VERIFIED_USER_INFO =
      _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_USER_INFO;
  @BuiltValueEnumConst(wireName: r'VERIFY_USER_INFO_FAILED')
  static const WaitVerifyContractResponseOpenAccountStatusEnum
      VERIFY_USER_INFO_FAILED =
      _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED;
  @BuiltValueEnumConst(wireName: r'VERIFIED_CONTRACT')
  static const WaitVerifyContractResponseOpenAccountStatusEnum
      VERIFIED_CONTRACT =
      _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_CONTRACT;
  @BuiltValueEnumConst(wireName: r'RESCAN_CONTRACT')
  static const WaitVerifyContractResponseOpenAccountStatusEnum RESCAN_CONTRACT =
      _$waitVerifyContractResponseOpenAccountStatusEnum_RESCAN_CONTRACT;
  @BuiltValueEnumConst(wireName: r'COMPLETED')
  static const WaitVerifyContractResponseOpenAccountStatusEnum COMPLETED =
      _$waitVerifyContractResponseOpenAccountStatusEnum_COMPLETED;
  @BuiltValueEnumConst(wireName: r'CANCELED')
  static const WaitVerifyContractResponseOpenAccountStatusEnum CANCELED =
      _$waitVerifyContractResponseOpenAccountStatusEnum_CANCELED;
  @BuiltValueEnumConst(wireName: r'REPRINT_CONTRACT', fallback: true)
  static const WaitVerifyContractResponseOpenAccountStatusEnum
      REPRINT_CONTRACT =
      _$waitVerifyContractResponseOpenAccountStatusEnum_REPRINT_CONTRACT;

  static Serializer<WaitVerifyContractResponseOpenAccountStatusEnum>
      get serializer =>
          _$waitVerifyContractResponseOpenAccountStatusEnumSerializer;

  const WaitVerifyContractResponseOpenAccountStatusEnum._(String name)
      : super(name);

  static BuiltSet<WaitVerifyContractResponseOpenAccountStatusEnum> get values =>
      _$waitVerifyContractResponseOpenAccountStatusEnumValues;
  static WaitVerifyContractResponseOpenAccountStatusEnum valueOf(String name) =>
      _$waitVerifyContractResponseOpenAccountStatusEnumValueOf(name);
}
