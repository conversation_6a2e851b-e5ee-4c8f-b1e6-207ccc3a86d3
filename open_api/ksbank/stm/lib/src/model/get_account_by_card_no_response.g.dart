// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_account_by_card_no_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetAccountByCardNoResponse extends GetAccountByCardNoResponse {
  @override
  final String? accountNo;
  @override
  final String? userCode;
  @override
  final String? cardName;

  factory _$GetAccountByCardNoResponse(
          [void Function(GetAccountByCardNoResponseBuilder)? updates]) =>
      (new GetAccountByCardNoResponseBuilder()..update(updates))._build();

  _$GetAccountByCardNoResponse._({this.accountNo, this.userCode, this.cardName})
      : super._();

  @override
  GetAccountByCardNoResponse rebuild(
          void Function(GetAccountByCardNoResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetAccountByCardNoResponseBuilder toBuilder() =>
      new GetAccountByCardNoResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetAccountByCardNoResponse &&
        accountNo == other.accountNo &&
        userCode == other.userCode &&
        cardName == other.cardName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, userCode.hashCode);
    _$hash = $jc(_$hash, cardName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetAccountByCardNoResponse')
          ..add('accountNo', accountNo)
          ..add('userCode', userCode)
          ..add('cardName', cardName))
        .toString();
  }
}

class GetAccountByCardNoResponseBuilder
    implements
        Builder<GetAccountByCardNoResponse, GetAccountByCardNoResponseBuilder> {
  _$GetAccountByCardNoResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _userCode;
  String? get userCode => _$this._userCode;
  set userCode(String? userCode) => _$this._userCode = userCode;

  String? _cardName;
  String? get cardName => _$this._cardName;
  set cardName(String? cardName) => _$this._cardName = cardName;

  GetAccountByCardNoResponseBuilder() {
    GetAccountByCardNoResponse._defaults(this);
  }

  GetAccountByCardNoResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _userCode = $v.userCode;
      _cardName = $v.cardName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetAccountByCardNoResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetAccountByCardNoResponse;
  }

  @override
  void update(void Function(GetAccountByCardNoResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetAccountByCardNoResponse build() => _build();

  _$GetAccountByCardNoResponse _build() {
    final _$result = _$v ??
        new _$GetAccountByCardNoResponse._(
            accountNo: accountNo, userCode: userCode, cardName: cardName);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
