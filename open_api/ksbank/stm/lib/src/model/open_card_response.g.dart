// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenCardResponse extends OpenCardResponse {
  @override
  final String? cifNo;
  @override
  final String? userId;
  @override
  final String? userName;
  @override
  final String? cardNumber;
  @override
  final String? accountNumber;
  @override
  final DateTime? openCardTime;

  factory _$OpenCardResponse(
          [void Function(OpenCardResponseBuilder)? updates]) =>
      (new OpenCardResponseBuilder()..update(updates))._build();

  _$OpenCardResponse._(
      {this.cifNo,
      this.userId,
      this.userName,
      this.cardNumber,
      this.accountNumber,
      this.openCardTime})
      : super._();

  @override
  OpenCardResponse rebuild(void Function(OpenCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenCardResponseBuilder toBuilder() =>
      new OpenCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenCardResponse &&
        cifNo == other.cifNo &&
        userId == other.userId &&
        userName == other.userName &&
        cardNumber == other.cardNumber &&
        accountNumber == other.accountNumber &&
        openCardTime == other.openCardTime;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, userName.hashCode);
    _$hash = $jc(_$hash, cardNumber.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, openCardTime.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenCardResponse')
          ..add('cifNo', cifNo)
          ..add('userId', userId)
          ..add('userName', userName)
          ..add('cardNumber', cardNumber)
          ..add('accountNumber', accountNumber)
          ..add('openCardTime', openCardTime))
        .toString();
  }
}

class OpenCardResponseBuilder
    implements Builder<OpenCardResponse, OpenCardResponseBuilder> {
  _$OpenCardResponse? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  String? _userName;
  String? get userName => _$this._userName;
  set userName(String? userName) => _$this._userName = userName;

  String? _cardNumber;
  String? get cardNumber => _$this._cardNumber;
  set cardNumber(String? cardNumber) => _$this._cardNumber = cardNumber;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  DateTime? _openCardTime;
  DateTime? get openCardTime => _$this._openCardTime;
  set openCardTime(DateTime? openCardTime) =>
      _$this._openCardTime = openCardTime;

  OpenCardResponseBuilder() {
    OpenCardResponse._defaults(this);
  }

  OpenCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _userId = $v.userId;
      _userName = $v.userName;
      _cardNumber = $v.cardNumber;
      _accountNumber = $v.accountNumber;
      _openCardTime = $v.openCardTime;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenCardResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$OpenCardResponse;
  }

  @override
  void update(void Function(OpenCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenCardResponse build() => _build();

  _$OpenCardResponse _build() {
    final _$result = _$v ??
        new _$OpenCardResponse._(
            cifNo: cifNo,
            userId: userId,
            userName: userName,
            cardNumber: cardNumber,
            accountNumber: accountNumber,
            openCardTime: openCardTime);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
