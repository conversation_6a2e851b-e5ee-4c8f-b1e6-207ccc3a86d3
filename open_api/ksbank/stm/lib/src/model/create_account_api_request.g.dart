// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_account_api_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CreateAccountApiRequestGenderEnum _$createAccountApiRequestGenderEnum_M =
    const CreateAccountApiRequestGenderEnum._('M');
const CreateAccountApiRequestGenderEnum _$createAccountApiRequestGenderEnum_F =
    const CreateAccountApiRequestGenderEnum._('F');

CreateAccountApiRequestGenderEnum _$createAccountApiRequestGenderEnumValueOf(
    String name) {
  switch (name) {
    case 'M':
      return _$createAccountApiRequestGenderEnum_M;
    case 'F':
      return _$createAccountApiRequestGenderEnum_F;
    default:
      return _$createAccountApiRequestGenderEnum_F;
  }
}

final BuiltSet<CreateAccountApiRequestGenderEnum>
    _$createAccountApiRequestGenderEnumValues = new BuiltSet<
        CreateAccountApiRequestGenderEnum>(const <CreateAccountApiRequestGenderEnum>[
  _$createAccountApiRequestGenderEnum_M,
  _$createAccountApiRequestGenderEnum_F,
]);

const CreateAccountApiRequestNationalityEnum
    _$createAccountApiRequestNationalityEnum_VN =
    const CreateAccountApiRequestNationalityEnum._('VN');

CreateAccountApiRequestNationalityEnum
    _$createAccountApiRequestNationalityEnumValueOf(String name) {
  switch (name) {
    case 'VN':
      return _$createAccountApiRequestNationalityEnum_VN;
    default:
      return _$createAccountApiRequestNationalityEnum_VN;
  }
}

final BuiltSet<CreateAccountApiRequestNationalityEnum>
    _$createAccountApiRequestNationalityEnumValues = new BuiltSet<
        CreateAccountApiRequestNationalityEnum>(const <CreateAccountApiRequestNationalityEnum>[
  _$createAccountApiRequestNationalityEnum_VN,
]);

Serializer<CreateAccountApiRequestGenderEnum>
    _$createAccountApiRequestGenderEnumSerializer =
    new _$CreateAccountApiRequestGenderEnumSerializer();
Serializer<CreateAccountApiRequestNationalityEnum>
    _$createAccountApiRequestNationalityEnumSerializer =
    new _$CreateAccountApiRequestNationalityEnumSerializer();

class _$CreateAccountApiRequestGenderEnumSerializer
    implements PrimitiveSerializer<CreateAccountApiRequestGenderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'M': 'M',
    'F': 'F',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'M': 'M',
    'F': 'F',
  };

  @override
  final Iterable<Type> types = const <Type>[CreateAccountApiRequestGenderEnum];
  @override
  final String wireName = 'CreateAccountApiRequestGenderEnum';

  @override
  Object serialize(
          Serializers serializers, CreateAccountApiRequestGenderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CreateAccountApiRequestGenderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CreateAccountApiRequestGenderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CreateAccountApiRequestNationalityEnumSerializer
    implements PrimitiveSerializer<CreateAccountApiRequestNationalityEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VN': 'VN',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VN': 'VN',
  };

  @override
  final Iterable<Type> types = const <Type>[
    CreateAccountApiRequestNationalityEnum
  ];
  @override
  final String wireName = 'CreateAccountApiRequestNationalityEnum';

  @override
  Object serialize(Serializers serializers,
          CreateAccountApiRequestNationalityEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CreateAccountApiRequestNationalityEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CreateAccountApiRequestNationalityEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CreateAccountApiRequest extends CreateAccountApiRequest {
  @override
  final String? idCardNumber;
  @override
  final String? phoneNumber;
  @override
  final String? email;
  @override
  final String? name;
  @override
  final Date? dob;
  @override
  final CreateAccountApiRequestGenderEnum? gender;
  @override
  final CreateAccountApiRequestNationalityEnum? nationality;
  @override
  final String? hometown;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? idCardIssuePlc;
  @override
  final Date? idCardIssueDt;
  @override
  final String? permanentAddress;
  @override
  final String? contactAddress;
  @override
  final String? idCardImageUp;
  @override
  final String? idCardImageDown;
  @override
  final String? openAccountFormUp;
  @override
  final String? openAccountFormDown;
  @override
  final String? contractFormUp;
  @override
  final String? contractFormDown;
  @override
  final String? servicePack;
  @override
  final String? cardNumber;
  @override
  final Date? issueDate;
  @override
  final Date? expirationDate;
  @override
  final String? track1;
  @override
  final String? branchCode;
  @override
  final bool? isOpenCard;
  @override
  final bool? openCard;

  factory _$CreateAccountApiRequest(
          [void Function(CreateAccountApiRequestBuilder)? updates]) =>
      (new CreateAccountApiRequestBuilder()..update(updates))._build();

  _$CreateAccountApiRequest._(
      {this.idCardNumber,
      this.phoneNumber,
      this.email,
      this.name,
      this.dob,
      this.gender,
      this.nationality,
      this.hometown,
      this.city,
      this.state,
      this.idCardIssuePlc,
      this.idCardIssueDt,
      this.permanentAddress,
      this.contactAddress,
      this.idCardImageUp,
      this.idCardImageDown,
      this.openAccountFormUp,
      this.openAccountFormDown,
      this.contractFormUp,
      this.contractFormDown,
      this.servicePack,
      this.cardNumber,
      this.issueDate,
      this.expirationDate,
      this.track1,
      this.branchCode,
      this.isOpenCard,
      this.openCard})
      : super._();

  @override
  CreateAccountApiRequest rebuild(
          void Function(CreateAccountApiRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateAccountApiRequestBuilder toBuilder() =>
      new CreateAccountApiRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateAccountApiRequest &&
        idCardNumber == other.idCardNumber &&
        phoneNumber == other.phoneNumber &&
        email == other.email &&
        name == other.name &&
        dob == other.dob &&
        gender == other.gender &&
        nationality == other.nationality &&
        hometown == other.hometown &&
        city == other.city &&
        state == other.state &&
        idCardIssuePlc == other.idCardIssuePlc &&
        idCardIssueDt == other.idCardIssueDt &&
        permanentAddress == other.permanentAddress &&
        contactAddress == other.contactAddress &&
        idCardImageUp == other.idCardImageUp &&
        idCardImageDown == other.idCardImageDown &&
        openAccountFormUp == other.openAccountFormUp &&
        openAccountFormDown == other.openAccountFormDown &&
        contractFormUp == other.contractFormUp &&
        contractFormDown == other.contractFormDown &&
        servicePack == other.servicePack &&
        cardNumber == other.cardNumber &&
        issueDate == other.issueDate &&
        expirationDate == other.expirationDate &&
        track1 == other.track1 &&
        branchCode == other.branchCode &&
        isOpenCard == other.isOpenCard &&
        openCard == other.openCard;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, idCardNumber.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, dob.hashCode);
    _$hash = $jc(_$hash, gender.hashCode);
    _$hash = $jc(_$hash, nationality.hashCode);
    _$hash = $jc(_$hash, hometown.hashCode);
    _$hash = $jc(_$hash, city.hashCode);
    _$hash = $jc(_$hash, state.hashCode);
    _$hash = $jc(_$hash, idCardIssuePlc.hashCode);
    _$hash = $jc(_$hash, idCardIssueDt.hashCode);
    _$hash = $jc(_$hash, permanentAddress.hashCode);
    _$hash = $jc(_$hash, contactAddress.hashCode);
    _$hash = $jc(_$hash, idCardImageUp.hashCode);
    _$hash = $jc(_$hash, idCardImageDown.hashCode);
    _$hash = $jc(_$hash, openAccountFormUp.hashCode);
    _$hash = $jc(_$hash, openAccountFormDown.hashCode);
    _$hash = $jc(_$hash, contractFormUp.hashCode);
    _$hash = $jc(_$hash, contractFormDown.hashCode);
    _$hash = $jc(_$hash, servicePack.hashCode);
    _$hash = $jc(_$hash, cardNumber.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, expirationDate.hashCode);
    _$hash = $jc(_$hash, track1.hashCode);
    _$hash = $jc(_$hash, branchCode.hashCode);
    _$hash = $jc(_$hash, isOpenCard.hashCode);
    _$hash = $jc(_$hash, openCard.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateAccountApiRequest')
          ..add('idCardNumber', idCardNumber)
          ..add('phoneNumber', phoneNumber)
          ..add('email', email)
          ..add('name', name)
          ..add('dob', dob)
          ..add('gender', gender)
          ..add('nationality', nationality)
          ..add('hometown', hometown)
          ..add('city', city)
          ..add('state', state)
          ..add('idCardIssuePlc', idCardIssuePlc)
          ..add('idCardIssueDt', idCardIssueDt)
          ..add('permanentAddress', permanentAddress)
          ..add('contactAddress', contactAddress)
          ..add('idCardImageUp', idCardImageUp)
          ..add('idCardImageDown', idCardImageDown)
          ..add('openAccountFormUp', openAccountFormUp)
          ..add('openAccountFormDown', openAccountFormDown)
          ..add('contractFormUp', contractFormUp)
          ..add('contractFormDown', contractFormDown)
          ..add('servicePack', servicePack)
          ..add('cardNumber', cardNumber)
          ..add('issueDate', issueDate)
          ..add('expirationDate', expirationDate)
          ..add('track1', track1)
          ..add('branchCode', branchCode)
          ..add('isOpenCard', isOpenCard)
          ..add('openCard', openCard))
        .toString();
  }
}

class CreateAccountApiRequestBuilder
    implements
        Builder<CreateAccountApiRequest, CreateAccountApiRequestBuilder> {
  _$CreateAccountApiRequest? _$v;

  String? _idCardNumber;
  String? get idCardNumber => _$this._idCardNumber;
  set idCardNumber(String? idCardNumber) => _$this._idCardNumber = idCardNumber;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  Date? _dob;
  Date? get dob => _$this._dob;
  set dob(Date? dob) => _$this._dob = dob;

  CreateAccountApiRequestGenderEnum? _gender;
  CreateAccountApiRequestGenderEnum? get gender => _$this._gender;
  set gender(CreateAccountApiRequestGenderEnum? gender) =>
      _$this._gender = gender;

  CreateAccountApiRequestNationalityEnum? _nationality;
  CreateAccountApiRequestNationalityEnum? get nationality =>
      _$this._nationality;
  set nationality(CreateAccountApiRequestNationalityEnum? nationality) =>
      _$this._nationality = nationality;

  String? _hometown;
  String? get hometown => _$this._hometown;
  set hometown(String? hometown) => _$this._hometown = hometown;

  String? _city;
  String? get city => _$this._city;
  set city(String? city) => _$this._city = city;

  String? _state;
  String? get state => _$this._state;
  set state(String? state) => _$this._state = state;

  String? _idCardIssuePlc;
  String? get idCardIssuePlc => _$this._idCardIssuePlc;
  set idCardIssuePlc(String? idCardIssuePlc) =>
      _$this._idCardIssuePlc = idCardIssuePlc;

  Date? _idCardIssueDt;
  Date? get idCardIssueDt => _$this._idCardIssueDt;
  set idCardIssueDt(Date? idCardIssueDt) =>
      _$this._idCardIssueDt = idCardIssueDt;

  String? _permanentAddress;
  String? get permanentAddress => _$this._permanentAddress;
  set permanentAddress(String? permanentAddress) =>
      _$this._permanentAddress = permanentAddress;

  String? _contactAddress;
  String? get contactAddress => _$this._contactAddress;
  set contactAddress(String? contactAddress) =>
      _$this._contactAddress = contactAddress;

  String? _idCardImageUp;
  String? get idCardImageUp => _$this._idCardImageUp;
  set idCardImageUp(String? idCardImageUp) =>
      _$this._idCardImageUp = idCardImageUp;

  String? _idCardImageDown;
  String? get idCardImageDown => _$this._idCardImageDown;
  set idCardImageDown(String? idCardImageDown) =>
      _$this._idCardImageDown = idCardImageDown;

  String? _openAccountFormUp;
  String? get openAccountFormUp => _$this._openAccountFormUp;
  set openAccountFormUp(String? openAccountFormUp) =>
      _$this._openAccountFormUp = openAccountFormUp;

  String? _openAccountFormDown;
  String? get openAccountFormDown => _$this._openAccountFormDown;
  set openAccountFormDown(String? openAccountFormDown) =>
      _$this._openAccountFormDown = openAccountFormDown;

  String? _contractFormUp;
  String? get contractFormUp => _$this._contractFormUp;
  set contractFormUp(String? contractFormUp) =>
      _$this._contractFormUp = contractFormUp;

  String? _contractFormDown;
  String? get contractFormDown => _$this._contractFormDown;
  set contractFormDown(String? contractFormDown) =>
      _$this._contractFormDown = contractFormDown;

  String? _servicePack;
  String? get servicePack => _$this._servicePack;
  set servicePack(String? servicePack) => _$this._servicePack = servicePack;

  String? _cardNumber;
  String? get cardNumber => _$this._cardNumber;
  set cardNumber(String? cardNumber) => _$this._cardNumber = cardNumber;

  Date? _issueDate;
  Date? get issueDate => _$this._issueDate;
  set issueDate(Date? issueDate) => _$this._issueDate = issueDate;

  Date? _expirationDate;
  Date? get expirationDate => _$this._expirationDate;
  set expirationDate(Date? expirationDate) =>
      _$this._expirationDate = expirationDate;

  String? _track1;
  String? get track1 => _$this._track1;
  set track1(String? track1) => _$this._track1 = track1;

  String? _branchCode;
  String? get branchCode => _$this._branchCode;
  set branchCode(String? branchCode) => _$this._branchCode = branchCode;

  bool? _isOpenCard;
  bool? get isOpenCard => _$this._isOpenCard;
  set isOpenCard(bool? isOpenCard) => _$this._isOpenCard = isOpenCard;

  bool? _openCard;
  bool? get openCard => _$this._openCard;
  set openCard(bool? openCard) => _$this._openCard = openCard;

  CreateAccountApiRequestBuilder() {
    CreateAccountApiRequest._defaults(this);
  }

  CreateAccountApiRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _idCardNumber = $v.idCardNumber;
      _phoneNumber = $v.phoneNumber;
      _email = $v.email;
      _name = $v.name;
      _dob = $v.dob;
      _gender = $v.gender;
      _nationality = $v.nationality;
      _hometown = $v.hometown;
      _city = $v.city;
      _state = $v.state;
      _idCardIssuePlc = $v.idCardIssuePlc;
      _idCardIssueDt = $v.idCardIssueDt;
      _permanentAddress = $v.permanentAddress;
      _contactAddress = $v.contactAddress;
      _idCardImageUp = $v.idCardImageUp;
      _idCardImageDown = $v.idCardImageDown;
      _openAccountFormUp = $v.openAccountFormUp;
      _openAccountFormDown = $v.openAccountFormDown;
      _contractFormUp = $v.contractFormUp;
      _contractFormDown = $v.contractFormDown;
      _servicePack = $v.servicePack;
      _cardNumber = $v.cardNumber;
      _issueDate = $v.issueDate;
      _expirationDate = $v.expirationDate;
      _track1 = $v.track1;
      _branchCode = $v.branchCode;
      _isOpenCard = $v.isOpenCard;
      _openCard = $v.openCard;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateAccountApiRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CreateAccountApiRequest;
  }

  @override
  void update(void Function(CreateAccountApiRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateAccountApiRequest build() => _build();

  _$CreateAccountApiRequest _build() {
    final _$result = _$v ??
        new _$CreateAccountApiRequest._(
            idCardNumber: idCardNumber,
            phoneNumber: phoneNumber,
            email: email,
            name: name,
            dob: dob,
            gender: gender,
            nationality: nationality,
            hometown: hometown,
            city: city,
            state: state,
            idCardIssuePlc: idCardIssuePlc,
            idCardIssueDt: idCardIssueDt,
            permanentAddress: permanentAddress,
            contactAddress: contactAddress,
            idCardImageUp: idCardImageUp,
            idCardImageDown: idCardImageDown,
            openAccountFormUp: openAccountFormUp,
            openAccountFormDown: openAccountFormDown,
            contractFormUp: contractFormUp,
            contractFormDown: contractFormDown,
            servicePack: servicePack,
            cardNumber: cardNumber,
            issueDate: issueDate,
            expirationDate: expirationDate,
            track1: track1,
            branchCode: branchCode,
            isOpenCard: isOpenCard,
            openCard: openCard);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
