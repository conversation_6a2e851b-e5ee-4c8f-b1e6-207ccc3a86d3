// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stm_deposit_cash_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$StmDepositCashRequest extends StmDepositCashRequest {
  @override
  final String svTransNoRef;
  @override
  final String? stmId;
  @override
  final String? fromCardNo;
  @override
  final String? fromIdCard;
  @override
  final String? isMyAcct;
  @override
  final String? tranAmount;
  @override
  final String? descText;

  factory _$StmDepositCashRequest(
          [void Function(StmDepositCashRequestBuilder)? updates]) =>
      (new StmDepositCashRequestBuilder()..update(updates))._build();

  _$StmDepositCashRequest._(
      {required this.svTransNoRef,
      this.stmId,
      this.fromCardNo,
      this.fromIdCard,
      this.isMyAcct,
      this.tranAmount,
      this.descText})
      : super._() {
    BuiltValueNullFieldError.checkNotNull(
        svTransNoRef, r'StmDepositCashRequest', 'svTransNoRef');
  }

  @override
  StmDepositCashRequest rebuild(
          void Function(StmDepositCashRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  StmDepositCashRequestBuilder toBuilder() =>
      new StmDepositCashRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is StmDepositCashRequest &&
        svTransNoRef == other.svTransNoRef &&
        stmId == other.stmId &&
        fromCardNo == other.fromCardNo &&
        fromIdCard == other.fromIdCard &&
        isMyAcct == other.isMyAcct &&
        tranAmount == other.tranAmount &&
        descText == other.descText;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, svTransNoRef.hashCode);
    _$hash = $jc(_$hash, stmId.hashCode);
    _$hash = $jc(_$hash, fromCardNo.hashCode);
    _$hash = $jc(_$hash, fromIdCard.hashCode);
    _$hash = $jc(_$hash, isMyAcct.hashCode);
    _$hash = $jc(_$hash, tranAmount.hashCode);
    _$hash = $jc(_$hash, descText.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'StmDepositCashRequest')
          ..add('svTransNoRef', svTransNoRef)
          ..add('stmId', stmId)
          ..add('fromCardNo', fromCardNo)
          ..add('fromIdCard', fromIdCard)
          ..add('isMyAcct', isMyAcct)
          ..add('tranAmount', tranAmount)
          ..add('descText', descText))
        .toString();
  }
}

class StmDepositCashRequestBuilder
    implements Builder<StmDepositCashRequest, StmDepositCashRequestBuilder> {
  _$StmDepositCashRequest? _$v;

  String? _svTransNoRef;
  String? get svTransNoRef => _$this._svTransNoRef;
  set svTransNoRef(String? svTransNoRef) => _$this._svTransNoRef = svTransNoRef;

  String? _stmId;
  String? get stmId => _$this._stmId;
  set stmId(String? stmId) => _$this._stmId = stmId;

  String? _fromCardNo;
  String? get fromCardNo => _$this._fromCardNo;
  set fromCardNo(String? fromCardNo) => _$this._fromCardNo = fromCardNo;

  String? _fromIdCard;
  String? get fromIdCard => _$this._fromIdCard;
  set fromIdCard(String? fromIdCard) => _$this._fromIdCard = fromIdCard;

  String? _isMyAcct;
  String? get isMyAcct => _$this._isMyAcct;
  set isMyAcct(String? isMyAcct) => _$this._isMyAcct = isMyAcct;

  String? _tranAmount;
  String? get tranAmount => _$this._tranAmount;
  set tranAmount(String? tranAmount) => _$this._tranAmount = tranAmount;

  String? _descText;
  String? get descText => _$this._descText;
  set descText(String? descText) => _$this._descText = descText;

  StmDepositCashRequestBuilder() {
    StmDepositCashRequest._defaults(this);
  }

  StmDepositCashRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _svTransNoRef = $v.svTransNoRef;
      _stmId = $v.stmId;
      _fromCardNo = $v.fromCardNo;
      _fromIdCard = $v.fromIdCard;
      _isMyAcct = $v.isMyAcct;
      _tranAmount = $v.tranAmount;
      _descText = $v.descText;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(StmDepositCashRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$StmDepositCashRequest;
  }

  @override
  void update(void Function(StmDepositCashRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  StmDepositCashRequest build() => _build();

  _$StmDepositCashRequest _build() {
    final _$result = _$v ??
        new _$StmDepositCashRequest._(
            svTransNoRef: BuiltValueNullFieldError.checkNotNull(
                svTransNoRef, r'StmDepositCashRequest', 'svTransNoRef'),
            stmId: stmId,
            fromCardNo: fromCardNo,
            fromIdCard: fromIdCard,
            isMyAcct: isMyAcct,
            tranAmount: tranAmount,
            descText: descText);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
