// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wait_verify_user_info_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_PENDING =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('PENDING');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_OTP =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('VERIFIED_OTP');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_USER_INFO =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._(
        'VERIFIED_USER_INFO');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._(
        'VERIFY_USER_INFO_FAILED');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_CONTRACT =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._(
        'VERIFIED_CONTRACT');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_RESCAN_CONTRACT =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('RESCAN_CONTRACT');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_COMPLETED =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('COMPLETED');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_CANCELED =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('CANCELED');
const WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnum_REPRINT_CONTRACT =
    const WaitVerifyUserInfoResponseOpenAccountStatusEnum._('REPRINT_CONTRACT');

WaitVerifyUserInfoResponseOpenAccountStatusEnum
    _$waitVerifyUserInfoResponseOpenAccountStatusEnumValueOf(String name) {
  switch (name) {
    case 'PENDING':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_PENDING;
    case 'VERIFIED_OTP':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_OTP;
    case 'VERIFIED_USER_INFO':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_USER_INFO;
    case 'VERIFY_USER_INFO_FAILED':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED;
    case 'VERIFIED_CONTRACT':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_CONTRACT;
    case 'RESCAN_CONTRACT':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_RESCAN_CONTRACT;
    case 'COMPLETED':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_COMPLETED;
    case 'CANCELED':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_CANCELED;
    case 'REPRINT_CONTRACT':
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_REPRINT_CONTRACT;
    default:
      return _$waitVerifyUserInfoResponseOpenAccountStatusEnum_REPRINT_CONTRACT;
  }
}

final BuiltSet<WaitVerifyUserInfoResponseOpenAccountStatusEnum>
    _$waitVerifyUserInfoResponseOpenAccountStatusEnumValues = new BuiltSet<
        WaitVerifyUserInfoResponseOpenAccountStatusEnum>(const <WaitVerifyUserInfoResponseOpenAccountStatusEnum>[
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_PENDING,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_OTP,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_USER_INFO,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_VERIFIED_CONTRACT,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_RESCAN_CONTRACT,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_COMPLETED,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_CANCELED,
  _$waitVerifyUserInfoResponseOpenAccountStatusEnum_REPRINT_CONTRACT,
]);

Serializer<WaitVerifyUserInfoResponseOpenAccountStatusEnum>
    _$waitVerifyUserInfoResponseOpenAccountStatusEnumSerializer =
    new _$WaitVerifyUserInfoResponseOpenAccountStatusEnumSerializer();

class _$WaitVerifyUserInfoResponseOpenAccountStatusEnumSerializer
    implements
        PrimitiveSerializer<WaitVerifyUserInfoResponseOpenAccountStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PENDING': 'PENDING',
    'VERIFIED_OTP': 'VERIFIED_OTP',
    'VERIFIED_USER_INFO': 'VERIFIED_USER_INFO',
    'VERIFY_USER_INFO_FAILED': 'VERIFY_USER_INFO_FAILED',
    'VERIFIED_CONTRACT': 'VERIFIED_CONTRACT',
    'RESCAN_CONTRACT': 'RESCAN_CONTRACT',
    'COMPLETED': 'COMPLETED',
    'CANCELED': 'CANCELED',
    'REPRINT_CONTRACT': 'REPRINT_CONTRACT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PENDING': 'PENDING',
    'VERIFIED_OTP': 'VERIFIED_OTP',
    'VERIFIED_USER_INFO': 'VERIFIED_USER_INFO',
    'VERIFY_USER_INFO_FAILED': 'VERIFY_USER_INFO_FAILED',
    'VERIFIED_CONTRACT': 'VERIFIED_CONTRACT',
    'RESCAN_CONTRACT': 'RESCAN_CONTRACT',
    'COMPLETED': 'COMPLETED',
    'CANCELED': 'CANCELED',
    'REPRINT_CONTRACT': 'REPRINT_CONTRACT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    WaitVerifyUserInfoResponseOpenAccountStatusEnum
  ];
  @override
  final String wireName = 'WaitVerifyUserInfoResponseOpenAccountStatusEnum';

  @override
  Object serialize(Serializers serializers,
          WaitVerifyUserInfoResponseOpenAccountStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  WaitVerifyUserInfoResponseOpenAccountStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      WaitVerifyUserInfoResponseOpenAccountStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$WaitVerifyUserInfoResponse extends WaitVerifyUserInfoResponse {
  @override
  final WaitVerifyUserInfoResponseOpenAccountStatusEnum? openAccountStatus;
  @override
  final String? agentId;
  @override
  final String? agentName;

  factory _$WaitVerifyUserInfoResponse(
          [void Function(WaitVerifyUserInfoResponseBuilder)? updates]) =>
      (new WaitVerifyUserInfoResponseBuilder()..update(updates))._build();

  _$WaitVerifyUserInfoResponse._(
      {this.openAccountStatus, this.agentId, this.agentName})
      : super._();

  @override
  WaitVerifyUserInfoResponse rebuild(
          void Function(WaitVerifyUserInfoResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  WaitVerifyUserInfoResponseBuilder toBuilder() =>
      new WaitVerifyUserInfoResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is WaitVerifyUserInfoResponse &&
        openAccountStatus == other.openAccountStatus &&
        agentId == other.agentId &&
        agentName == other.agentName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, openAccountStatus.hashCode);
    _$hash = $jc(_$hash, agentId.hashCode);
    _$hash = $jc(_$hash, agentName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'WaitVerifyUserInfoResponse')
          ..add('openAccountStatus', openAccountStatus)
          ..add('agentId', agentId)
          ..add('agentName', agentName))
        .toString();
  }
}

class WaitVerifyUserInfoResponseBuilder
    implements
        Builder<WaitVerifyUserInfoResponse, WaitVerifyUserInfoResponseBuilder> {
  _$WaitVerifyUserInfoResponse? _$v;

  WaitVerifyUserInfoResponseOpenAccountStatusEnum? _openAccountStatus;
  WaitVerifyUserInfoResponseOpenAccountStatusEnum? get openAccountStatus =>
      _$this._openAccountStatus;
  set openAccountStatus(
          WaitVerifyUserInfoResponseOpenAccountStatusEnum? openAccountStatus) =>
      _$this._openAccountStatus = openAccountStatus;

  String? _agentId;
  String? get agentId => _$this._agentId;
  set agentId(String? agentId) => _$this._agentId = agentId;

  String? _agentName;
  String? get agentName => _$this._agentName;
  set agentName(String? agentName) => _$this._agentName = agentName;

  WaitVerifyUserInfoResponseBuilder() {
    WaitVerifyUserInfoResponse._defaults(this);
  }

  WaitVerifyUserInfoResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _openAccountStatus = $v.openAccountStatus;
      _agentId = $v.agentId;
      _agentName = $v.agentName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(WaitVerifyUserInfoResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$WaitVerifyUserInfoResponse;
  }

  @override
  void update(void Function(WaitVerifyUserInfoResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  WaitVerifyUserInfoResponse build() => _build();

  _$WaitVerifyUserInfoResponse _build() {
    final _$result = _$v ??
        new _$WaitVerifyUserInfoResponse._(
            openAccountStatus: openAccountStatus,
            agentId: agentId,
            agentName: agentName);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
