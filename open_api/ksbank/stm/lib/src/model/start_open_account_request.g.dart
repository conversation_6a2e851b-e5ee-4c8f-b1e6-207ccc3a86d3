// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'start_open_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$StartOpenAccountRequest extends StartOpenAccountRequest {
  @override
  final String? atmID;
  @override
  final String? atmLocation;
  @override
  final String? idCardNumber;
  @override
  final String? phoneNumber;
  @override
  final String? email;
  @override
  final String? agentId;

  factory _$StartOpenAccountRequest(
          [void Function(StartOpenAccountRequestBuilder)? updates]) =>
      (new StartOpenAccountRequestBuilder()..update(updates))._build();

  _$StartOpenAccountRequest._(
      {this.atmID,
      this.atmLocation,
      this.idCardNumber,
      this.phoneNumber,
      this.email,
      this.agentId})
      : super._();

  @override
  StartOpenAccountRequest rebuild(
          void Function(StartOpenAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  StartOpenAccountRequestBuilder toBuilder() =>
      new StartOpenAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is StartOpenAccountRequest &&
        atmID == other.atmID &&
        atmLocation == other.atmLocation &&
        idCardNumber == other.idCardNumber &&
        phoneNumber == other.phoneNumber &&
        email == other.email &&
        agentId == other.agentId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, atmID.hashCode);
    _$hash = $jc(_$hash, atmLocation.hashCode);
    _$hash = $jc(_$hash, idCardNumber.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, agentId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'StartOpenAccountRequest')
          ..add('atmID', atmID)
          ..add('atmLocation', atmLocation)
          ..add('idCardNumber', idCardNumber)
          ..add('phoneNumber', phoneNumber)
          ..add('email', email)
          ..add('agentId', agentId))
        .toString();
  }
}

class StartOpenAccountRequestBuilder
    implements
        Builder<StartOpenAccountRequest, StartOpenAccountRequestBuilder> {
  _$StartOpenAccountRequest? _$v;

  String? _atmID;
  String? get atmID => _$this._atmID;
  set atmID(String? atmID) => _$this._atmID = atmID;

  String? _atmLocation;
  String? get atmLocation => _$this._atmLocation;
  set atmLocation(String? atmLocation) => _$this._atmLocation = atmLocation;

  String? _idCardNumber;
  String? get idCardNumber => _$this._idCardNumber;
  set idCardNumber(String? idCardNumber) => _$this._idCardNumber = idCardNumber;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _agentId;
  String? get agentId => _$this._agentId;
  set agentId(String? agentId) => _$this._agentId = agentId;

  StartOpenAccountRequestBuilder() {
    StartOpenAccountRequest._defaults(this);
  }

  StartOpenAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _atmID = $v.atmID;
      _atmLocation = $v.atmLocation;
      _idCardNumber = $v.idCardNumber;
      _phoneNumber = $v.phoneNumber;
      _email = $v.email;
      _agentId = $v.agentId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(StartOpenAccountRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$StartOpenAccountRequest;
  }

  @override
  void update(void Function(StartOpenAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  StartOpenAccountRequest build() => _build();

  _$StartOpenAccountRequest _build() {
    final _$result = _$v ??
        new _$StartOpenAccountRequest._(
            atmID: atmID,
            atmLocation: atmLocation,
            idCardNumber: idCardNumber,
            phoneNumber: phoneNumber,
            email: email,
            agentId: agentId);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
