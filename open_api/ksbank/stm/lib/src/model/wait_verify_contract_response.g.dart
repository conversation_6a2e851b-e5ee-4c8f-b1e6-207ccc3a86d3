// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wait_verify_contract_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_PENDING =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('PENDING');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_OTP =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('VERIFIED_OTP');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_USER_INFO =
    const WaitVerifyContractResponseOpenAccountStatusEnum._(
        'VERIFIED_USER_INFO');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED =
    const WaitVerifyContractResponseOpenAccountStatusEnum._(
        'VERIFY_USER_INFO_FAILED');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_CONTRACT =
    const WaitVerifyContractResponseOpenAccountStatusEnum._(
        'VERIFIED_CONTRACT');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_RESCAN_CONTRACT =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('RESCAN_CONTRACT');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_COMPLETED =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('COMPLETED');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_CANCELED =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('CANCELED');
const WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnum_REPRINT_CONTRACT =
    const WaitVerifyContractResponseOpenAccountStatusEnum._('REPRINT_CONTRACT');

WaitVerifyContractResponseOpenAccountStatusEnum
    _$waitVerifyContractResponseOpenAccountStatusEnumValueOf(String name) {
  switch (name) {
    case 'PENDING':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_PENDING;
    case 'VERIFIED_OTP':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_OTP;
    case 'VERIFIED_USER_INFO':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_USER_INFO;
    case 'VERIFY_USER_INFO_FAILED':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED;
    case 'VERIFIED_CONTRACT':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_CONTRACT;
    case 'RESCAN_CONTRACT':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_RESCAN_CONTRACT;
    case 'COMPLETED':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_COMPLETED;
    case 'CANCELED':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_CANCELED;
    case 'REPRINT_CONTRACT':
      return _$waitVerifyContractResponseOpenAccountStatusEnum_REPRINT_CONTRACT;
    default:
      return _$waitVerifyContractResponseOpenAccountStatusEnum_REPRINT_CONTRACT;
  }
}

final BuiltSet<WaitVerifyContractResponseOpenAccountStatusEnum>
    _$waitVerifyContractResponseOpenAccountStatusEnumValues = new BuiltSet<
        WaitVerifyContractResponseOpenAccountStatusEnum>(const <WaitVerifyContractResponseOpenAccountStatusEnum>[
  _$waitVerifyContractResponseOpenAccountStatusEnum_PENDING,
  _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_OTP,
  _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_USER_INFO,
  _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFY_USER_INFO_FAILED,
  _$waitVerifyContractResponseOpenAccountStatusEnum_VERIFIED_CONTRACT,
  _$waitVerifyContractResponseOpenAccountStatusEnum_RESCAN_CONTRACT,
  _$waitVerifyContractResponseOpenAccountStatusEnum_COMPLETED,
  _$waitVerifyContractResponseOpenAccountStatusEnum_CANCELED,
  _$waitVerifyContractResponseOpenAccountStatusEnum_REPRINT_CONTRACT,
]);

Serializer<WaitVerifyContractResponseOpenAccountStatusEnum>
    _$waitVerifyContractResponseOpenAccountStatusEnumSerializer =
    new _$WaitVerifyContractResponseOpenAccountStatusEnumSerializer();

class _$WaitVerifyContractResponseOpenAccountStatusEnumSerializer
    implements
        PrimitiveSerializer<WaitVerifyContractResponseOpenAccountStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PENDING': 'PENDING',
    'VERIFIED_OTP': 'VERIFIED_OTP',
    'VERIFIED_USER_INFO': 'VERIFIED_USER_INFO',
    'VERIFY_USER_INFO_FAILED': 'VERIFY_USER_INFO_FAILED',
    'VERIFIED_CONTRACT': 'VERIFIED_CONTRACT',
    'RESCAN_CONTRACT': 'RESCAN_CONTRACT',
    'COMPLETED': 'COMPLETED',
    'CANCELED': 'CANCELED',
    'REPRINT_CONTRACT': 'REPRINT_CONTRACT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PENDING': 'PENDING',
    'VERIFIED_OTP': 'VERIFIED_OTP',
    'VERIFIED_USER_INFO': 'VERIFIED_USER_INFO',
    'VERIFY_USER_INFO_FAILED': 'VERIFY_USER_INFO_FAILED',
    'VERIFIED_CONTRACT': 'VERIFIED_CONTRACT',
    'RESCAN_CONTRACT': 'RESCAN_CONTRACT',
    'COMPLETED': 'COMPLETED',
    'CANCELED': 'CANCELED',
    'REPRINT_CONTRACT': 'REPRINT_CONTRACT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    WaitVerifyContractResponseOpenAccountStatusEnum
  ];
  @override
  final String wireName = 'WaitVerifyContractResponseOpenAccountStatusEnum';

  @override
  Object serialize(Serializers serializers,
          WaitVerifyContractResponseOpenAccountStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  WaitVerifyContractResponseOpenAccountStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      WaitVerifyContractResponseOpenAccountStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$WaitVerifyContractResponse extends WaitVerifyContractResponse {
  @override
  final WaitVerifyContractResponseOpenAccountStatusEnum? openAccountStatus;

  factory _$WaitVerifyContractResponse(
          [void Function(WaitVerifyContractResponseBuilder)? updates]) =>
      (new WaitVerifyContractResponseBuilder()..update(updates))._build();

  _$WaitVerifyContractResponse._({this.openAccountStatus}) : super._();

  @override
  WaitVerifyContractResponse rebuild(
          void Function(WaitVerifyContractResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  WaitVerifyContractResponseBuilder toBuilder() =>
      new WaitVerifyContractResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is WaitVerifyContractResponse &&
        openAccountStatus == other.openAccountStatus;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, openAccountStatus.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'WaitVerifyContractResponse')
          ..add('openAccountStatus', openAccountStatus))
        .toString();
  }
}

class WaitVerifyContractResponseBuilder
    implements
        Builder<WaitVerifyContractResponse, WaitVerifyContractResponseBuilder> {
  _$WaitVerifyContractResponse? _$v;

  WaitVerifyContractResponseOpenAccountStatusEnum? _openAccountStatus;
  WaitVerifyContractResponseOpenAccountStatusEnum? get openAccountStatus =>
      _$this._openAccountStatus;
  set openAccountStatus(
          WaitVerifyContractResponseOpenAccountStatusEnum? openAccountStatus) =>
      _$this._openAccountStatus = openAccountStatus;

  WaitVerifyContractResponseBuilder() {
    WaitVerifyContractResponse._defaults(this);
  }

  WaitVerifyContractResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _openAccountStatus = $v.openAccountStatus;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(WaitVerifyContractResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$WaitVerifyContractResponse;
  }

  @override
  void update(void Function(WaitVerifyContractResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  WaitVerifyContractResponse build() => _build();

  _$WaitVerifyContractResponse _build() {
    final _$result = _$v ??
        new _$WaitVerifyContractResponse._(
            openAccountStatus: openAccountStatus);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
