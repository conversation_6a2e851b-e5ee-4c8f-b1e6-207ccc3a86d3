//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'start_open_account_request.g.dart';

/// StartOpenAccountRequest
///
/// Properties:
/// * [atmID] - Mã định danh ATM
/// * [atmLocation] - Địa chỉ ATM
/// * [idCardNumber] - Số CMND/CCCD
/// * [phoneNumber] - Số điện thoại
/// * [email] - Đ<PERSON>a chỉ email
/// * [agentId] - Thông tin tổng đài viên thực hiện hướng dẫn mở thẻ
@BuiltValue()
abstract class StartOpenAccountRequest
    implements Built<StartOpenAccountRequest, StartOpenAccountRequestBuilder> {
  /// Mã định danh ATM
  @BuiltValueField(wireName: r'atmID')
  String? get atmID;

  /// Địa chỉ ATM
  @BuiltValueField(wireName: r'atmLocation')
  String? get atmLocation;

  /// Số CMND/CCCD
  @BuiltValueField(wireName: r'idCardNumber')
  String? get idCardNumber;

  /// Số điện thoại
  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  /// Địa chỉ email
  @BuiltValueField(wireName: r'email')
  String? get email;

  /// Thông tin tổng đài viên thực hiện hướng dẫn mở thẻ
  @BuiltValueField(wireName: r'agentId')
  String? get agentId;

  StartOpenAccountRequest._();

  factory StartOpenAccountRequest(
          [void updates(StartOpenAccountRequestBuilder b)]) =
      _$StartOpenAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(StartOpenAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<StartOpenAccountRequest> get serializer =>
      _$StartOpenAccountRequestSerializer();
}

class _$StartOpenAccountRequestSerializer
    implements PrimitiveSerializer<StartOpenAccountRequest> {
  @override
  final Iterable<Type> types = const [
    StartOpenAccountRequest,
    _$StartOpenAccountRequest
  ];

  @override
  final String wireName = r'StartOpenAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    StartOpenAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.atmID != null) {
      yield r'atmID';
      yield serializers.serialize(
        object.atmID,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.atmLocation != null) {
      yield r'atmLocation';
      yield serializers.serialize(
        object.atmLocation,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardNumber != null) {
      yield r'idCardNumber';
      yield serializers.serialize(
        object.idCardNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.agentId != null) {
      yield r'agentId';
      yield serializers.serialize(
        object.agentId,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    StartOpenAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required StartOpenAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'atmID':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.atmID = valueDes;
          break;
        case r'atmLocation':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.atmLocation = valueDes;
          break;
        case r'idCardNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardNumber = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        case r'agentId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.agentId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  StartOpenAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = StartOpenAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
