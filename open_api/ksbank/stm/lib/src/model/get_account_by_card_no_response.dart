//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_account_by_card_no_response.g.dart';

/// GetAccountByCardNoResponse
///
/// Properties:
/// * [accountNo]
/// * [userCode]
/// * [cardName]
@BuiltValue()
abstract class GetAccountByCardNoResponse
    implements
        Built<GetAccountByCardNoResponse, GetAccountByCardNoResponseBuilder> {
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  @BuiltValueField(wireName: r'userCode')
  String? get userCode;

  @BuiltValueField(wireName: r'cardName')
  String? get cardName;

  GetAccountByCardNoResponse._();

  factory GetAccountByCardNoResponse(
          [void updates(GetAccountByCardNoResponseBuilder b)]) =
      _$GetAccountByCardNoResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetAccountByCardNoResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetAccountByCardNoResponse> get serializer =>
      _$GetAccountByCardNoResponseSerializer();
}

class _$GetAccountByCardNoResponseSerializer
    implements PrimitiveSerializer<GetAccountByCardNoResponse> {
  @override
  final Iterable<Type> types = const [
    GetAccountByCardNoResponse,
    _$GetAccountByCardNoResponse
  ];

  @override
  final String wireName = r'GetAccountByCardNoResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetAccountByCardNoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.userCode != null) {
      yield r'userCode';
      yield serializers.serialize(
        object.userCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardName != null) {
      yield r'cardName';
      yield serializers.serialize(
        object.cardName,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetAccountByCardNoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetAccountByCardNoResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'userCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.userCode = valueDes;
          break;
        case r'cardName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetAccountByCardNoResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetAccountByCardNoResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
