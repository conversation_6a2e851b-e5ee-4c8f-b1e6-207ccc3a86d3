//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_stm/src/model/date.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'create_account_api_request.g.dart';

/// CreateAccountApiRequest
///
/// Properties:
/// * [idCardNumber]
/// * [phoneNumber]
/// * [email]
/// * [name]
/// * [dob]
/// * [gender]
/// * [nationality]
/// * [hometown]
/// * [city]
/// * [state]
/// * [idCardIssuePlc]
/// * [idCardIssueDt]
/// * [permanentAddress]
/// * [contactAddress]
/// * [idCardImageUp]
/// * [idCardImageDown]
/// * [openAccountFormUp]
/// * [openAccountFormDown]
/// * [contractFormUp]
/// * [contractFormDown]
/// * [servicePack]
/// * [cardNumber]
/// * [issueDate]
/// * [expirationDate]
/// * [track1]
/// * [branchCode]
/// * [isOpenCard]
/// * [openCard]
@BuiltValue()
abstract class CreateAccountApiRequest
    implements Built<CreateAccountApiRequest, CreateAccountApiRequestBuilder> {
  @BuiltValueField(wireName: r'idCardNumber')
  String? get idCardNumber;

  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  @BuiltValueField(wireName: r'email')
  String? get email;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'dob')
  Date? get dob;

  @BuiltValueField(wireName: r'gender')
  CreateAccountApiRequestGenderEnum? get gender;
  // enum genderEnum {  M,  F,  };

  @BuiltValueField(wireName: r'nationality')
  CreateAccountApiRequestNationalityEnum? get nationality;
  // enum nationalityEnum {  VN,  };

  @BuiltValueField(wireName: r'hometown')
  String? get hometown;

  @BuiltValueField(wireName: r'city')
  String? get city;

  @BuiltValueField(wireName: r'state')
  String? get state;

  @BuiltValueField(wireName: r'idCardIssuePlc')
  String? get idCardIssuePlc;

  @BuiltValueField(wireName: r'idCardIssueDt')
  Date? get idCardIssueDt;

  @BuiltValueField(wireName: r'permanentAddress')
  String? get permanentAddress;

  @BuiltValueField(wireName: r'contactAddress')
  String? get contactAddress;

  @BuiltValueField(wireName: r'idCardImageUp')
  String? get idCardImageUp;

  @BuiltValueField(wireName: r'idCardImageDown')
  String? get idCardImageDown;

  @BuiltValueField(wireName: r'openAccountFormUp')
  String? get openAccountFormUp;

  @BuiltValueField(wireName: r'openAccountFormDown')
  String? get openAccountFormDown;

  @BuiltValueField(wireName: r'contractFormUp')
  String? get contractFormUp;

  @BuiltValueField(wireName: r'contractFormDown')
  String? get contractFormDown;

  @BuiltValueField(wireName: r'servicePack')
  String? get servicePack;

  @BuiltValueField(wireName: r'cardNumber')
  String? get cardNumber;

  @BuiltValueField(wireName: r'issueDate')
  Date? get issueDate;

  @BuiltValueField(wireName: r'expirationDate')
  Date? get expirationDate;

  @BuiltValueField(wireName: r'track1')
  String? get track1;

  @BuiltValueField(wireName: r'branchCode')
  String? get branchCode;

  @BuiltValueField(wireName: r'isOpenCard')
  bool? get isOpenCard;

  @BuiltValueField(wireName: r'openCard')
  bool? get openCard;

  CreateAccountApiRequest._();

  factory CreateAccountApiRequest(
          [void updates(CreateAccountApiRequestBuilder b)]) =
      _$CreateAccountApiRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CreateAccountApiRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CreateAccountApiRequest> get serializer =>
      _$CreateAccountApiRequestSerializer();
}

class _$CreateAccountApiRequestSerializer
    implements PrimitiveSerializer<CreateAccountApiRequest> {
  @override
  final Iterable<Type> types = const [
    CreateAccountApiRequest,
    _$CreateAccountApiRequest
  ];

  @override
  final String wireName = r'CreateAccountApiRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CreateAccountApiRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.idCardNumber != null) {
      yield r'idCardNumber';
      yield serializers.serialize(
        object.idCardNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.dob != null) {
      yield r'dob';
      yield serializers.serialize(
        object.dob,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.gender != null) {
      yield r'gender';
      yield serializers.serialize(
        object.gender,
        specifiedType:
            const FullType.nullable(CreateAccountApiRequestGenderEnum),
      );
    }
    if (object.nationality != null) {
      yield r'nationality';
      yield serializers.serialize(
        object.nationality,
        specifiedType:
            const FullType.nullable(CreateAccountApiRequestNationalityEnum),
      );
    }
    if (object.hometown != null) {
      yield r'hometown';
      yield serializers.serialize(
        object.hometown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.city != null) {
      yield r'city';
      yield serializers.serialize(
        object.city,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.state != null) {
      yield r'state';
      yield serializers.serialize(
        object.state,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardIssuePlc != null) {
      yield r'idCardIssuePlc';
      yield serializers.serialize(
        object.idCardIssuePlc,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardIssueDt != null) {
      yield r'idCardIssueDt';
      yield serializers.serialize(
        object.idCardIssueDt,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.permanentAddress != null) {
      yield r'permanentAddress';
      yield serializers.serialize(
        object.permanentAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contactAddress != null) {
      yield r'contactAddress';
      yield serializers.serialize(
        object.contactAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardImageUp != null) {
      yield r'idCardImageUp';
      yield serializers.serialize(
        object.idCardImageUp,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardImageDown != null) {
      yield r'idCardImageDown';
      yield serializers.serialize(
        object.idCardImageDown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.openAccountFormUp != null) {
      yield r'openAccountFormUp';
      yield serializers.serialize(
        object.openAccountFormUp,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.openAccountFormDown != null) {
      yield r'openAccountFormDown';
      yield serializers.serialize(
        object.openAccountFormDown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contractFormUp != null) {
      yield r'contractFormUp';
      yield serializers.serialize(
        object.contractFormUp,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contractFormDown != null) {
      yield r'contractFormDown';
      yield serializers.serialize(
        object.contractFormDown,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.servicePack != null) {
      yield r'servicePack';
      yield serializers.serialize(
        object.servicePack,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardNumber != null) {
      yield r'cardNumber';
      yield serializers.serialize(
        object.cardNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.issueDate != null) {
      yield r'issueDate';
      yield serializers.serialize(
        object.issueDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.expirationDate != null) {
      yield r'expirationDate';
      yield serializers.serialize(
        object.expirationDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.track1 != null) {
      yield r'track1';
      yield serializers.serialize(
        object.track1,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.branchCode != null) {
      yield r'branchCode';
      yield serializers.serialize(
        object.branchCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.isOpenCard != null) {
      yield r'isOpenCard';
      yield serializers.serialize(
        object.isOpenCard,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.openCard != null) {
      yield r'openCard';
      yield serializers.serialize(
        object.openCard,
        specifiedType: const FullType.nullable(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CreateAccountApiRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CreateAccountApiRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'idCardNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardNumber = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'dob':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.dob = valueDes;
          break;
        case r'gender':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CreateAccountApiRequestGenderEnum),
          ) as CreateAccountApiRequestGenderEnum?;
          if (valueDes == null) continue;
          result.gender = valueDes;
          break;
        case r'nationality':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CreateAccountApiRequestNationalityEnum),
          ) as CreateAccountApiRequestNationalityEnum?;
          if (valueDes == null) continue;
          result.nationality = valueDes;
          break;
        case r'hometown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.hometown = valueDes;
          break;
        case r'city':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.city = valueDes;
          break;
        case r'state':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.state = valueDes;
          break;
        case r'idCardIssuePlc':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardIssuePlc = valueDes;
          break;
        case r'idCardIssueDt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.idCardIssueDt = valueDes;
          break;
        case r'permanentAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.permanentAddress = valueDes;
          break;
        case r'contactAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contactAddress = valueDes;
          break;
        case r'idCardImageUp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardImageUp = valueDes;
          break;
        case r'idCardImageDown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardImageDown = valueDes;
          break;
        case r'openAccountFormUp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.openAccountFormUp = valueDes;
          break;
        case r'openAccountFormDown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.openAccountFormDown = valueDes;
          break;
        case r'contractFormUp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contractFormUp = valueDes;
          break;
        case r'contractFormDown':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.contractFormDown = valueDes;
          break;
        case r'servicePack':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.servicePack = valueDes;
          break;
        case r'cardNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardNumber = valueDes;
          break;
        case r'issueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.issueDate = valueDes;
          break;
        case r'expirationDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.expirationDate = valueDes;
          break;
        case r'track1':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.track1 = valueDes;
          break;
        case r'branchCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.branchCode = valueDes;
          break;
        case r'isOpenCard':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isOpenCard = valueDes;
          break;
        case r'openCard':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.openCard = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CreateAccountApiRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CreateAccountApiRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CreateAccountApiRequestGenderEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'M')
  static const CreateAccountApiRequestGenderEnum M =
      _$createAccountApiRequestGenderEnum_M;
  @BuiltValueEnumConst(wireName: r'F', fallback: true)
  static const CreateAccountApiRequestGenderEnum F =
      _$createAccountApiRequestGenderEnum_F;

  static Serializer<CreateAccountApiRequestGenderEnum> get serializer =>
      _$createAccountApiRequestGenderEnumSerializer;

  const CreateAccountApiRequestGenderEnum._(String name) : super(name);

  static BuiltSet<CreateAccountApiRequestGenderEnum> get values =>
      _$createAccountApiRequestGenderEnumValues;
  static CreateAccountApiRequestGenderEnum valueOf(String name) =>
      _$createAccountApiRequestGenderEnumValueOf(name);
}

class CreateAccountApiRequestNationalityEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'VN', fallback: true)
  static const CreateAccountApiRequestNationalityEnum VN =
      _$createAccountApiRequestNationalityEnum_VN;

  static Serializer<CreateAccountApiRequestNationalityEnum> get serializer =>
      _$createAccountApiRequestNationalityEnumSerializer;

  const CreateAccountApiRequestNationalityEnum._(String name) : super(name);

  static BuiltSet<CreateAccountApiRequestNationalityEnum> get values =>
      _$createAccountApiRequestNationalityEnumValues;
  static CreateAccountApiRequestNationalityEnum valueOf(String name) =>
      _$createAccountApiRequestNationalityEnumValueOf(name);
}
