// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'submit_info_api_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const SubmitInfoApiRequestGenderEnum _$submitInfoApiRequestGenderEnum_M =
    const SubmitInfoApiRequestGenderEnum._('M');
const SubmitInfoApiRequestGenderEnum _$submitInfoApiRequestGenderEnum_F =
    const SubmitInfoApiRequestGenderEnum._('F');

SubmitInfoApiRequestGenderEnum _$submitInfoApiRequestGenderEnumValueOf(
    String name) {
  switch (name) {
    case 'M':
      return _$submitInfoApiRequestGenderEnum_M;
    case 'F':
      return _$submitInfoApiRequestGenderEnum_F;
    default:
      return _$submitInfoApiRequestGenderEnum_F;
  }
}

final BuiltSet<SubmitInfoApiRequestGenderEnum>
    _$submitInfoApiRequestGenderEnumValues = new BuiltSet<
        SubmitInfoApiRequestGenderEnum>(const <SubmitInfoApiRequestGenderEnum>[
  _$submitInfoApiRequestGenderEnum_M,
  _$submitInfoApiRequestGenderEnum_F,
]);

const SubmitInfoApiRequestNationalityEnum
    _$submitInfoApiRequestNationalityEnum_VN =
    const SubmitInfoApiRequestNationalityEnum._('VN');

SubmitInfoApiRequestNationalityEnum
    _$submitInfoApiRequestNationalityEnumValueOf(String name) {
  switch (name) {
    case 'VN':
      return _$submitInfoApiRequestNationalityEnum_VN;
    default:
      return _$submitInfoApiRequestNationalityEnum_VN;
  }
}

final BuiltSet<SubmitInfoApiRequestNationalityEnum>
    _$submitInfoApiRequestNationalityEnumValues = new BuiltSet<
        SubmitInfoApiRequestNationalityEnum>(const <SubmitInfoApiRequestNationalityEnum>[
  _$submitInfoApiRequestNationalityEnum_VN,
]);

Serializer<SubmitInfoApiRequestGenderEnum>
    _$submitInfoApiRequestGenderEnumSerializer =
    new _$SubmitInfoApiRequestGenderEnumSerializer();
Serializer<SubmitInfoApiRequestNationalityEnum>
    _$submitInfoApiRequestNationalityEnumSerializer =
    new _$SubmitInfoApiRequestNationalityEnumSerializer();

class _$SubmitInfoApiRequestGenderEnumSerializer
    implements PrimitiveSerializer<SubmitInfoApiRequestGenderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'M': 'M',
    'F': 'F',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'M': 'M',
    'F': 'F',
  };

  @override
  final Iterable<Type> types = const <Type>[SubmitInfoApiRequestGenderEnum];
  @override
  final String wireName = 'SubmitInfoApiRequestGenderEnum';

  @override
  Object serialize(
          Serializers serializers, SubmitInfoApiRequestGenderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  SubmitInfoApiRequestGenderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      SubmitInfoApiRequestGenderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$SubmitInfoApiRequestNationalityEnumSerializer
    implements PrimitiveSerializer<SubmitInfoApiRequestNationalityEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VN': 'VN',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VN': 'VN',
  };

  @override
  final Iterable<Type> types = const <Type>[
    SubmitInfoApiRequestNationalityEnum
  ];
  @override
  final String wireName = 'SubmitInfoApiRequestNationalityEnum';

  @override
  Object serialize(
          Serializers serializers, SubmitInfoApiRequestNationalityEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  SubmitInfoApiRequestNationalityEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      SubmitInfoApiRequestNationalityEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$SubmitInfoApiRequest extends SubmitInfoApiRequest {
  @override
  final String? idCardIssuePlc;
  @override
  final SubmitInfoApiRequestGenderEnum? gender;
  @override
  final String? idCardImageDown;
  @override
  final String? idCardImageUp;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? contactAddress;
  @override
  final String? permanentAddress;
  @override
  final Date? issueDate;
  @override
  final String? email;
  @override
  final String? hometown;
  @override
  final String? phoneNumber;
  @override
  final SubmitInfoApiRequestNationalityEnum? nationality;
  @override
  final Date? dob;
  @override
  final String? idCardNumber;
  @override
  final String? name;

  factory _$SubmitInfoApiRequest(
          [void Function(SubmitInfoApiRequestBuilder)? updates]) =>
      (new SubmitInfoApiRequestBuilder()..update(updates))._build();

  _$SubmitInfoApiRequest._(
      {this.idCardIssuePlc,
      this.gender,
      this.idCardImageDown,
      this.idCardImageUp,
      this.city,
      this.state,
      this.contactAddress,
      this.permanentAddress,
      this.issueDate,
      this.email,
      this.hometown,
      this.phoneNumber,
      this.nationality,
      this.dob,
      this.idCardNumber,
      this.name})
      : super._();

  @override
  SubmitInfoApiRequest rebuild(
          void Function(SubmitInfoApiRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SubmitInfoApiRequestBuilder toBuilder() =>
      new SubmitInfoApiRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SubmitInfoApiRequest &&
        idCardIssuePlc == other.idCardIssuePlc &&
        gender == other.gender &&
        idCardImageDown == other.idCardImageDown &&
        idCardImageUp == other.idCardImageUp &&
        city == other.city &&
        state == other.state &&
        contactAddress == other.contactAddress &&
        permanentAddress == other.permanentAddress &&
        issueDate == other.issueDate &&
        email == other.email &&
        hometown == other.hometown &&
        phoneNumber == other.phoneNumber &&
        nationality == other.nationality &&
        dob == other.dob &&
        idCardNumber == other.idCardNumber &&
        name == other.name;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, idCardIssuePlc.hashCode);
    _$hash = $jc(_$hash, gender.hashCode);
    _$hash = $jc(_$hash, idCardImageDown.hashCode);
    _$hash = $jc(_$hash, idCardImageUp.hashCode);
    _$hash = $jc(_$hash, city.hashCode);
    _$hash = $jc(_$hash, state.hashCode);
    _$hash = $jc(_$hash, contactAddress.hashCode);
    _$hash = $jc(_$hash, permanentAddress.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, hometown.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, nationality.hashCode);
    _$hash = $jc(_$hash, dob.hashCode);
    _$hash = $jc(_$hash, idCardNumber.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SubmitInfoApiRequest')
          ..add('idCardIssuePlc', idCardIssuePlc)
          ..add('gender', gender)
          ..add('idCardImageDown', idCardImageDown)
          ..add('idCardImageUp', idCardImageUp)
          ..add('city', city)
          ..add('state', state)
          ..add('contactAddress', contactAddress)
          ..add('permanentAddress', permanentAddress)
          ..add('issueDate', issueDate)
          ..add('email', email)
          ..add('hometown', hometown)
          ..add('phoneNumber', phoneNumber)
          ..add('nationality', nationality)
          ..add('dob', dob)
          ..add('idCardNumber', idCardNumber)
          ..add('name', name))
        .toString();
  }
}

class SubmitInfoApiRequestBuilder
    implements Builder<SubmitInfoApiRequest, SubmitInfoApiRequestBuilder> {
  _$SubmitInfoApiRequest? _$v;

  String? _idCardIssuePlc;
  String? get idCardIssuePlc => _$this._idCardIssuePlc;
  set idCardIssuePlc(String? idCardIssuePlc) =>
      _$this._idCardIssuePlc = idCardIssuePlc;

  SubmitInfoApiRequestGenderEnum? _gender;
  SubmitInfoApiRequestGenderEnum? get gender => _$this._gender;
  set gender(SubmitInfoApiRequestGenderEnum? gender) => _$this._gender = gender;

  String? _idCardImageDown;
  String? get idCardImageDown => _$this._idCardImageDown;
  set idCardImageDown(String? idCardImageDown) =>
      _$this._idCardImageDown = idCardImageDown;

  String? _idCardImageUp;
  String? get idCardImageUp => _$this._idCardImageUp;
  set idCardImageUp(String? idCardImageUp) =>
      _$this._idCardImageUp = idCardImageUp;

  String? _city;
  String? get city => _$this._city;
  set city(String? city) => _$this._city = city;

  String? _state;
  String? get state => _$this._state;
  set state(String? state) => _$this._state = state;

  String? _contactAddress;
  String? get contactAddress => _$this._contactAddress;
  set contactAddress(String? contactAddress) =>
      _$this._contactAddress = contactAddress;

  String? _permanentAddress;
  String? get permanentAddress => _$this._permanentAddress;
  set permanentAddress(String? permanentAddress) =>
      _$this._permanentAddress = permanentAddress;

  Date? _issueDate;
  Date? get issueDate => _$this._issueDate;
  set issueDate(Date? issueDate) => _$this._issueDate = issueDate;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _hometown;
  String? get hometown => _$this._hometown;
  set hometown(String? hometown) => _$this._hometown = hometown;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  SubmitInfoApiRequestNationalityEnum? _nationality;
  SubmitInfoApiRequestNationalityEnum? get nationality => _$this._nationality;
  set nationality(SubmitInfoApiRequestNationalityEnum? nationality) =>
      _$this._nationality = nationality;

  Date? _dob;
  Date? get dob => _$this._dob;
  set dob(Date? dob) => _$this._dob = dob;

  String? _idCardNumber;
  String? get idCardNumber => _$this._idCardNumber;
  set idCardNumber(String? idCardNumber) => _$this._idCardNumber = idCardNumber;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  SubmitInfoApiRequestBuilder() {
    SubmitInfoApiRequest._defaults(this);
  }

  SubmitInfoApiRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _idCardIssuePlc = $v.idCardIssuePlc;
      _gender = $v.gender;
      _idCardImageDown = $v.idCardImageDown;
      _idCardImageUp = $v.idCardImageUp;
      _city = $v.city;
      _state = $v.state;
      _contactAddress = $v.contactAddress;
      _permanentAddress = $v.permanentAddress;
      _issueDate = $v.issueDate;
      _email = $v.email;
      _hometown = $v.hometown;
      _phoneNumber = $v.phoneNumber;
      _nationality = $v.nationality;
      _dob = $v.dob;
      _idCardNumber = $v.idCardNumber;
      _name = $v.name;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SubmitInfoApiRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$SubmitInfoApiRequest;
  }

  @override
  void update(void Function(SubmitInfoApiRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SubmitInfoApiRequest build() => _build();

  _$SubmitInfoApiRequest _build() {
    final _$result = _$v ??
        new _$SubmitInfoApiRequest._(
            idCardIssuePlc: idCardIssuePlc,
            gender: gender,
            idCardImageDown: idCardImageDown,
            idCardImageUp: idCardImageUp,
            city: city,
            state: state,
            contactAddress: contactAddress,
            permanentAddress: permanentAddress,
            issueDate: issueDate,
            email: email,
            hometown: hometown,
            phoneNumber: phoneNumber,
            nationality: nationality,
            dob: dob,
            idCardNumber: idCardNumber,
            name: name);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
