//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:ksbank_api_stm/src/date_serializer.dart';
import 'package:ksbank_api_stm/src/model/date.dart';

import 'package:ksbank_api_stm/src/model/alarm_operation_request.dart';
import 'package:ksbank_api_stm/src/model/alarm_operation_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_alarm_operation_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_cancel_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_cancel_open_card_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_cancel_qr_payment_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_check_card_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_check_card_type_by_card_no_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_confirm_qr_transaction_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_confirm_qr_transaction_response_v2.dart';
import 'package:ksbank_api_stm/src/model/base_response_confirm_take_card_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_create_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_execute_qr_transfer_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_execute_qr_transfer_response_v2.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_account_by_card_no_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_account_limit_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_bill_fee_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_card_product_supported_open_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_open_card_detail_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_qr_payment_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_get_qr_code_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_mapping_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_open_card_confirm_take_card_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_open_card_qr_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_open_card_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_send_otp_to_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_set_amount_command_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_start_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_stm_deposit_cash_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_submit_avatar_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_submit_card_info_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_submit_contract_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_submit_info_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_verify_otp_to_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_capture_action_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_scan_qr_done_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_scan_qr_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_select_account_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_verify_contract_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_verify_photo_status_response.dart';
import 'package:ksbank_api_stm/src/model/base_response_wait_verify_user_info_response.dart';
import 'package:ksbank_api_stm/src/model/cancel_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/cancel_open_card_response.dart';
import 'package:ksbank_api_stm/src/model/cancel_qr_payment_response.dart';
import 'package:ksbank_api_stm/src/model/card_product_supported_open.dart';
import 'package:ksbank_api_stm/src/model/check_card_response.dart';
import 'package:ksbank_api_stm/src/model/check_card_type_by_card_no_request.dart';
import 'package:ksbank_api_stm/src/model/check_card_type_by_card_no_response.dart';
import 'package:ksbank_api_stm/src/model/confirm_open_card_api_request.dart';
import 'package:ksbank_api_stm/src/model/confirm_qr_payment_api_request.dart';
import 'package:ksbank_api_stm/src/model/confirm_qr_transaction_response.dart';
import 'package:ksbank_api_stm/src/model/confirm_qr_transaction_response_v2.dart';
import 'package:ksbank_api_stm/src/model/confirm_take_card_api_request.dart';
import 'package:ksbank_api_stm/src/model/confirm_take_card_response.dart';
import 'package:ksbank_api_stm/src/model/create_account_api_request.dart';
import 'package:ksbank_api_stm/src/model/create_account_response.dart';
import 'package:ksbank_api_stm/src/model/execute_open_card_api_request.dart';
import 'package:ksbank_api_stm/src/model/execute_qr_transfer_response.dart';
import 'package:ksbank_api_stm/src/model/execute_qr_transfer_response_v2.dart';
import 'package:ksbank_api_stm/src/model/file_upload_response.dart';
import 'package:ksbank_api_stm/src/model/get_account_by_card_no_request.dart';
import 'package:ksbank_api_stm/src/model/get_account_by_card_no_response.dart';
import 'package:ksbank_api_stm/src/model/get_account_limit_response.dart';
import 'package:ksbank_api_stm/src/model/get_bill_fee_response.dart';
import 'package:ksbank_api_stm/src/model/get_card_product_supported_open_response.dart';
import 'package:ksbank_api_stm/src/model/get_open_card_detail_response.dart';
import 'package:ksbank_api_stm/src/model/get_qr_code_request.dart';
import 'package:ksbank_api_stm/src/model/get_qr_payment_response.dart';
import 'package:ksbank_api_stm/src/model/get_qr_code_response.dart';
import 'package:ksbank_api_stm/src/model/map_template_to_stm_request.dart';
import 'package:ksbank_api_stm/src/model/mapping_account_api_request.dart';
import 'package:ksbank_api_stm/src/model/mapping_account_response.dart';
import 'package:ksbank_api_stm/src/model/null_type.dart';
import 'package:ksbank_api_stm/src/model/open_card_confirm_take_card_response.dart';
import 'package:ksbank_api_stm/src/model/open_card_qr_response.dart';
import 'package:ksbank_api_stm/src/model/open_card_qr_request.dart';
import 'package:ksbank_api_stm/src/model/open_card_response.dart';
import 'package:ksbank_api_stm/src/model/send_otp_to_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/set_amount_command_response.dart';
import 'package:ksbank_api_stm/src/model/start_open_account_request.dart';
import 'package:ksbank_api_stm/src/model/start_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/stm_deposit_cash_request.dart';
import 'package:ksbank_api_stm/src/model/stm_deposit_cash_response.dart';
import 'package:ksbank_api_stm/src/model/submit_avatar_api_request.dart';
import 'package:ksbank_api_stm/src/model/submit_avatar_response.dart';
import 'package:ksbank_api_stm/src/model/submit_card_info_api_request.dart';
import 'package:ksbank_api_stm/src/model/submit_card_info_response.dart';
import 'package:ksbank_api_stm/src/model/submit_contract_api_request.dart';
import 'package:ksbank_api_stm/src/model/submit_contract_response.dart';
import 'package:ksbank_api_stm/src/model/submit_info_api_request.dart';
import 'package:ksbank_api_stm/src/model/submit_info_response.dart';
import 'package:ksbank_api_stm/src/model/token_response.dart';
import 'package:ksbank_api_stm/src/model/verify_otp_api_request.dart';
import 'package:ksbank_api_stm/src/model/verify_otp_to_open_account_response.dart';
import 'package:ksbank_api_stm/src/model/wait_capture_action_response.dart';
import 'package:ksbank_api_stm/src/model/wait_scan_qr_done_response.dart';
import 'package:ksbank_api_stm/src/model/wait_scan_qr_response.dart';
import 'package:ksbank_api_stm/src/model/wait_select_account_response.dart';
import 'package:ksbank_api_stm/src/model/wait_verify_contract_response.dart';
import 'package:ksbank_api_stm/src/model/wait_verify_photo_status_response.dart';
import 'package:ksbank_api_stm/src/model/wait_verify_user_info_response.dart';

part 'serializers.g.dart';

@SerializersFor([
  AlarmOperationRequest,
  AlarmOperationResponse,
  BaseResponseAlarmOperationResponse,
  BaseResponseCancelOpenAccountResponse,
  BaseResponseCancelOpenCardResponse,
  BaseResponseCancelQRPaymentResponse,
  BaseResponseCheckCardResponse,
  BaseResponseCheckCardTypeByCardNoResponse,
  BaseResponseConfirmQRTransactionResponse,
  BaseResponseConfirmQRTransactionResponseV2,
  BaseResponseConfirmTakeCardResponse,
  BaseResponseCreateAccountResponse,
  BaseResponseExecuteQRTransferResponse,
  BaseResponseExecuteQRTransferResponseV2,
  BaseResponseGetAccountByCardNoResponse,
  BaseResponseGetAccountLimitResponse,
  BaseResponseGetBillFeeResponse,
  BaseResponseGetCardProductSupportedOpenResponse,
  BaseResponseGetOpenCardDetailResponse,
  BaseResponseGetQRPaymentResponse,
  BaseResponseGetQrCodeResponse,
  BaseResponseMappingAccountResponse,
  BaseResponseOpenCardConfirmTakeCardResponse,
  BaseResponseOpenCardQRResponse,
  BaseResponseOpenCardResponse,
  BaseResponseSendOtpToOpenAccountResponse,
  BaseResponseSetAmountCommandResponse,
  BaseResponseStartOpenAccountResponse,
  BaseResponseStmDepositCashResponse,
  BaseResponseSubmitAvatarResponse,
  BaseResponseSubmitCardInfoResponse,
  BaseResponseSubmitContractResponse,
  BaseResponseSubmitInfoResponse,
  BaseResponseVerifyOtpToOpenAccountResponse,
  BaseResponseWaitCaptureActionResponse,
  BaseResponseWaitScanQRDoneResponse,
  BaseResponseWaitScanQrResponse,
  BaseResponseWaitSelectAccountResponse,
  BaseResponseWaitVerifyContractResponse,
  BaseResponseWaitVerifyPhotoStatusResponse,
  BaseResponseWaitVerifyUserInfoResponse,
  CancelOpenAccountResponse,
  CancelOpenCardResponse,
  CancelQRPaymentResponse,
  CardProductSupportedOpen,
  CheckCardResponse,
  CheckCardTypeByCardNoRequest,
  CheckCardTypeByCardNoResponse,
  ConfirmOpenCardApiRequest,
  ConfirmQRPaymentApiRequest,
  ConfirmQRTransactionResponse,
  ConfirmQRTransactionResponseV2,
  ConfirmTakeCardApiRequest,
  ConfirmTakeCardResponse,
  CreateAccountApiRequest,
  CreateAccountResponse,
  ExecuteOpenCardApiRequest,
  ExecuteQRTransferResponse,
  ExecuteQRTransferResponseV2,
  FileUploadResponse,
  GetAccountByCardNoRequest,
  GetAccountByCardNoResponse,
  GetAccountLimitResponse,
  GetBillFeeResponse,
  GetCardProductSupportedOpenResponse,
  GetOpenCardDetailResponse,
  GetQRCodeRequest,
  GetQRPaymentResponse,
  GetQrCodeResponse,
  MapTemplateToSTMRequest,
  MappingAccountApiRequest,
  MappingAccountResponse,
  NullType,
  OpenCardConfirmTakeCardResponse,
  OpenCardQRResponse,
  OpenCardQrRequest,
  OpenCardResponse,
  SendOtpToOpenAccountResponse,
  SetAmountCommandResponse,
  StartOpenAccountRequest,
  StartOpenAccountResponse,
  StmDepositCashRequest,
  StmDepositCashResponse,
  SubmitAvatarApiRequest,
  SubmitAvatarResponse,
  SubmitCardInfoApiRequest,
  SubmitCardInfoResponse,
  SubmitContractApiRequest,
  SubmitContractResponse,
  SubmitInfoApiRequest,
  SubmitInfoResponse,
  TokenResponse,
  VerifyOtpApiRequest,
  VerifyOtpToOpenAccountResponse,
  WaitCaptureActionResponse,
  WaitScanQRDoneResponse,
  WaitScanQrResponse,
  WaitSelectAccountResponse,
  WaitVerifyContractResponse,
  WaitVerifyPhotoStatusResponse,
  WaitVerifyUserInfoResponse,
])
Serializers serializers = (_$serializers.toBuilder()
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer()))
    .build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
