// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (new Serializers().toBuilder()
      ..add(AlarmOperationRequest.serializer)
      ..add(AlarmOperationResponse.serializer)
      ..add(BaseResponseAlarmOperationResponse.serializer)
      ..add(BaseResponseCancelOpenAccountResponse.serializer)
      ..add(BaseResponseCancelOpenCardResponse.serializer)
      ..add(BaseResponseCancelQRPaymentResponse.serializer)
      ..add(BaseResponseCheckCardResponse.serializer)
      ..add(BaseResponseCheckCardTypeByCardNoResponse.serializer)
      ..add(BaseResponseConfirmQRTransactionResponse.serializer)
      ..add(BaseResponseConfirmQRTransactionResponseV2.serializer)
      ..add(BaseResponseConfirmTakeCardResponse.serializer)
      ..add(BaseResponseCreateAccountResponse.serializer)
      ..add(BaseResponseExecuteQRTransferResponse.serializer)
      ..add(BaseResponseExecuteQRTransferResponseV2.serializer)
      ..add(BaseResponseGetAccountByCardNoResponse.serializer)
      ..add(BaseResponseGetAccountLimitResponse.serializer)
      ..add(BaseResponseGetBillFeeResponse.serializer)
      ..add(BaseResponseGetCardProductSupportedOpenResponse.serializer)
      ..add(BaseResponseGetOpenCardDetailResponse.serializer)
      ..add(BaseResponseGetQRPaymentResponse.serializer)
      ..add(BaseResponseGetQrCodeResponse.serializer)
      ..add(BaseResponseMappingAccountResponse.serializer)
      ..add(BaseResponseOpenCardConfirmTakeCardResponse.serializer)
      ..add(BaseResponseOpenCardQRResponse.serializer)
      ..add(BaseResponseOpenCardResponse.serializer)
      ..add(BaseResponseSendOtpToOpenAccountResponse.serializer)
      ..add(BaseResponseSetAmountCommandResponse.serializer)
      ..add(BaseResponseStartOpenAccountResponse.serializer)
      ..add(BaseResponseStmDepositCashResponse.serializer)
      ..add(BaseResponseSubmitAvatarResponse.serializer)
      ..add(BaseResponseSubmitCardInfoResponse.serializer)
      ..add(BaseResponseSubmitContractResponse.serializer)
      ..add(BaseResponseSubmitInfoResponse.serializer)
      ..add(BaseResponseVerifyOtpToOpenAccountResponse.serializer)
      ..add(BaseResponseWaitCaptureActionResponse.serializer)
      ..add(BaseResponseWaitScanQRDoneResponse.serializer)
      ..add(BaseResponseWaitScanQrResponse.serializer)
      ..add(BaseResponseWaitSelectAccountResponse.serializer)
      ..add(BaseResponseWaitVerifyContractResponse.serializer)
      ..add(BaseResponseWaitVerifyPhotoStatusResponse.serializer)
      ..add(BaseResponseWaitVerifyUserInfoResponse.serializer)
      ..add(CancelOpenAccountResponse.serializer)
      ..add(CancelOpenCardResponse.serializer)
      ..add(CancelQRPaymentResponse.serializer)
      ..add(CancelQRPaymentResponseQrPaymentStatusEnum.serializer)
      ..add(CardProductSupportedOpen.serializer)
      ..add(CheckCardResponse.serializer)
      ..add(CheckCardTypeByCardNoRequest.serializer)
      ..add(CheckCardTypeByCardNoResponse.serializer)
      ..add(ConfirmOpenCardApiRequest.serializer)
      ..add(ConfirmOpenCardApiRequestConfirmTakeCardStatusEnum.serializer)
      ..add(ConfirmQRPaymentApiRequest.serializer)
      ..add(ConfirmQRPaymentApiRequestConfirmQRStatusEnum.serializer)
      ..add(ConfirmQRTransactionResponse.serializer)
      ..add(ConfirmQRTransactionResponseQrPaymentStatusEnum.serializer)
      ..add(ConfirmQRTransactionResponseV2.serializer)
      ..add(ConfirmQRTransactionResponseV2QrPaymentStatusEnum.serializer)
      ..add(ConfirmTakeCardApiRequest.serializer)
      ..add(ConfirmTakeCardApiRequestConfirmTakeCardStatusEnum.serializer)
      ..add(ConfirmTakeCardResponse.serializer)
      ..add(CreateAccountApiRequest.serializer)
      ..add(CreateAccountApiRequestGenderEnum.serializer)
      ..add(CreateAccountApiRequestNationalityEnum.serializer)
      ..add(CreateAccountResponse.serializer)
      ..add(ExecuteOpenCardApiRequest.serializer)
      ..add(ExecuteQRTransferResponse.serializer)
      ..add(ExecuteQRTransferResponseV2.serializer)
      ..add(FileUploadResponse.serializer)
      ..add(GetAccountByCardNoRequest.serializer)
      ..add(GetAccountByCardNoResponse.serializer)
      ..add(GetAccountLimitResponse.serializer)
      ..add(GetBillFeeResponse.serializer)
      ..add(GetCardProductSupportedOpenResponse.serializer)
      ..add(GetOpenCardDetailResponse.serializer)
      ..add(GetQRCodeRequest.serializer)
      ..add(GetQRPaymentResponse.serializer)
      ..add(GetQRPaymentResponseQrPaymentStatusEnum.serializer)
      ..add(GetQrCodeResponse.serializer)
      ..add(MapTemplateToSTMRequest.serializer)
      ..add(MappingAccountApiRequest.serializer)
      ..add(MappingAccountResponse.serializer)
      ..add(NullType.serializer)
      ..add(OpenCardConfirmTakeCardResponse.serializer)
      ..add(OpenCardQRResponse.serializer)
      ..add(OpenCardQrRequest.serializer)
      ..add(OpenCardResponse.serializer)
      ..add(SendOtpToOpenAccountResponse.serializer)
      ..add(SetAmountCommandResponse.serializer)
      ..add(SetAmountCommandResponseQrPaymentStatusEnum.serializer)
      ..add(StartOpenAccountRequest.serializer)
      ..add(StartOpenAccountResponse.serializer)
      ..add(StmDepositCashRequest.serializer)
      ..add(StmDepositCashResponse.serializer)
      ..add(SubmitAvatarApiRequest.serializer)
      ..add(SubmitAvatarResponse.serializer)
      ..add(SubmitCardInfoApiRequest.serializer)
      ..add(SubmitCardInfoResponse.serializer)
      ..add(SubmitContractApiRequest.serializer)
      ..add(SubmitContractResponse.serializer)
      ..add(SubmitInfoApiRequest.serializer)
      ..add(SubmitInfoApiRequestGenderEnum.serializer)
      ..add(SubmitInfoApiRequestNationalityEnum.serializer)
      ..add(SubmitInfoResponse.serializer)
      ..add(TokenResponse.serializer)
      ..add(VerifyOtpApiRequest.serializer)
      ..add(VerifyOtpToOpenAccountResponse.serializer)
      ..add(WaitCaptureActionResponse.serializer)
      ..add(WaitCaptureActionResponseTakePhotoStatusEnum.serializer)
      ..add(WaitScanQRDoneResponse.serializer)
      ..add(WaitScanQrResponse.serializer)
      ..add(WaitSelectAccountResponse.serializer)
      ..add(WaitVerifyContractResponse.serializer)
      ..add(WaitVerifyContractResponseOpenAccountStatusEnum.serializer)
      ..add(WaitVerifyPhotoStatusResponse.serializer)
      ..add(WaitVerifyPhotoStatusResponseTakePhotoStatusEnum.serializer)
      ..add(WaitVerifyUserInfoResponse.serializer)
      ..add(WaitVerifyUserInfoResponseOpenAccountStatusEnum.serializer)
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(CardProductSupportedOpen)]),
          () => new ListBuilder<CardProductSupportedOpen>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
