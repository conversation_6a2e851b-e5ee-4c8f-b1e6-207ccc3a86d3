//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'package:dio/dio.dart';
import 'package:built_value/serializer.dart';
import 'package:ksbank_api_stm/src/serializers.dart';
import 'package:ksbank_api_stm/src/auth/api_key_auth.dart';
import 'package:ksbank_api_stm/src/auth/basic_auth.dart';
import 'package:ksbank_api_stm/src/auth/bearer_auth.dart';
import 'package:ksbank_api_stm/src/auth/oauth.dart';
import 'package:ksbank_api_stm/src/api/apimth_bng_mqr_api.dart';
import 'package:ksbank_api_stm/src/api/apimti_khon_qua_stm_api.dart';
import 'package:ksbank_api_stm/src/api/api_upload_media_cho_stm_api.dart';
import 'package:ksbank_api_stm/src/api/bankbusinessforstmapi_api.dart';
import 'package:ksbank_api_stm/src/api/qrpaymentstmapi_api.dart';

class KsbankApiStm {
  static const String basePath = r'https://dev-ksapi.ssf.vn/stm';

  final Dio dio;
  final Serializers serializers;

  KsbankApiStm({
    Dio? dio,
    Serializers? serializers,
    String? basePathOverride,
    List<Interceptor>? interceptors,
  })  : this.serializers = serializers ?? standardSerializers,
        this.dio = dio ??
            Dio(BaseOptions(
              baseUrl: basePathOverride ?? basePath,
              connectTimeout: const Duration(milliseconds: 5000),
              receiveTimeout: const Duration(milliseconds: 3000),
            )) {
    if (interceptors == null) {
      this.dio.interceptors.addAll([
        OAuthInterceptor(),
        BasicAuthInterceptor(),
        BearerAuthInterceptor(),
        ApiKeyAuthInterceptor(),
      ]);
    } else {
      this.dio.interceptors.addAll(interceptors);
    }
  }

  void setOAuthToken(String name, String token) {
    if (this.dio.interceptors.any((i) => i is OAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is OAuthInterceptor)
              as OAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBearerAuth(String name, String token) {
    if (this.dio.interceptors.any((i) => i is BearerAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BearerAuthInterceptor)
              as BearerAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBasicAuth(String name, String username, String password) {
    if (this.dio.interceptors.any((i) => i is BasicAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BasicAuthInterceptor)
              as BasicAuthInterceptor)
          .authInfo[name] = BasicAuthInfo(username, password);
    }
  }

  void setApiKey(String name, String apiKey) {
    if (this.dio.interceptors.any((i) => i is ApiKeyAuthInterceptor)) {
      (this
                  .dio
                  .interceptors
                  .firstWhere((element) => element is ApiKeyAuthInterceptor)
              as ApiKeyAuthInterceptor)
          .apiKeys[name] = apiKey;
    }
  }

  /// Get APIMThBngMQRApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  APIMThBngMQRApi getAPIMThBngMQRApi() {
    return APIMThBngMQRApi(dio, serializers);
  }

  /// Get APIMTiKhonQuaSTMApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  APIMTiKhonQuaSTMApi getAPIMTiKhonQuaSTMApi() {
    return APIMTiKhonQuaSTMApi(dio, serializers);
  }

  /// Get APIUploadMediaChoSTMApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  APIUploadMediaChoSTMApi getAPIUploadMediaChoSTMApi() {
    return APIUploadMediaChoSTMApi(dio, serializers);
  }

  /// Get BANKBUSINESSFORSTMAPIApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  BANKBUSINESSFORSTMAPIApi getBANKBUSINESSFORSTMAPIApi() {
    return BANKBUSINESSFORSTMAPIApi(dio, serializers);
  }

  /// Get QRPAYMENTSTMAPIApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  QRPAYMENTSTMAPIApi getQRPAYMENTSTMAPIApi() {
    return QRPAYMENTSTMAPIApi(dio, serializers);
  }
}
