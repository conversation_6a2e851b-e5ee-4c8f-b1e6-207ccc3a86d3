import 'package:test/test.dart';
import 'package:ksbank_api_stm/ksbank_api_stm.dart';

// tests for MapTemplateToSTMRequest
void main() {
  final instance = MapTemplateToSTMRequestBuilder();
  // TODO add properties to the builder and call build()

  group(MapTemplateToSTMRequest, () {
    // String atmId
    test('to test the property `atmId`', () async {
      // TODO
    });

    // num transactionAmount
    test('to test the property `transactionAmount`', () async {
      // TODO
    });

    // String accountNumber
    test('to test the property `accountNumber`', () async {
      // TODO
    });

    // bool printReceipt
    test('to test the property `printReceipt`', () async {
      // TODO
    });

    // String otp
    test('to test the property `otp`', () async {
      // TODO
    });
  });
}
