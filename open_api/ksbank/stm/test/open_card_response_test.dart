import 'package:test/test.dart';
import 'package:ksbank_api_stm/ksbank_api_stm.dart';

// tests for OpenCardResponse
void main() {
  final instance = OpenCardResponseBuilder();
  // TODO add properties to the builder and call build()

  group(OpenCardResponse, () {
    // String userName
    test('to test the property `userName`', () async {
      // TODO
    });

    // String cifNo
    test('to test the property `cifNo`', () async {
      // TODO
    });

    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // String accountNumber
    test('to test the property `accountNumber`', () async {
      // TODO
    });

    // String cardNumber
    test('to test the property `cardNumber`', () async {
      // TODO
    });

    // DateTime openCardTime
    test('to test the property `openCardTime`', () async {
      // TODO
    });
  });
}
