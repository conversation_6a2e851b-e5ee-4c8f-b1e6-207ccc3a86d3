import 'package:test/test.dart';
import 'package:ksbank_api_stm/ksbank_api_stm.dart';

// tests for StmDepositCashRequest
void main() {
  final instance = StmDepositCashRequestBuilder();
  // TODO add properties to the builder and call build()

  group(StmDepositCashRequest, () {
    // String stmId
    test('to test the property `stmId`', () async {
      // TODO
    });

    // String fromCardNo
    test('to test the property `fromCardNo`', () async {
      // TODO
    });

    // String fromIdCard
    test('to test the property `fromIdCard`', () async {
      // TODO
    });

    // String isMyAcct
    test('to test the property `isMyAcct`', () async {
      // TODO
    });

    // String tranAmount
    test('to test the property `tranAmount`', () async {
      // TODO
    });

    // String descText
    test('to test the property `descText`', () async {
      // TODO
    });
  });
}
