# ksbank_api_stm.api.APIMThBngMQRApi

## Load the API package
```dart
import 'package:ksbank_api_stm/api.dart';
```

All URIs are relative to *https://dev-ksapi.ssf.vn/stm*

Method | HTTP request | Description
------------- | ------------- | -------------
[**cancelTransaction**](APIMThBngMQRApi.md#canceltransaction) | **POST** /open-card/cancel/{transactionId} | 10. [STM API] Hủy giao dịch mở thẻ
[**confirmTakeCard**](APIMThBngMQRApi.md#confirmtakecard) | **POST** /open-card/confirm-take-card/{transactionId} | 9. [STM API] báo việc in thẻ và nhả thẻ thành công hay thất bại
[**getCardProductSupportedOpen**](APIMThBngMQRApi.md#getcardproductsupportedopen) | **GET** /open-card/mobile/getCardProductSupportedOpen | [Mobile] Mobile gọi danh sách thẻ STM hỗ trợ mở
[**getQrCode1**](APIMThBngMQRApi.md#getqrcode1) | **POST** /open-card/qr-code | 1. [STM API] get QR code
[**getStmInfo1**](APIMThBngMQRApi.md#getstminfo1) | **GET** /open-card/stm-info/{transactionId} | 2. [Mobile API] Lấy thông tin của máy STM qua mã QR
[**mappingAccountInfo**](APIMThBngMQRApi.md#mappingaccountinfo) | **POST** /open-card/account-info/{transactionId} | 4. [Mobile API] Đẩy lên thông tin tài khoản được chọn của người dùng
[**mobileCancelTransaction**](APIMThBngMQRApi.md#mobilecanceltransaction) | **POST** /open-card/mobile/cancel/{transactionId} | 11. [Mobile] Hủy giao dịch mở thẻ
[**openCard**](APIMThBngMQRApi.md#opencard) | **POST** /open-card/execute-open-card/{transactionId} | 7. [STM API] Api thực hiện mở thẻ
[**submitCardInfo**](APIMThBngMQRApi.md#submitcardinfo) | **POST** /open-card/card-info/{transactionId} | 6. [STM API] đẩy thông tin của thẻ lên
[**waitScanQr**](APIMThBngMQRApi.md#waitscanqr) | **GET** /open-card/wait-scan-qr-code/{transactionId} | 3. [STM API] đợi mobile scan qr QR code
[**waitSelectAccount**](APIMThBngMQRApi.md#waitselectaccount) | **GET** /open-card/wait-select-account/{transactionId} | 5. [STM API] đợi mobile chọn tài khoản


# **cancelTransaction**
> BaseResponseCancelOpenCardResponse cancelTransaction(transactionId)

10. [STM API] Hủy giao dịch mở thẻ

Gọi api này khi ấn vào nút hủy hoặc quá thời gian đợi của luồng mà chưa hoàn thành việc mở thẻ

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = api.cancelTransaction(transactionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->cancelTransaction: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 

### Return type

[**BaseResponseCancelOpenCardResponse**](BaseResponseCancelOpenCardResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **confirmTakeCard**
> BaseResponseOpenCardConfirmTakeCardResponse confirmTakeCard(transactionId, confirmOpenCardApiRequest)

9. [STM API] báo việc in thẻ và nhả thẻ thành công hay thất bại

Sau khi STM tiến hành in thẻ, nếu thành công hay trục trặc gì và thất bại thì báo lên server qua api này

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 
final ConfirmOpenCardApiRequest confirmOpenCardApiRequest = ; // ConfirmOpenCardApiRequest | 

try {
    final response = api.confirmTakeCard(transactionId, confirmOpenCardApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->confirmTakeCard: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 
 **confirmOpenCardApiRequest** | [**ConfirmOpenCardApiRequest**](ConfirmOpenCardApiRequest.md)|  | 

### Return type

[**BaseResponseOpenCardConfirmTakeCardResponse**](BaseResponseOpenCardConfirmTakeCardResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getCardProductSupportedOpen**
> BaseResponseGetCardProductSupportedOpenResponse getCardProductSupportedOpen()

[Mobile] Mobile gọi danh sách thẻ STM hỗ trợ mở

Mobile gọi danh sách thẻ STM hỗ trợ mở

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();

try {
    final response = api.getCardProductSupportedOpen();
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->getCardProductSupportedOpen: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseGetCardProductSupportedOpenResponse**](BaseResponseGetCardProductSupportedOpenResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getQrCode1**
> BaseResponseOpenCardQRResponse getQrCode1(openCardQrRequest)

1. [STM API] get QR code

Ban đầu khi người dùng đến cây STM, ấn vào phần mở thẻ thì sẽ gọi API này để lấy nội dungmã QR và hiển thị cho người dùng

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final OpenCardQrRequest openCardQrRequest = ; // OpenCardQrRequest | 

try {
    final response = api.getQrCode1(openCardQrRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->getQrCode1: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **openCardQrRequest** | [**OpenCardQrRequest**](OpenCardQrRequest.md)|  | 

### Return type

[**BaseResponseOpenCardQRResponse**](BaseResponseOpenCardQRResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getStmInfo1**
> BaseResponseGetOpenCardDetailResponse getStmInfo1(transactionId)

2. [Mobile API] Lấy thông tin của máy STM qua mã QR

Sau khi quét mã QR, mobile sẽ nhận được một deeplink trong đó có chứa action là mở thẻbằng mã QR và transactionId của luồng mở thẻ này, lấy transactionId đó post vào API này để lấythông tin máy STM và hiển thị thông tin máy STM cho người dùng sau khi quét.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = api.getStmInfo1(transactionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->getStmInfo1: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 

### Return type

[**BaseResponseGetOpenCardDetailResponse**](BaseResponseGetOpenCardDetailResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **mappingAccountInfo**
> BaseResponseMappingAccountResponse mappingAccountInfo(transactionId, mappingAccountApiRequest)

4. [Mobile API] Đẩy lên thông tin tài khoản được chọn của người dùng

Sau khi quét mã QR nhận được thông tin máy STM (id và vị trí) hiển thị nó lên cùng với cảmột select button cho phép chọn tài khoản để mở thẻ, sau khi chọn tài khoản xong thì đẩy thông tinnày lên qua api này sau khi ấn tiếp tục

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 
final MappingAccountApiRequest mappingAccountApiRequest = ; // MappingAccountApiRequest | 

try {
    final response = api.mappingAccountInfo(transactionId, mappingAccountApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->mappingAccountInfo: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 
 **mappingAccountApiRequest** | [**MappingAccountApiRequest**](MappingAccountApiRequest.md)|  | 

### Return type

[**BaseResponseMappingAccountResponse**](BaseResponseMappingAccountResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **mobileCancelTransaction**
> BaseResponseCancelOpenCardResponse mobileCancelTransaction(transactionId)

11. [Mobile] Hủy giao dịch mở thẻ

Mobile hủy giao dịch

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = api.mobileCancelTransaction(transactionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->mobileCancelTransaction: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 

### Return type

[**BaseResponseCancelOpenCardResponse**](BaseResponseCancelOpenCardResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **openCard**
> BaseResponseOpenCardResponse openCard(transactionId, executeOpenCardApiRequest)

7. [STM API] Api thực hiện mở thẻ

Sau khi chọn số tài khoản cần mở thẻ và ấn tiếp tục, màn hình sẽ hiển thị lên phần nhậpmã PIN cho etoken, nhập mã pin, lấy được etoken và truyền vào body của API này để gọi việc mở thẻ

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 
final ExecuteOpenCardApiRequest executeOpenCardApiRequest = ; // ExecuteOpenCardApiRequest | 

try {
    final response = api.openCard(transactionId, executeOpenCardApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->openCard: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 
 **executeOpenCardApiRequest** | [**ExecuteOpenCardApiRequest**](ExecuteOpenCardApiRequest.md)|  | 

### Return type

[**BaseResponseOpenCardResponse**](BaseResponseOpenCardResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submitCardInfo**
> BaseResponseSubmitCardInfoResponse submitCardInfo(transactionId, submitCardInfoApiRequest)

6. [STM API] đẩy thông tin của thẻ lên

Khi người dùng đã quét xong mã QR, STM lấy một thẻ và đẩy số thẻ lên cho hệ thốngđể dùng cho mở thẻ ở luồng tiếp theo

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 
final SubmitCardInfoApiRequest submitCardInfoApiRequest = ; // SubmitCardInfoApiRequest | 

try {
    final response = api.submitCardInfo(transactionId, submitCardInfoApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->submitCardInfo: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 
 **submitCardInfoApiRequest** | [**SubmitCardInfoApiRequest**](SubmitCardInfoApiRequest.md)|  | 

### Return type

[**BaseResponseSubmitCardInfoResponse**](BaseResponseSubmitCardInfoResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **waitScanQr**
> BaseResponseWaitScanQrResponse waitScanQr(transactionId)

3. [STM API] đợi mobile scan qr QR code

Khi mobile scan QR code xong thì sẽ trả về thông tin cho máy STM là đã scan xong và thôngtin của người dùng, STM nhận được thì nên tắt cái màn mã QR đi và hiển thị thông báo cùng với hiệu ứng loading đợi, thông báo kiểu :Đang tiến hành mở thẻ cho khách hàng ABC ... (API này có thể sau sẽ update thêm là chọn loại thẻ phát hành nếu như có URD)

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = api.waitScanQr(transactionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->waitScanQr: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 

### Return type

[**BaseResponseWaitScanQrResponse**](BaseResponseWaitScanQrResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **waitSelectAccount**
> BaseResponseWaitSelectAccountResponse waitSelectAccount(transactionId)

5. [STM API] đợi mobile chọn tài khoản

Api này cho phép STM đợi thông tin mở tài khoản bao gồm số tài khoản mở thẻ, loại thẻ (bổ sung sau) và hiển thị lên màn hình STM luôn và đợi đến phần đợi thực hiện tạo thẻ.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = api.waitSelectAccount(transactionId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling APIMThBngMQRApi->waitSelectAccount: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **transactionId** | **String**|  | 

### Return type

[**BaseResponseWaitSelectAccountResponse**](BaseResponseWaitSelectAccountResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

