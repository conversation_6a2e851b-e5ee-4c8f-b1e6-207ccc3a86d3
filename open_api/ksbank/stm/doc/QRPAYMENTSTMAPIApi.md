# ksbank_api_stm.api.QRPAYMENTSTMAPIApi

## Load the API package
```dart
import 'package:ksbank_api_stm/api.dart';
```

All URIs are relative to *https://dev-ksapi.ssf.vn/stm*

Method | HTTP request | Description
------------- | ------------- | -------------
[**cancelQRPayment**](QRPAYMENTSTMAPIApi.md#cancelqrpayment) | **POST** /qr-payment/cancel/{qrPaymentId} | 
[**confirmQRTransfer**](QRPAYMENTSTMAPIApi.md#confirmqrtransfer) | **POST** /qr-payment/old/confirm/{qrPaymentId} | 7.[STM gọi] API confirm việc rút tiền. 
[**confirmQRTransferV2**](QRPAYMENTSTMAPIApi.md#confirmqrtransferv2) | **POST** /qr-payment/confirm/{qrPaymentId} | 7.[STM gọi] API confirm việc rút tiền. (Version2)
[**executeQrTransfer**](QRPAYMENTSTMAPIApi.md#executeqrtransfer) | **POST** /qr-payment/old/execute/{qrPaymentId} | 6.[STM gọi] API hạch toán.
[**executeQrTransferV2**](QRPAYMENTSTMAPIApi.md#executeqrtransferv2) | **POST** /qr-payment/execute/{qrPaymentId} | 6.[STM gọi] API hạch toán. (Version2)
[**getQrCode**](QRPAYMENTSTMAPIApi.md#getqrcode) | **POST** /qr-payment/qr-code | 1. [STM API] get QR code
[**getStmInfo**](QRPAYMENTSTMAPIApi.md#getstminfo) | **POST** /qr-payment/detail/{qrPaymentId} | 3. API get STM info via Scan QR code.
[**setTransactionAmount**](QRPAYMENTSTMAPIApi.md#settransactionamount) | **POST** /qr-payment/fulfil/{qrPaymentId} | 4. API fulfill transaction form.
[**waitScanQRDone**](QRPAYMENTSTMAPIApi.md#waitscanqrdone) | **GET** /qr-payment/wait-fulfil-template/{qrPaymentId} | 2. STM gọi, đợi người dùng chọn xong tài khoản 


# **cancelQRPayment**
> BaseResponseCancelQRPaymentResponse cancelQRPayment(qrPaymentId)



### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 

try {
    final response = api.cancelQRPayment(qrPaymentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->cancelQRPayment: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 

### Return type

[**BaseResponseCancelQRPaymentResponse**](BaseResponseCancelQRPaymentResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **confirmQRTransfer**
> BaseResponseConfirmQRTransactionResponse confirmQRTransfer(qrPaymentId, confirmQRPaymentApiRequest)

7.[STM gọi] API confirm việc rút tiền. 

STM gọi api này để xác nhận việc rút tiền có thành công hay không, nếu rút tiền thành công để status là SUCCESSnếu thất bại để status là  FAILED gửi kèm message ghi lý do thất bại nhả tiền.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 
final ConfirmQRPaymentApiRequest confirmQRPaymentApiRequest = ; // ConfirmQRPaymentApiRequest | 

try {
    final response = api.confirmQRTransfer(qrPaymentId, confirmQRPaymentApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->confirmQRTransfer: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 
 **confirmQRPaymentApiRequest** | [**ConfirmQRPaymentApiRequest**](ConfirmQRPaymentApiRequest.md)|  | 

### Return type

[**BaseResponseConfirmQRTransactionResponse**](BaseResponseConfirmQRTransactionResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **confirmQRTransferV2**
> BaseResponseConfirmQRTransactionResponseV2 confirmQRTransferV2(qrPaymentId, confirmQRPaymentApiRequest)

7.[STM gọi] API confirm việc rút tiền. (Version2)

STM gọi api này để xác nhận việc rút tiền có thành công hay không, nếu rút tiền thành công để status là SUCCESSnếu thất bại để status là  FAILED gửi kèm message ghi lý do thất bại nhả tiền.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 
final ConfirmQRPaymentApiRequest confirmQRPaymentApiRequest = ; // ConfirmQRPaymentApiRequest | 

try {
    final response = api.confirmQRTransferV2(qrPaymentId, confirmQRPaymentApiRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->confirmQRTransferV2: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 
 **confirmQRPaymentApiRequest** | [**ConfirmQRPaymentApiRequest**](ConfirmQRPaymentApiRequest.md)|  | 

### Return type

[**BaseResponseConfirmQRTransactionResponseV2**](BaseResponseConfirmQRTransactionResponseV2.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **executeQrTransfer**
> BaseResponseExecuteQRTransferResponse executeQrTransfer(qrPaymentId)

6.[STM gọi] API hạch toán.

STM gọi API này sau khi người dùng đã chọn tài khoản để thanh toán.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 

try {
    final response = api.executeQrTransfer(qrPaymentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->executeQrTransfer: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 

### Return type

[**BaseResponseExecuteQRTransferResponse**](BaseResponseExecuteQRTransferResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **executeQrTransferV2**
> BaseResponseExecuteQRTransferResponseV2 executeQrTransferV2(qrPaymentId)

6.[STM gọi] API hạch toán. (Version2)

STM gọi API này sau khi người dùng đã chọn tài khoản để thanh toán.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 

try {
    final response = api.executeQrTransferV2(qrPaymentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->executeQrTransferV2: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 

### Return type

[**BaseResponseExecuteQRTransferResponseV2**](BaseResponseExecuteQRTransferResponseV2.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getQrCode**
> BaseResponseGetQrCodeResponse getQrCode(getQRCodeRequest)

1. [STM API] get QR code

STM call this API to get QR code.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final GetQRCodeRequest getQRCodeRequest = ; // GetQRCodeRequest | 

try {
    final response = api.getQrCode(getQRCodeRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->getQrCode: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **getQRCodeRequest** | [**GetQRCodeRequest**](GetQRCodeRequest.md)|  | 

### Return type

[**BaseResponseGetQrCodeResponse**](BaseResponseGetQrCodeResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getStmInfo**
> BaseResponseGetQRPaymentResponse getStmInfo(qrPaymentId)

3. API get STM info via Scan QR code.

Mobile call this API to get STM info for next step.

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 

try {
    final response = api.getStmInfo(qrPaymentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->getStmInfo: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 

### Return type

[**BaseResponseGetQRPaymentResponse**](BaseResponseGetQRPaymentResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setTransactionAmount**
> BaseResponseSetAmountCommandResponse setTransactionAmount(qrPaymentId, mapTemplateToSTMRequest)

4. API fulfill transaction form.

Mobile api: mobile gọi api này để để chọn tài khoản cũng như số tiền muốn rút

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 
final MapTemplateToSTMRequest mapTemplateToSTMRequest = ; // MapTemplateToSTMRequest | 

try {
    final response = api.setTransactionAmount(qrPaymentId, mapTemplateToSTMRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->setTransactionAmount: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 
 **mapTemplateToSTMRequest** | [**MapTemplateToSTMRequest**](MapTemplateToSTMRequest.md)|  | 

### Return type

[**BaseResponseSetAmountCommandResponse**](BaseResponseSetAmountCommandResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **waitScanQRDone**
> BaseResponseWaitScanQRDoneResponse waitScanQRDone(qrPaymentId)

2. STM gọi, đợi người dùng chọn xong tài khoản 

Đợi người dùng chọn xong tài khoản, sau khi chọn xong tài khoản thì chạy luôn đến api hạch toán

### Example
```dart
import 'package:ksbank_api_stm/api.dart';

final api = KsbankApiStm().getQRPAYMENTSTMAPIApi();
final String qrPaymentId = qrPaymentId_example; // String | 

try {
    final response = api.waitScanQRDone(qrPaymentId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling QRPAYMENTSTMAPIApi->waitScanQRDone: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qrPaymentId** | **String**|  | 

### Return type

[**BaseResponseWaitScanQRDoneResponse**](BaseResponseWaitScanQRDoneResponse.md)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

