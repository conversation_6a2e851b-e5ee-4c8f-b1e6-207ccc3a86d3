# ksbank_api_stm.model.CreateAccountApiRequest

## Load the model package
```dart
import 'package:ksbank_api_stm/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**idCardNumber** | **String** |  | [optional] 
**phoneNumber** | **String** |  | [optional] 
**email** | **String** |  | [optional] 
**name** | **String** |  | [optional] 
**dob** | [**Date**](Date.md) |  | [optional] 
**gender** | **String** |  | [optional] 
**nationality** | **String** |  | [optional] 
**hometown** | **String** |  | [optional] 
**city** | **String** |  | [optional] 
**state** | **String** |  | [optional] 
**idCardIssuePlc** | **String** |  | [optional] 
**idCardIssueDt** | [**Date**](Date.md) |  | [optional] 
**permanentAddress** | **String** |  | [optional] 
**contactAddress** | **String** |  | [optional] 
**idCardImageUp** | **String** |  | [optional] 
**idCardImageDown** | **String** |  | [optional] 
**openAccountFormUp** | **String** |  | [optional] 
**openAccountFormDown** | **String** |  | [optional] 
**contractFormUp** | **String** |  | [optional] 
**contractFormDown** | **String** |  | [optional] 
**servicePack** | **String** |  | [optional] 
**cardNumber** | **String** |  | [optional] 
**issueDate** | [**Date**](Date.md) |  | [optional] 
**expirationDate** | [**Date**](Date.md) |  | [optional] 
**track1** | **String** |  | [optional] 
**branchCode** | **String** |  | [optional] 
**isOpenCard** | **bool** |  | [optional] 
**openCard** | **bool** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


