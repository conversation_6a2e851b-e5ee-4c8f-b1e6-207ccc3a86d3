# ksbank_api_stm (EXPERIMENTAL)
Documentation STM service API

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_stm: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_stm:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_stm:
    path: /path/to/ksbank_api_stm
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:ksbank_api_stm/ksbank_api_stm.dart';


final api = KsbankApiStm().getAPIMThBngMQRApi();
final String transactionId = transactionId_example; // String | 

try {
    final response = await api.cancelTransaction(transactionId);
    print(response);
} catch on DioException (e) {
    print("Exception when calling APIMThBngMQRApi->cancelTransaction: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *https://dev-ksapi.ssf.vn/stm*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**cancelTransaction**](doc/APIMThBngMQRApi.md#canceltransaction) | **POST** /open-card/cancel/{transactionId} | 10. [STM API] Hủy giao dịch mở thẻ
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**confirmTakeCard**](doc/APIMThBngMQRApi.md#confirmtakecard) | **POST** /open-card/confirm-take-card/{transactionId} | 9. [STM API] báo việc in thẻ và nhả thẻ thành công hay thất bại
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**getCardProductSupportedOpen**](doc/APIMThBngMQRApi.md#getcardproductsupportedopen) | **GET** /open-card/mobile/getCardProductSupportedOpen | [Mobile] Mobile gọi danh sách thẻ STM hỗ trợ mở
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**getQrCode1**](doc/APIMThBngMQRApi.md#getqrcode1) | **POST** /open-card/qr-code | 1. [STM API] get QR code
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**getStmInfo1**](doc/APIMThBngMQRApi.md#getstminfo1) | **GET** /open-card/stm-info/{transactionId} | 2. [Mobile API] Lấy thông tin của máy STM qua mã QR
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**mappingAccountInfo**](doc/APIMThBngMQRApi.md#mappingaccountinfo) | **POST** /open-card/account-info/{transactionId} | 4. [Mobile API] Đẩy lên thông tin tài khoản được chọn của người dùng
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**mobileCancelTransaction**](doc/APIMThBngMQRApi.md#mobilecanceltransaction) | **POST** /open-card/mobile/cancel/{transactionId} | 11. [Mobile] Hủy giao dịch mở thẻ
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**openCard**](doc/APIMThBngMQRApi.md#opencard) | **POST** /open-card/execute-open-card/{transactionId} | 7. [STM API] Api thực hiện mở thẻ
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**submitCardInfo**](doc/APIMThBngMQRApi.md#submitcardinfo) | **POST** /open-card/card-info/{transactionId} | 6. [STM API] đẩy thông tin của thẻ lên
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**waitScanQr**](doc/APIMThBngMQRApi.md#waitscanqr) | **GET** /open-card/wait-scan-qr-code/{transactionId} | 3. [STM API] đợi mobile scan qr QR code
[*APIMThBngMQRApi*](doc/APIMThBngMQRApi.md) | [**waitSelectAccount**](doc/APIMThBngMQRApi.md#waitselectaccount) | **GET** /open-card/wait-select-account/{transactionId} | 5. [STM API] đợi mobile chọn tài khoản
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**cancelOpenAccount**](doc/APIMTiKhonQuaSTMApi.md#cancelopenaccount) | **POST** /open-account/cancel/{transactionId} | 2.  Api hủy giao dịch tạo tài khoản
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**confirmTakeCard1**](doc/APIMTiKhonQuaSTMApi.md#confirmtakecard1) | **POST** /open-account/confirm-take-card/{transactionId} | 6. API xác nhận người dùng đã lấy thẻ
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**createAccount**](doc/APIMTiKhonQuaSTMApi.md#createaccount) | **POST** /open-account/create-account/{transactionId} | 5. API tạo tài khoản
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**sendOtpToOpenAccount**](doc/APIMTiKhonQuaSTMApi.md#sendotptoopenaccount) | **POST** /open-account/send-otp/{transactionId} | 3. Api gửi OTP
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**startOpenAccount**](doc/APIMTiKhonQuaSTMApi.md#startopenaccount) | **POST** /open-account/start-open-account | 1. Api bắt đầu mở tài khoản
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**submitAvatar**](doc/APIMTiKhonQuaSTMApi.md#submitavatar) | **POST** /open-account/submit-avatar/{transactionId} | 1.6 Api submit avatar
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**submitContract**](doc/APIMTiKhonQuaSTMApi.md#submitcontract) | **POST** /open-account/submit-contract/{transactionId} | 5.1 Api submit contract
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**submitInfo**](doc/APIMTiKhonQuaSTMApi.md#submitinfo) | **POST** /open-account/submit-info/{transactionId} | 1.5 Api submit info
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**verifyOtpToOpenAccount**](doc/APIMTiKhonQuaSTMApi.md#verifyotptoopenaccount) | **POST** /open-account/verify-otp/{transactionId} | 4. Api xác nhận OTP mở tài khoản
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**waitCaptureAction**](doc/APIMTiKhonQuaSTMApi.md#waitcaptureaction) | **POST** /open-account/wait-capture-action/{transactionId} | 4.1. Đợi chụp ảnh
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**waitVerifyContract**](doc/APIMTiKhonQuaSTMApi.md#waitverifycontract) | **POST** /open-account/wait-verify-contract/{transactionId} | 8. Đợi xác thực thông tin hợp đồng
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**waiteVerifyInfo**](doc/APIMTiKhonQuaSTMApi.md#waiteverifyinfo) | **POST** /open-account/wait-verify-info/{transactionId} | 7. Đợi xác thực thông tin người dùng
[*APIMTiKhonQuaSTMApi*](doc/APIMTiKhonQuaSTMApi.md) | [**waiteVerifyPhoto**](doc/APIMTiKhonQuaSTMApi.md#waiteverifyphoto) | **POST** /open-account/wait-verify-photo/{transactionId} | 4.2. Đợi confirm ảnh
[*APIUploadMediaChoSTMApi*](doc/APIUploadMediaChoSTMApi.md) | [**upload**](doc/APIUploadMediaChoSTMApi.md#upload) | **POST** /media/upload | 
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**alarmOperation**](doc/BANKBUSINESSFORSTMAPIApi.md#alarmoperation) | **POST** /bank/operation/alarm | 3.4. [STM API] Alarm to Admin
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**checkCardActive**](doc/BANKBUSINESSFORSTMAPIApi.md#checkcardactive) | **GET** /bank/cards/active-status | 3.1. [STM API] Check active card
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**checkCardTypeByCardNo**](doc/BANKBUSINESSFORSTMAPIApi.md#checkcardtypebycardno) | **POST** /bank/checkCardTypeByCardNo | 
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**depositCash**](doc/BANKBUSINESSFORSTMAPIApi.md#depositcash) | **POST** /bank/stm-deposit-cash | 
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**getAccountByCardNo**](doc/BANKBUSINESSFORSTMAPIApi.md#getaccountbycardno) | **POST** /bank/get-account-by-card-no | 
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**getAccountLimit**](doc/BANKBUSINESSFORSTMAPIApi.md#getaccountlimit) | **GET** /bank/transactions/withdrawal/limit-info | 3.2. [STM API] Get account limit
[*BANKBUSINESSFORSTMAPIApi*](doc/BANKBUSINESSFORSTMAPIApi.md) | [**getBillFee**](doc/BANKBUSINESSFORSTMAPIApi.md#getbillfee) | **GET** /bank/transactions/receipt/fee | 3.3. [STM API] Get bill fee
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**cancelQRPayment**](doc/QRPAYMENTSTMAPIApi.md#cancelqrpayment) | **POST** /qr-payment/cancel/{qrPaymentId} | 
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**confirmQRTransfer**](doc/QRPAYMENTSTMAPIApi.md#confirmqrtransfer) | **POST** /qr-payment/old/confirm/{qrPaymentId} | 7.[STM gọi] API confirm việc rút tiền. 
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**confirmQRTransferV2**](doc/QRPAYMENTSTMAPIApi.md#confirmqrtransferv2) | **POST** /qr-payment/confirm/{qrPaymentId} | 7.[STM gọi] API confirm việc rút tiền. (Version2)
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**executeQrTransfer**](doc/QRPAYMENTSTMAPIApi.md#executeqrtransfer) | **POST** /qr-payment/old/execute/{qrPaymentId} | 6.[STM gọi] API hạch toán.
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**executeQrTransferV2**](doc/QRPAYMENTSTMAPIApi.md#executeqrtransferv2) | **POST** /qr-payment/execute/{qrPaymentId} | 6.[STM gọi] API hạch toán. (Version2)
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**getQrCode**](doc/QRPAYMENTSTMAPIApi.md#getqrcode) | **POST** /qr-payment/qr-code | 1. [STM API] get QR code
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**getStmInfo**](doc/QRPAYMENTSTMAPIApi.md#getstminfo) | **POST** /qr-payment/detail/{qrPaymentId} | 3. API get STM info via Scan QR code.
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**setTransactionAmount**](doc/QRPAYMENTSTMAPIApi.md#settransactionamount) | **POST** /qr-payment/fulfil/{qrPaymentId} | 4. API fulfill transaction form.
[*QRPAYMENTSTMAPIApi*](doc/QRPAYMENTSTMAPIApi.md) | [**waitScanQRDone**](doc/QRPAYMENTSTMAPIApi.md#waitscanqrdone) | **GET** /qr-payment/wait-fulfil-template/{qrPaymentId} | 2. STM gọi, đợi người dùng chọn xong tài khoản 


## Documentation For Models

 - [AlarmOperationRequest](doc/AlarmOperationRequest.md)
 - [AlarmOperationResponse](doc/AlarmOperationResponse.md)
 - [BaseResponseAlarmOperationResponse](doc/BaseResponseAlarmOperationResponse.md)
 - [BaseResponseCancelOpenAccountResponse](doc/BaseResponseCancelOpenAccountResponse.md)
 - [BaseResponseCancelOpenCardResponse](doc/BaseResponseCancelOpenCardResponse.md)
 - [BaseResponseCancelQRPaymentResponse](doc/BaseResponseCancelQRPaymentResponse.md)
 - [BaseResponseCheckCardResponse](doc/BaseResponseCheckCardResponse.md)
 - [BaseResponseCheckCardTypeByCardNoResponse](doc/BaseResponseCheckCardTypeByCardNoResponse.md)
 - [BaseResponseConfirmQRTransactionResponse](doc/BaseResponseConfirmQRTransactionResponse.md)
 - [BaseResponseConfirmQRTransactionResponseV2](doc/BaseResponseConfirmQRTransactionResponseV2.md)
 - [BaseResponseConfirmTakeCardResponse](doc/BaseResponseConfirmTakeCardResponse.md)
 - [BaseResponseCreateAccountResponse](doc/BaseResponseCreateAccountResponse.md)
 - [BaseResponseExecuteQRTransferResponse](doc/BaseResponseExecuteQRTransferResponse.md)
 - [BaseResponseExecuteQRTransferResponseV2](doc/BaseResponseExecuteQRTransferResponseV2.md)
 - [BaseResponseGetAccountByCardNoResponse](doc/BaseResponseGetAccountByCardNoResponse.md)
 - [BaseResponseGetAccountLimitResponse](doc/BaseResponseGetAccountLimitResponse.md)
 - [BaseResponseGetBillFeeResponse](doc/BaseResponseGetBillFeeResponse.md)
 - [BaseResponseGetCardProductSupportedOpenResponse](doc/BaseResponseGetCardProductSupportedOpenResponse.md)
 - [BaseResponseGetOpenCardDetailResponse](doc/BaseResponseGetOpenCardDetailResponse.md)
 - [BaseResponseGetQRPaymentResponse](doc/BaseResponseGetQRPaymentResponse.md)
 - [BaseResponseGetQrCodeResponse](doc/BaseResponseGetQrCodeResponse.md)
 - [BaseResponseMappingAccountResponse](doc/BaseResponseMappingAccountResponse.md)
 - [BaseResponseOpenCardConfirmTakeCardResponse](doc/BaseResponseOpenCardConfirmTakeCardResponse.md)
 - [BaseResponseOpenCardQRResponse](doc/BaseResponseOpenCardQRResponse.md)
 - [BaseResponseOpenCardResponse](doc/BaseResponseOpenCardResponse.md)
 - [BaseResponseSendOtpToOpenAccountResponse](doc/BaseResponseSendOtpToOpenAccountResponse.md)
 - [BaseResponseSetAmountCommandResponse](doc/BaseResponseSetAmountCommandResponse.md)
 - [BaseResponseStartOpenAccountResponse](doc/BaseResponseStartOpenAccountResponse.md)
 - [BaseResponseStmDepositCashResponse](doc/BaseResponseStmDepositCashResponse.md)
 - [BaseResponseSubmitAvatarResponse](doc/BaseResponseSubmitAvatarResponse.md)
 - [BaseResponseSubmitCardInfoResponse](doc/BaseResponseSubmitCardInfoResponse.md)
 - [BaseResponseSubmitContractResponse](doc/BaseResponseSubmitContractResponse.md)
 - [BaseResponseSubmitInfoResponse](doc/BaseResponseSubmitInfoResponse.md)
 - [BaseResponseVerifyOtpToOpenAccountResponse](doc/BaseResponseVerifyOtpToOpenAccountResponse.md)
 - [BaseResponseWaitCaptureActionResponse](doc/BaseResponseWaitCaptureActionResponse.md)
 - [BaseResponseWaitScanQRDoneResponse](doc/BaseResponseWaitScanQRDoneResponse.md)
 - [BaseResponseWaitScanQrResponse](doc/BaseResponseWaitScanQrResponse.md)
 - [BaseResponseWaitSelectAccountResponse](doc/BaseResponseWaitSelectAccountResponse.md)
 - [BaseResponseWaitVerifyContractResponse](doc/BaseResponseWaitVerifyContractResponse.md)
 - [BaseResponseWaitVerifyPhotoStatusResponse](doc/BaseResponseWaitVerifyPhotoStatusResponse.md)
 - [BaseResponseWaitVerifyUserInfoResponse](doc/BaseResponseWaitVerifyUserInfoResponse.md)
 - [CancelOpenAccountResponse](doc/CancelOpenAccountResponse.md)
 - [CancelOpenCardResponse](doc/CancelOpenCardResponse.md)
 - [CancelQRPaymentResponse](doc/CancelQRPaymentResponse.md)
 - [CardProductSupportedOpen](doc/CardProductSupportedOpen.md)
 - [CheckCardResponse](doc/CheckCardResponse.md)
 - [CheckCardTypeByCardNoRequest](doc/CheckCardTypeByCardNoRequest.md)
 - [CheckCardTypeByCardNoResponse](doc/CheckCardTypeByCardNoResponse.md)
 - [ConfirmOpenCardApiRequest](doc/ConfirmOpenCardApiRequest.md)
 - [ConfirmQRPaymentApiRequest](doc/ConfirmQRPaymentApiRequest.md)
 - [ConfirmQRTransactionResponse](doc/ConfirmQRTransactionResponse.md)
 - [ConfirmQRTransactionResponseV2](doc/ConfirmQRTransactionResponseV2.md)
 - [ConfirmTakeCardApiRequest](doc/ConfirmTakeCardApiRequest.md)
 - [ConfirmTakeCardResponse](doc/ConfirmTakeCardResponse.md)
 - [CreateAccountApiRequest](doc/CreateAccountApiRequest.md)
 - [CreateAccountResponse](doc/CreateAccountResponse.md)
 - [ExecuteOpenCardApiRequest](doc/ExecuteOpenCardApiRequest.md)
 - [ExecuteQRTransferResponse](doc/ExecuteQRTransferResponse.md)
 - [ExecuteQRTransferResponseV2](doc/ExecuteQRTransferResponseV2.md)
 - [FileUploadResponse](doc/FileUploadResponse.md)
 - [GetAccountByCardNoRequest](doc/GetAccountByCardNoRequest.md)
 - [GetAccountByCardNoResponse](doc/GetAccountByCardNoResponse.md)
 - [GetAccountLimitResponse](doc/GetAccountLimitResponse.md)
 - [GetBillFeeResponse](doc/GetBillFeeResponse.md)
 - [GetCardProductSupportedOpenResponse](doc/GetCardProductSupportedOpenResponse.md)
 - [GetOpenCardDetailResponse](doc/GetOpenCardDetailResponse.md)
 - [GetQRCodeRequest](doc/GetQRCodeRequest.md)
 - [GetQRPaymentResponse](doc/GetQRPaymentResponse.md)
 - [GetQrCodeResponse](doc/GetQrCodeResponse.md)
 - [MapTemplateToSTMRequest](doc/MapTemplateToSTMRequest.md)
 - [MappingAccountApiRequest](doc/MappingAccountApiRequest.md)
 - [MappingAccountResponse](doc/MappingAccountResponse.md)
 - [NullType](doc/NullType.md)
 - [OpenCardConfirmTakeCardResponse](doc/OpenCardConfirmTakeCardResponse.md)
 - [OpenCardQRResponse](doc/OpenCardQRResponse.md)
 - [OpenCardQrRequest](doc/OpenCardQrRequest.md)
 - [OpenCardResponse](doc/OpenCardResponse.md)
 - [SendOtpToOpenAccountResponse](doc/SendOtpToOpenAccountResponse.md)
 - [SetAmountCommandResponse](doc/SetAmountCommandResponse.md)
 - [StartOpenAccountRequest](doc/StartOpenAccountRequest.md)
 - [StartOpenAccountResponse](doc/StartOpenAccountResponse.md)
 - [StmDepositCashRequest](doc/StmDepositCashRequest.md)
 - [StmDepositCashResponse](doc/StmDepositCashResponse.md)
 - [SubmitAvatarApiRequest](doc/SubmitAvatarApiRequest.md)
 - [SubmitAvatarResponse](doc/SubmitAvatarResponse.md)
 - [SubmitCardInfoApiRequest](doc/SubmitCardInfoApiRequest.md)
 - [SubmitCardInfoResponse](doc/SubmitCardInfoResponse.md)
 - [SubmitContractApiRequest](doc/SubmitContractApiRequest.md)
 - [SubmitContractResponse](doc/SubmitContractResponse.md)
 - [SubmitInfoApiRequest](doc/SubmitInfoApiRequest.md)
 - [SubmitInfoResponse](doc/SubmitInfoResponse.md)
 - [TokenResponse](doc/TokenResponse.md)
 - [VerifyOtpApiRequest](doc/VerifyOtpApiRequest.md)
 - [VerifyOtpToOpenAccountResponse](doc/VerifyOtpToOpenAccountResponse.md)
 - [WaitCaptureActionResponse](doc/WaitCaptureActionResponse.md)
 - [WaitScanQRDoneResponse](doc/WaitScanQRDoneResponse.md)
 - [WaitScanQrResponse](doc/WaitScanQrResponse.md)
 - [WaitSelectAccountResponse](doc/WaitSelectAccountResponse.md)
 - [WaitVerifyContractResponse](doc/WaitVerifyContractResponse.md)
 - [WaitVerifyPhotoStatusResponse](doc/WaitVerifyPhotoStatusResponse.md)
 - [WaitVerifyUserInfoResponse](doc/WaitVerifyUserInfoResponse.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### Authorization

- **Type**: HTTP Bearer Token authentication (Bearer [token])


## Author



