{"openapi": "3.0.1", "info": {"title": "Profile API", "description": "Documentation Profile API v1.0", "version": "1.0"}, "servers": [{"url": "http://localhost:8088"}], "security": [{"Authorization": []}], "tags": [{"name": "API dành cho các user từ máy STM", "description": "<PERSON><PERSON><PERSON> api liên quan đến user STM"}, {"name": "Đăng ký user qua VNPOST", "description": "Đăng ký user qua VNPOST"}, {"name": "User bank info resource", "description": "The write side for user bank info resource"}, {"name": "User command resource", "description": "The write side for user resource"}, {"name": "API địa chỉ các chi nh<PERSON>h", "description": "<PERSON>ra c<PERSON>u thông tin địa chỉ các chi nhánh"}, {"name": "User query resource", "description": "The read side for user resource"}, {"name": "Liveness resource", "description": "Liveness detection API endpoints"}, {"name": "User notification setting resource", "description": "Những api lấy và cài đặt thông tin notification cho người dùng"}, {"name": "User Setting resource", "description": "The user setting resource"}, {"name": "Device Info resource", "description": "The device info resource"}, {"name": "API Xác thực nhanh qua KLB", "description": "API Xác thực nhanh qua KLB"}, {"name": "Resource for manage bank's user", "description": "The write side for for manage bank's user"}, {"name": "User App Setting resource", "description": "<PERSON><PERSON><PERSON><PERSON> lý các thiết lập của khách hàng trên mobile app"}], "paths": {"/users/{userId}/customer-group/{customerGroupId}": {"put": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin nhóm khách hàng", "operationId": "updateCustomerGroup", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerGroupId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateCustomerGroupUseResponse"}}}}}}}, "/users/v1/bank-info/ebank-package": {"put": {"tags": ["User bank info resource"], "summary": "API cập nhật gói ebanking c<PERSON><PERSON> kh<PERSON>ch hàng.", "description": "Sử dụng để cho pháp kh<PERSON>ch hàng update thông gói dịch vụ với các hạn mức khác nhau.", "operationId": "updateUserBankPackageV1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEbankingPackageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/users/update": {"put": {"tags": ["User command resource"], "summary": "API cập nhật thông tin người dùng", "description": "Sử dụng để cập nhật thông tin người dùng.", "operationId": "updateUserInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}}}}, "/users/updateAddress": {"put": {"tags": ["User command resource"], "summary": "API cập nhật thông tin địa chỉ người dùng", "description": "Sử dụng để cập nhật thông tin địa chỉ người dùng.", "operationId": "updateUserAddress", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateInfoResponse"}}}}}}}, "/users/setting/notification": {"get": {"tags": ["User notification setting resource"], "operationId": "getNotificationSetting", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserNotificationSettingResponse"}}}}}}, "put": {"tags": ["User notification setting resource"], "operationId": "updateNotification", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettingRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserNotificationSettingResponse"}}}}}}, "post": {"tags": ["User notification setting resource"], "summary": "Thay đổi trạng thái đăng kí nhận notification của người dùng", "description": "Sử dụng api này để thay đổi thông tin đăng kí notification cho người dùng", "operationId": "updateNotificationSetting", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettingRequest"}}}, "required": true}, "responses": {"2000000": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}, "examples": {"success": {"description": "success", "value": {"success": true, "code": 2000000, "data": null, "message": "<PERSON><PERSON><PERSON><PERSON> hiện thành công"}}}}}}}}}, "/users/password/change/{username}": {"put": {"tags": ["User command resource"], "summary": "<PERSON> đổi mật kh<PERSON>u", "description": "Sử dụng để thay đổi mật khẩu. <PERSON><PERSON>t khẩu tối thiểu 8 ký tự, có ít nhất 1 ký tự hoa/ thường/ đặc biệt.", "operationId": "changePassword", "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChangeRequest"}}}, "required": true}, "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/bank-info/ebank-package": {"put": {"tags": ["User bank info resource"], "summary": "API cập nhật gói ebanking c<PERSON><PERSON> kh<PERSON>ch hàng.", "description": "Sử dụng để cho pháp kh<PERSON>ch hàng update thông gói dịch vụ với các hạn mức khác nhau.", "operationId": "updateUserBankPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEbankingPackageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/users/app-settings/login-by-biometrics-setting": {"get": {"tags": ["User App Setting resource"], "summary": "API lấy thông tin cài đặt đăng nhập bằng sinh trắc học.", "description": "API lấy thông tin cài đặt đăng nhập bằng sinh trắc học.", "operationId": "getLoginByBiometricsSetting", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseLoginByBiometricsSettingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}, "put": {"tags": ["User App Setting resource"], "summary": "API cập nhật thông tin cài đặt đăng nhập bằng sinh trắc học.", "description": "API cập nhật thông tin cài đặt đăng nhập bằng sinh trắc học.", "operationId": "updateLoginByBiometricsSetting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLoginByBiometricsSettingRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateLoginByBiometricsSettingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/users/app-settings/authen-trans-by-biometrics-setting": {"get": {"tags": ["User App Setting resource"], "summary": "API lấy thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.", "description": "API lấy thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.", "operationId": "getAuthenTransByBiometricsSetting", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAuthenTransByBiometricsSettingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}, "put": {"tags": ["User App Setting resource"], "summary": "API cập nhật thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.", "description": "API cập nhật thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.", "operationId": "updateAuthenTransByBiometricsSetting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAuthenTransByBiometricsSettingRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateAuthenTransByBiometricsSettingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/devices/unlink": {"put": {"tags": ["Device Info resource"], "summary": "API hủy liên kết thiết bị.", "description": "<PERSON><PERSON><PERSON> để lấy hủy liên kết thiết bị.", "operationId": "unlinkDevice", "parameters": [{"name": "udid", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/users/vnpost/customer/register": {"post": {"tags": ["Đăng ký user qua VNPOST"], "summary": "API đăng ký cho user thông thường từ VNP", "description": "Sử dụng để đăng ký cho user từ VNP", "operationId": "createUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserFromVNPRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFromVNPostResponse"}}}}}}}, "/users/vnpost/checkUser": {"post": {"tags": ["Đăng ký user qua VNPOST"], "summary": "API kiểm tra thông tin tạo mới user", "description": "Sử dụng để tạo mới 1 user. <PERSON><PERSON><PERSON> vào là tên đăng nhập mong muốn (sử dụng số điện thoại/ email), <PERSON><PERSON>t khẩu tối thiểu 8 ký tự, có ít nhất 1 ký tự hoa/ thường/ đặc biệt.", "operationId": "checkUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckUserProfileExistRequest"}}}, "required": true}, "responses": {"201": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserProfileExistResponse"}}}}, "409": {"description": "User existed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserProfileExistResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserProfileExistResponse"}}}}}}}, "/users/v1/klbCancelMobileVNP": {"post": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> dịch v<PERSON> trên app <PERSON>en long cũ", "description": "<PERSON><PERSON><PERSON> dịch v<PERSON> trên app <PERSON>en long cũ", "operationId": "klbCancelMobileVNP", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/v1/create": {"post": {"tags": ["User command resource"], "summary": "API Tạo mới user", "description": "Sử dụng để tạo mới 1 user. <PERSON><PERSON><PERSON> vào là tên đăng nhập mong muốn (sử dụng số điện thoại/ email), <PERSON><PERSON>t khẩu tối thiểu 8 ký tự, có ít nhất 1 ký tự hoa/ thường/ đặc biệt.", "operationId": "createNewUserV1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationV1Request"}}}, "required": true}, "responses": {"201": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}, "409": {"description": "User existed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}}, "security": []}}, "/users/v1/confirmQuickVerify": {"post": {"tags": ["API Xác thực nhanh qua KLB"], "summary": "API tạo yêu cầu xác thực nhanh", "operationId": "confirmQuickVerify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmQuickVerifyRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateQuickVerifyResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateQuickVerifyResponse"}}}}}}}, "/users/update/important-request": {"post": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> c<PERSON>u thay đổi thông tin quan trong", "description": "Su dung de yêu cầu thay đổi thông tin quan trong", "operationId": "verifyImportantInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyImportantInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/update-info": {"post": {"tags": ["User command resource"], "summary": "[Thay đổi thông tin người dùng] - 2. <PERSON><PERSON><PERSON><PERSON> vào thông tin cần thay đổi", "description": "Nhập vào thay đổi sđt hoặc email, nếu chỉ thay đổi số điện thoại => api tiếp là gọi api số 3 là đã đổi thành công => đến màn hình kết thúc, nếu chỉ thay đổi email => api tiếp là api số 4 là đổi thành công => kết thúc. Nếu đổi cả số điện thoại và email hoặc đổi số điện thoại và email chưa được verified thì phải gọi qua cả api số 3 và 4 thì mới đến màn hình kết thúc. Nếu nhập trùng sđt/ email với một số khác trên hệ thống thì sẽ báo lỗi.", "operationId": "updateUserInfo_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateUserInfoResponse"}}}}}}}, "/users/update-info/verify-sms-otp": {"post": {"tags": ["User command resource"], "summary": "[Thay đổi thông tin người dùng] - 3. <PERSON>erify SMS OTP", "description": "Verify sms OTP", "operationId": "verifySmsOtpToUpdateInfo", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifySmSOtpToUpdateUserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/update-info/verify-password": {"post": {"tags": ["User command resource"], "summary": "[Thay đổi thông tin người dùng] - 1. <PERSON><PERSON><PERSON> <PERSON> mật kh<PERSON>u", "description": "<PERSON><PERSON><PERSON> nhận mật khẩu trước khi tiến hành thay đổi thông tin người dùng, api này trả về một transactionId dùng để theo dõi transaction và truyền vào cho các API tiếp theo trong cùng một transaction", "operationId": "verifyPasswordToUpdateInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyPasswordResponse"}}}}}}}, "/users/update-info/verify-email-otp": {"post": {"tags": ["User command resource"], "summary": "[Thay đổi thông tin người dùng] - 4. <PERSON><PERSON>fy SMS OTP", "description": "Verify email OTP", "operationId": "verifyEmailOtpToUpdateInfo", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEmailOtpToUpdateInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/soft-otp/verify": {"post": {"tags": ["User command resource"], "summary": "API xác thực OTP", "description": "Sử dụng để xác thực <PERSON>", "operationId": "verifySoftOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}, "required": true}, "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/soft-otp/verify-advance-otp": {"post": {"tags": ["User command resource"], "summary": "API xác thực OTP", "description": "Sử dụng để xác thực <PERSON>", "operationId": "verifyAdvanceSoftOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvanceOTPRequest"}}}, "required": true}, "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/soft-otp/remove": {"post": {"tags": ["User command resource"], "summary": "Hủy đăng kí soft OTP", "description": "Sử dụng api này để hủy đăng ký etoken, sau khi hủy đăng kí thì không thể sử dụng etoken trên thiết bị này đư<PERSON><PERSON> nữa", "operationId": "deActiveSoftOtp", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}, "required": true}, "responses": {"2000000": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}, "examples": {"success": {"description": "success", "value": {"success": true, "code": 2000000, "data": null, "message": "<PERSON><PERSON><PERSON><PERSON> hiện thành công"}}}}}}, "4000001": {"description": "<PERSON><PERSON>ập sai mã soft otp để verify", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}, "examples": {"invalid otp": {"description": "invalid otp", "value": {"success": true, "code": 4000001, "data": null, "message": "Invalid OTP"}}}}}}, "4230001": {"description": "<PERSON><PERSON><PERSON> năng OTP với người dùng này đang bị khóa", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}, "examples": {"otp function locked": {"description": "otp function locked", "value": {"success": true, "code": 4230001, "data": null, "message": "OTP for this user is locked"}}}}}}}}}, "/users/soft-otp/register": {"post": {"tags": ["User command resource"], "operationId": "registerSoftOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}, "required": true}, "responses": {"201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOTPToken"}}}}}}}, "/users/soft-otp/pin-verified": {"post": {"tags": ["User command resource"], "operationId": "pinEtokenVerify", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOTPToken"}}}}}}}, "/users/soft-otp/etoken-status": {"post": {"tags": ["User command resource"], "summary": "API lấy trạng thái của ETOKEN", "description": "Mã code 4010004 => đã đăng kí trên thiết bị khác => show luồng cài đặt lại etoken.Mã code 2000000 => trả về trạng thái active hoặc unactive", "operationId": "getEtokenStatus", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEtokenStatus"}}}}}}}, "/users/setting/theme/schedule": {"post": {"tags": ["User Setting resource"], "summary": "Update schedule cho một theme, đc gọi bởi CMS", "operationId": "updateThemeSchedule", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateThemeScheduleRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateThemeScheduleResponse"}}}}}}}, "/users/setting/sync": {"get": {"tags": ["User Setting resource"], "operationId": "getSetting", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseMapStringObject"}}}}}}, "post": {"tags": ["User Setting resource"], "operationId": "syncSetting", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}}}}, "/users/register/verify": {"post": {"tags": ["User command resource"], "summary": "Verify to register user", "description": "Use this endpoint to verify to register user", "operationId": "verifyUserIdentity", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdentityRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/register/v1/verify": {"post": {"tags": ["User command resource"], "summary": "Verify to register user", "description": "Use this endpoint to verify to register user", "operationId": "verifyUserIdentityV1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdentityRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdentityResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdentityResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdentityResponse"}}}}}, "security": []}}, "/users/register/confirm": {"post": {"tags": ["User command resource"], "summary": "Verify to register user", "description": "Use this endpoint to verify to register user", "operationId": "confirmUserIdentity", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOtpRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/refresh-token/{refreshToken}": {"post": {"tags": ["User command resource"], "summary": "API lấy lại token mới", "description": "Sử dụng refresh_token để để lấy lại access_token mới khi access_token cũ đã hết hạn", "operationId": "refreshToken", "parameters": [{"name": "refreshToken", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}}, "security": []}}, "/users/re-install/etoken": {"post": {"tags": ["User command resource"], "summary": "[<PERSON><PERSON><PERSON> khi đã cài trên thiế<PERSON> bị k<PERSON>] - 4. <PERSON><PERSON> tr<PERSON> về secret mới", "description": "<PERSON><PERSON> <PERSON>hi nhập mã PIN xong thì api này trả về secret mới cho mobile lưu lại", "operationId": "reInstallSoftOTP", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReinstallEtokenRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOTPToken"}}}}}}}, "/users/re-install/etoken/verify-user-info": {"post": {"tags": ["User command resource"], "summary": "[<PERSON><PERSON><PERSON> khi đã cài trên thiết bị kh<PERSON>c] - 2. <PERSON><PERSON><PERSON> tra thông tin thẻ của người dùng", "description": "<PERSON><PERSON><PERSON> tra thông tin thẻ của người dùng của user để lấy lại etoken", "operationId": "verifyUserInfoToResetEtoken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyUserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/re-install/etoken/verify-sms-otp": {"post": {"tags": ["User command resource"], "summary": "[<PERSON><PERSON><PERSON> khi đã cài trên thiết bị kh<PERSON>c] - 3. Verify SMS OTP", "description": "Verify SMS OTP để lấy lại etoken", "operationId": "verifySmsOtpToResetEtoken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpToResetEtokenRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/re-install/etoken/verify-password": {"post": {"tags": ["User command resource"], "summary": "[<PERSON><PERSON><PERSON> khi đã cài trên thiết bị kh<PERSON>c] - 1. <PERSON><PERSON><PERSON> tra mật khẩu", "description": "<PERSON><PERSON><PERSON> tra mật khẩu của user để lấy lại etoken. Response tr<PERSON> về transactionId, sử dụng nó cho các api tiếp theo", "operationId": "verifyPasswordToResetEtoken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyPasswordToResetEtokenResponse"}}}}}}}, "/users/password/verify": {"post": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> thực mật kh<PERSON>u", "description": "Sử dụng để xác thực mật khẩu", "operationId": "isPasswordCorrect", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/password/v2/verify": {"post": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> thực mật kh<PERSON>u", "description": "Sử dụng để xác thực mật khẩu", "operationId": "isPasswordCorrectV2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/password/v1/verify": {"post": {"tags": ["User command resource"], "summary": "<PERSON><PERSON><PERSON> thực mật kh<PERSON>u", "description": "Sử dụng để xác thực mật khẩu", "operationId": "isPasswordCorrectV1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/password/forgot/request": {"post": {"tags": ["User command resource"], "summary": "<PERSON> quên mật khẩu", "description": "Sử dụng để lấy lại mật khẩu. <PERSON><PERSON>t khẩu tối thiểu 8 ký tự, có ít nhất 1 ký tự hoa/ thường/ đặc biệt.", "operationId": "forgotPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}}, "security": []}}, "/users/password/forgot/new-password": {"post": {"tags": ["User command resource"], "operationId": "recoverNewPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecoverPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/otp/verify": {"post": {"tags": ["User command resource"], "summary": "API xác thực OTP", "description": "Sử dụng để xác thực <PERSON>", "operationId": "verifyOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpForUnActiveUserRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/otp/generate": {"post": {"tags": ["User command resource"], "summary": "API gửi tao mã OTP", "operationId": "generateOtp", "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateOtpRequest"}}}, "required": true}, "responses": {"201": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/otp-for-auto-test/generate": {"post": {"tags": ["User command resource"], "summary": "API gửi tao mã dành cho auto test", "operationId": "generateOtpForTest", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateOtpRequest"}}}, "required": true}, "responses": {"201": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseString"}}}}}, "security": []}}, "/users/open/v1/login": {"post": {"tags": ["User command resource"], "summary": "API đăng nhập", "description": "Sử dụng để đăng nhập vào hệ thống. Thông tin trả về bao gồm access token và refresh token", "operationId": "loginOpenApi", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}}, "security": []}}, "/users/notificationInfo": {"post": {"tags": ["User command resource"], "summary": "API update thông tin user", "description": "Sử dụng update thông tin playerId sau khi playerId đã subscribe thành công", "operationId": "updateNotificationInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationInfoRequest"}}}, "required": true}, "responses": {"2000000": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}, "examples": {"success": {"description": "success", "value": {"success": true, "code": 2000000, "data": null, "message": "<PERSON><PERSON><PERSON><PERSON> hiện thành công"}}}}}}}}}, "/users/login": {"post": {"tags": ["User command resource"], "summary": "API đăng nhập", "description": "Sử dụng để đăng nhập vào hệ thống. Thông tin trả về bao gồm access token và refresh token", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}}, "security": []}}, "/users/internal/v1/getListUserInfoByListUserId": {"post": {"tags": ["User query resource"], "summary": "API lấy danh sách UserInfo bằng danh sách userId", "description": "API lấy danh sách UserInfo bằng danh sách userId", "operationId": "getListUserInfoByListUserId", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListUserInfoByListUserIdRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListUserInfoByListUserIdResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListUserInfoByListUserIdResponse"}}}}}}}, "/users/internal/v1/getListUserInfoByListUserIdAndBranch": {"post": {"tags": ["User query resource"], "summary": "API lấy danh sách UserInfo bằng danh sách userId", "description": "API lấy danh sách UserInfo bằng danh sách userId", "operationId": "getListUserInfoByListUserIdAndBranch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListUserInfoByListUserIdAndBranchRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListUserInfoByListUserIdResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListUserInfoByListUserIdResponse"}}}}}}}, "/users/external/v1/verifyOtpAndGetUserInfo": {"post": {"tags": ["User query resource"], "summary": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "description": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "operationId": "verifyOtpAndGetUserInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpAndGetUserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyOtpAndGetUserInfoResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyOtpAndGetUserInfoResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyOtpAndGetUserInfoResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyOtpAndGetUserInfoResponse"}}}}}, "security": []}}, "/users/external/v1/getUserKlbInfo": {"post": {"tags": ["API Xác thực nhanh qua KLB"], "summary": "API tạo tìm kiếm yêu cầu xác thực n<PERSON>h", "operationId": "getUserKlbInfo", "parameters": [{"name": "identity", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}}, "security": []}}, "/users/external/v1/getQuickVerifyByIdentity": {"post": {"tags": ["API Xác thực nhanh qua KLB"], "summary": "API tạo tìm kiếm yêu cầu xác thực n<PERSON>h", "operationId": "getQuickVerifyByIdentity", "parameters": [{"name": "identity", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}}, "security": []}}, "/users/external/v1/createQuickVerify": {"post": {"tags": ["API Xác thực nhanh qua KLB"], "summary": "API tạo yêu cầu xác thực nhanh", "operationId": "createQuickVerify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuickVerifyRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateQuickVerifyResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateQuickVerifyResponse"}}}}}, "security": []}}, "/users/external/v1/checkUserKlb": {"post": {"tags": ["User query resource"], "summary": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "description": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "operationId": "checkUserKlb", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckUserKlbRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserKlbResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserKlbResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserKlbResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserKlbResponse"}}}}}, "security": []}}, "/users/external/v1/checkUserAndSendOtp": {"post": {"tags": ["User query resource"], "summary": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "description": "API lấy kiểm ta xem user đã có tài khoản klb chưa", "operationId": "checkUserAndSendOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckUserAndSendOtpRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserAndSendOtpResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserAndSendOtpResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserAndSendOtpResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserAndSendOtpResponse"}}}}}, "security": []}}, "/users/ekyc/verifyIdCardSide": {"post": {"tags": ["User command resource"], "summary": "VERIFY ID CARD SIDE", "description": "Use this endpoint to verify id card side", "operationId": "verifyIdCardSide", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdCardSideRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSideResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSideResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSideResponse"}}}}}}}, "/users/ekyc/verify-live-ness": {"post": {"tags": ["User command resource"], "summary": "Verify live ness by ekyc", "description": "Use this endpoint to verify live ness by ekyc", "operationId": "verifyLiveNessByEkyc", "parameters": [{"name": "userIdentity", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/ekyc/verify-id-card": {"post": {"tags": ["User command resource"], "summary": "Save face id by ekyc", "description": "Use this endpoint to verify face id by ekyc", "operationId": "verifyIdCardWithFaceId", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdCardFaceIdRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}}, "security": []}}, "/users/ekyc/v1/save-id-card": {"post": {"tags": ["User command resource"], "summary": "Verify id card by ekyc", "description": "Use this endpoint to verify id card by ekyc", "operationId": "saveAndReadIdCardByEkycV1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEkycV1Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/ekyc/update-ocr": {"post": {"tags": ["User command resource"], "summary": "Update info by user", "description": "Use this endpoint to update info by user", "operationId": "updateOcrByUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOcrRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOcrResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOcrResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOcrResponse"}}}}}, "security": []}}, "/users/ekyc/start-live-ness": {"post": {"tags": ["User command resource"], "summary": "Start live ness", "description": "Use this endpoint to Start live ness", "operationId": "startLiveNess", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartLiveNessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartLiveNessResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartLiveNessResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartLiveNessResponse"}}}}}, "security": []}}, "/users/ekyc/save-live-ness": {"post": {"tags": ["User command resource"], "summary": "Save live ness", "description": "Use this endpoint to Start live ness", "operationId": "saveLiveNess", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveLiveNessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}}, "security": []}}, "/users/ekyc/save-id-card": {"post": {"tags": ["User command resource"], "summary": "Verify id card by ekyc", "description": "Use this endpoint to verify id card by ekyc", "operationId": "saveAndReadIdCardByEkyc", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEkycRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/ekyc/save-face-id": {"post": {"tags": ["User command resource"], "summary": "Save face id by ekyc", "description": "Use this endpoint to verify face id by ekyc", "operationId": "saveFaceId", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEkycRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}, "security": []}}, "/users/ekyc/result/{userIdentity}": {"post": {"tags": ["User command resource"], "summary": "Final result for ekyc", "description": "Use this endpoint to get Final result for ekyc", "operationId": "getFinalEkycResult", "parameters": [{"name": "userIdentity", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycFinalResponse"}}}}}, "security": []}}, "/users/ekyc/profileLiveness/verify-live-ness": {"post": {"tags": ["User command resource"], "summary": "VERIFY PROFILE LIVENESS", "description": "Use this endpoint to Start live ness", "operationId": "verifyProfileLiveNess", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyProfileLivenessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessResponse"}}}}}, "security": []}}, "/users/ekyc/profileLiveness/start-live-ness": {"post": {"tags": ["User command resource"], "summary": "START PROFILE LIVENESS", "description": "Use this endpoint to Start live ness", "operationId": "startLiveNess_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartProfileLivenessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLivenessResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLivenessResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLivenessResponse"}}}}}, "security": []}}, "/users/ekyc/profileLiveness/save-video-liveness": {"post": {"tags": ["User command resource"], "summary": "SAVE VIDEO PROFILE LIVENESS", "description": "Use this endpoint to save video live ness", "operationId": "saveVideoProfileLiveness", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveVideoEKYCProfileLivenessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLivenessResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLivenessResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLivenessResponse"}}}}}, "security": []}}, "/users/ekyc/profileLiveness/save-live-ness": {"post": {"tags": ["User command resource"], "summary": "SAVE PROFILE LIVENESS", "description": "Use this endpoint to Start live ness", "operationId": "saveProfileLiveNess", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveProfileLivenessRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessResponse"}}}}}, "security": []}}, "/users/ekyc/getLivenessType": {"post": {"tags": ["User command resource"], "summary": "GET LIVENESS TYPE", "description": "Lấy livenessType: FACE or NOSE", "operationId": "getLivenessType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLivenessTypeRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetLivenessTypeResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetLivenessTypeResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetLivenessTypeResponse"}}}}}, "security": []}}, "/users/create": {"post": {"tags": ["User command resource"], "summary": "API Tạo mới user", "description": "Sử dụng để tạo mới 1 user. <PERSON><PERSON><PERSON> vào là tên đăng nhập mong muốn (sử dụng số điện thoại/ email), <PERSON><PERSON>t khẩu tối thiểu 8 ký tự, có ít nhất 1 ký tự hoa/ thường/ đặc biệt.", "operationId": "createNewUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationRequest"}}}, "required": true}, "responses": {"201": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}, "409": {"description": "User existed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFinalResponse"}}}}}, "security": []}}, "/users/2345/v1/verifyIdCardSide": {"post": {"tags": ["User command resource"], "summary": "[2345] VERIFY ID CARD SIDE", "description": "Use this endpoint to verify id card side", "operationId": "verifyIdCardSide_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdCardSide2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSide2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSide2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardSide2345Response"}}}}}}}, "/users/2345/v1/verify-id-card": {"post": {"tags": ["User command resource"], "summary": "[2345] VERIFY ID CARD", "description": "Use this endpoint to verify face id by ekyc", "operationId": "verifyIdCardWithFaceId2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyIdCardFaceId2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardFaceId2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardFaceId2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyIdCardFaceId2345Response"}}}}}}}, "/users/2345/v1/update-ocr": {"post": {"tags": ["User command resource"], "summary": "[2345] Update info by mobile NFC", "description": "Update info by mobile NFC", "operationId": "updateOcrByNFC2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOCRByNFC2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOCRByNFC2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOCRByNFC2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOCRByNFC2345Response"}}}}}}}, "/users/2345/v1/save-id-card": {"post": {"tags": ["User command resource"], "summary": "[2345] API save id card", "description": "[2345] API save id card", "operationId": "saveAndReadIdCardBy2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveAndReadIdCard2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/2345/v1/profileLiveness/verify-live-ness": {"post": {"tags": ["User command resource"], "summary": "[2345] VERIFY PROFILE LIVENESS", "description": "Use this endpoint to Start live ness", "operationId": "verifyProfileLiveNess2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyProfileLiveness2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLiveness2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLiveness2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLiveness2345Response"}}}}}}}, "/users/2345/v1/profileLiveness/start-live-ness": {"post": {"tags": ["User command resource"], "summary": "[2345] START PROFILE LIVENESS", "description": "[2345] Use this endpoint to Start live ness", "operationId": "startProfileLiveNess2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartProfileLiveness2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}}}}, "/users/2345/v1/profileLiveness/save-video-liveness": {"post": {"tags": ["User command resource"], "summary": "[2345] SAVE VIDEO PROFILE LIVENESS", "description": "Use this endpoint to save video live ness", "operationId": "saveVideoProfileLiveness2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveVideoEKYCProfileLiveness2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLiveness2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLiveness2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoEKYCProfileLiveness2345Response"}}}}}}}, "/users/2345/v1/profileLiveness/save-live-ness": {"post": {"tags": ["User command resource"], "summary": "[2345] SAVE PROFILE LIVENESS", "description": " Use this endpoint to save live ness", "operationId": "saveProfileLiveNess2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveProfileLiveness2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLiveness2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLiveness2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLiveness2345Response"}}}}}}}, "/users/2345/v1/finish-verify": {"post": {"tags": ["User command resource"], "summary": "[2345] <PERSON><PERSON><PERSON><PERSON> verify 2345, đ<PERSON><PERSON> dữ liệu vào rà soát", "description": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON> verify 2345, đ<PERSON><PERSON> dữ liệu vào rà soát", "operationId": "finishVerify2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinishVerify2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseFinishVerify2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseFinishVerify2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseFinishVerify2345Response"}}}}}}}, "/users/2345/v1/compare-customer-face": {"post": {"tags": ["User command resource"], "summary": "[2345] So s<PERSON><PERSON> face KH", "description": "[2345] So s<PERSON><PERSON> face KH", "operationId": "compareCustomerFace2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompareCustomerFace2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCompareCustomerFace2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCompareCustomerFace2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCompareCustomerFace2345Response"}}}}}}}, "/users/2345/transaction/v1/profileLiveness/verify-live-ness": {"post": {"tags": ["User command resource"], "summary": "[MOBILE_CALL] 3. VERIFY PROFILE LIVENESS TRANSACTION", "description": "API Sử dung để add frames khi KH xác thực STH trong quá trình giao dịch", "operationId": "verifyProfileLiveNessTransaction2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyProfileLivenessTransaction2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessTransaction2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessTransaction2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyProfileLivenessTransaction2345Response"}}}}}}}, "/users/2345/transaction/v1/profileLiveness/start-live-ness": {"post": {"tags": ["User command resource"], "summary": "[2345] START PROFILE LIVENESS", "description": "[2345] Use this endpoint to Start live ness", "operationId": "startProfileLiveNessTransaction2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartProfileLivenessTransaction2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartProfileLiveness2345Response"}}}}}}}, "/users/2345/transaction/v1/profileLiveness/save-video-liveness": {"post": {"tags": ["User command resource"], "summary": "[MOBILE_CALL] 4. SAVE VIDEO PROFILE LIVENESS TRANSACTION", "description": "API Sử dung để save video liveness khi KH xác thực STH trong quá trình giao dịch", "operationId": "saveVideoProfileLiveNessTransaction2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveVideoProfileLivenessTransaction2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoProfileLivenessTransaction2345Request"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoProfileLivenessTransaction2345Request"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveVideoProfileLivenessTransaction2345Request"}}}}}}}, "/users/2345/transaction/v1/profileLiveness/save-live-ness": {"post": {"tags": ["User command resource"], "summary": "[MOBILE_CALL] 2. SAVE PROFILE LIVENESS TRANSACTION", "description": "API Sử dung để add frames khi KH xác thực STH trong quá trình giao dịch", "operationId": "saveProfileLiveNessTransaction2345", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveProfileLivenessTransaction2345Request"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessTransaction2345Response"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessTransaction2345Response"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveProfileLivenessTransaction2345Response"}}}}}}}, "/stm/users/customer/validate": {"post": {"tags": ["API dành cho các user từ máy STM"], "summary": "API kiểm tra sự tồn tại của User trước khi mở tài khoản", "description": "Sử dụng để đăng ký cho user từ STM", "operationId": "validateUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateUserRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/stm/users/customer/register": {"post": {"tags": ["API dành cho các user từ máy STM"], "summary": "API đăng ký cho user thông thường từ máy STM", "description": "Sử dụng để đăng ký cho user từ STM", "operationId": "createUser_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserFromSTMRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateUserFromSTMResponse"}}}}}}}, "/saleAppCollab/v1/register": {"post": {"tags": ["sale-app-collaborator-controller"], "summary": "API Tạo hồ sơ đăng ký CTV", "description": "API tạo hồ sơ đăng ký CTV", "operationId": "registerSaleAppCollab", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterSaleAppCollabRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterSaleAppCollabResponse"}}}}}}}, "/notifications": {"post": {"tags": ["notification-controller"], "operationId": "sendNotification", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateNotificationResponse"}}}}}}}, "/migration/v1/user-crm": {"post": {"tags": ["migration-controller"], "operationId": "migrateUserCrm", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseMigrateUserCrmResponse"}}}}}}}, "/migration/v1/old-customer": {"post": {"tags": ["migration-controller"], "operationId": "migrateCustomerFromCore", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseMigrateCustomerFromOldSysResponse"}}}}}}}, "/migration/v1/ksf-customer": {"post": {"tags": ["migration-controller"], "operationId": "migrateKsfCustomer", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseMigrateKsfCustomerResponse"}}}}}}}, "/api/v1/liveness/transaction/sessions": {"post": {"tags": ["Liveness resource"], "summary": "Create liveness session", "description": "Create a new liveness detection session", "operationId": "createTransactionLivenessSession", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTransactionLivenessSessionRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateTransactionLivenessSessionResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateTransactionLivenessSessionResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateTransactionLivenessSessionResponse"}}}}}, "security": []}}, "/api/terms/v1/create": {"post": {"tags": ["terms-controller"], "operationId": "createTerms", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTermsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/admin/users/customer/resetPassword": {"post": {"tags": ["Resource for manage bank's user"], "summary": "API để reset password", "description": "Sử dụng để reset password", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPasswordChangeRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/admin/users/customer/register": {"post": {"tags": ["Resource for manage bank's user"], "summary": "API đăng ký cho user đã có CIF", "description": "Sử dụng để đăng ký cho user đã có CIF", "operationId": "registerCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterCifCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/admin/users/customer/open": {"post": {"tags": ["Resource for manage bank's user"], "summary": "API kich hoat lai dịch vụ ebanking", "description": "Sử dụng để kich hoat lai dịch vụ ebanking", "operationId": "openCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/admin/users/customer/close": {"post": {"tags": ["Resource for manage bank's user"], "summary": "API hủy dịch vụ ebanking", "description": "Sử dụng để hủy dịch vụ ebanking", "operationId": "closeCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CloseCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/address/branch/getBranches": {"post": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách chi nhánh", "description": "Sử dụng để tra cứu thông tin danh sách chi nhánh\n\nThông tin trả về danh sách gồm: \n\nbranchCode: Mã chi nhánh\n\nbranchName: Tên chi nhánh\n\ndistrictCode: Mã huyện/ thị xã\n\naddress: Địa chỉ chi nhánh", "operationId": "getBranches", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBranchRequest"}}}, "required": true}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}}, "security": []}}, "/users/v2/detail": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin cho tiết người dùng", "description": "Dùng để lấy thông tin chi tiết của người dùng đnagư nhập", "operationId": "getUserInfoV2", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV2"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV2"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV2"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV2"}}}}}}}, "/users/v1/getQuickVerifyByUserId": {"get": {"tags": ["API Xác thực nhanh qua KLB"], "summary": "API tạo tìm kiếm yêu cầu xác thực n<PERSON>h", "operationId": "getQuickVerifyByUserId", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQuickVerifyByIdentityResponse"}}}}}}}, "/users/v1/getEkycStatus": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin trạng thái ekyc", "description": "API lấy thông tin trạng thái ekyc", "operationId": "getEkycStatus", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/users/v1/detail": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin cho tiết người dùng", "description": "Dùng để lấy thông tin chi tiết của người dùng đnagư nhập", "operationId": "getUserInfoV1", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV1"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV1"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV1"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponseV1"}}}}}}}, "/users/v1/branchOpenCif": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin chi nhánh đăng ký dịch vụ ngân hàng điện tử", "description": "Dùng để lấy thông tin chi tiết chi nhánh ngân hàng điện tử của người dùng đăng nhập", "operationId": "getUserBranch", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserBranchResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserBranchResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserBranchResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserBranchResponse"}}}}}}}, "/users/setting/theme": {"get": {"tags": ["User Setting resource"], "summary": "<PERSON><PERSON><PERSON> danh sách tất cả các theme của App KienLongBank Plus", "operationId": "getListTheme", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListThemeResponse"}}}}}}}, "/users/setting/theme/detail": {"get": {"tags": ["User Setting resource"], "summary": "L<PERSON>y một theme chỉ định của App KienLongBank Plus", "operationId": "getThemeDetail", "parameters": [{"name": "id", "in": "query", "description": "theme id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetThemeDetailResponse"}}}}}}}, "/users/open/v1/detail": {"get": {"tags": ["User query resource"], "operationId": "getUserInfoOpen", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserKlbInfoResponse"}}}}}}}, "/users/internal/v1/getUserInfoByFaceId": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin user không ở trạng thái khóa hoặc hủy bằng FaceId", "description": "PI lấy thông tin user không ở trạng thái khóa hoặc hủy bằng FaceId", "operationId": "getUserInfoByFaceId", "parameters": [{"name": "faceId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}}}}, "/users/internal/v1/getUserInfoByCifNo": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin user không ở trạng thái khóa hoặc hủy bằng số cif", "description": "PI lấy thông tin user không ở trạng thái khóa hoặc hủy bằng số cif", "operationId": "getUserInfoByCif", "parameters": [{"name": "cifNo", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUserInfoByCifResponse"}}}}}}}, "/users/internal/v1/getListUserId": {"get": {"tags": ["User query resource"], "operationId": "getListUserIds", "parameters": [{"name": "branchId", "in": "query", "description": "Mã chi nh<PERSON>h", "required": false, "schema": {"type": "string"}}, {"name": "userIds", "in": "query", "description": "<PERSON><PERSON> s<PERSON>ch <PERSON>d ng<PERSON> dùng", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "phoneNumber", "in": "query", "description": "SĐT chủ shop và nhân viên", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListUserIdResponse"}}}}}}}, "/users/get-by-birthday": {"get": {"tags": ["User query resource"], "summary": "API lấy danh sách cif có ngày sinh vào ngày sinh truyền vào", "description": "<PERSON><PERSON><PERSON> cho loyalty gọi nội bộ để thưởng điểm", "operationId": "getUserByBirthday", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "date", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBirthdayUserResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBirthdayUserResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBirthdayUserResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseBirthdayUserResponse"}}}}}}}, "/users/ekyc-status": {"get": {"tags": ["User bank info resource"], "summary": "API lấy thông tin trạng thái ekyc của khách hàng.", "description": "Sử dụng để lấy thông tin trạng thái ekyc của khách hàng.", "operationId": "getEkycStatus_1", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycStatusResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycStatusResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycStatusResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseEkycStatusResponse"}}}}}}}, "/users/detail": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin cho tiết người dùng", "description": "Dùng để lấy thông tin chi tiết của người dùng đnagư nhập", "operationId": "getUserInfo", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}}}}, "/users/detail/v1/{user_id}": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin cho tiết người dùng", "description": "Dùng để lấy thông tin chi tiết của người dùng đnagư nhập", "operationId": "getUserInfoByUserId", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseQueryInfoResponse"}}}}}}}, "/users/customer_rank": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin phân hạng của người dùng", "description": "Dùng để lấy thông tin phân hạng của người dùng", "operationId": "getCustomerRankInfo", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerRankResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerRankResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerRankResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerRankResponse"}}}}}}}, "/users/branch/{branchCode}": {"get": {"tags": ["User query resource"], "summary": "API lấy thông tin chi nhánh theo branch code", "description": "Dùng để lấy thông tin chi nhánh theo branch code", "operationId": "getBranchDetailByCode", "parameters": [{"name": "branchCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetBranchDetailByCodeResponse"}}}}, "4010002": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetBranchDetailByCodeResponse"}}}}, "4040007": {"description": "User does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetBranchDetailByCodeResponse"}}}}, "5000000": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetBranchDetailByCodeResponse"}}}}}}}, "/users/bank-info": {"get": {"tags": ["User bank info resource"], "summary": "API lấy thông tin người dùng liên kết tài khoản bank.", "description": "Sử dụng để lấy thông tin người dùng liên kết tài khoản bank.", "operationId": "getUserBankInfo", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserBankInfoResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserBankInfoResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserBankInfoResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserBankInfoResponse"}}}}}}}, "/users/bank-info/{cifNumber}": {"get": {"tags": ["User bank info resource"], "summary": "API lấy thông tin người dùng liên kết tài khoản bank thông qua số cif.", "description": "Sử dụng để lấy thông tin người dùng liên kết tài khoản bank cho các dịch vụ khác.", "operationId": "getUserBankInfoViaCif", "parameters": [{"name": "cifNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUserBankInfoResponse"}}}}}}}, "/users/2345/v1/checkNeedVerifyType": {"get": {"tags": ["User command resource"], "summary": "[2345] API KIỂM TRA KH CẦN UPDATE CCCD CHIP HAY FACE_ID", "description": "[2345] API KIỂM TRA KH CẦN UPDATE CCCD CHIP HAY FACE_ID", "operationId": "checkNeedVerifyType", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckNeedVerifyTypeResponse"}}}}, "400": {"description": "Bad request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckNeedVerifyTypeResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckNeedVerifyTypeResponse"}}}}}}}, "/notifications/internal/v1/getPlayerIdsByCifNo": {"get": {"tags": ["notification-controller"], "summary": "API lấy playerId của UserId bằng số cif", "description": "API lấy playerId của UserId bằng số cif", "operationId": "getPlayerIdsByCifNo", "parameters": [{"name": "cifNo", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetPlayerIdsByCifNoResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetPlayerIdsByCifNoResponse"}}}}}}}, "/notifications/cif/{cifNumber}": {"get": {"tags": ["notification-controller"], "operationId": "getNotification", "parameters": [{"name": "cifNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetNotificationInfoResponse"}}}}}}}, "/devices/detail": {"get": {"tags": ["Device Info resource"], "summary": "API lấy thông tin chi tiết thiết bị đăng nhập", "description": "<PERSON>ùng để lấy thông tin chi tiết thiết bị đăng nhập", "operationId": "getDeviceInfo", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDeviceInfoResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/customer-group": {"get": {"tags": ["customer-group-controller-impl"], "summary": "<PERSON><PERSON><PERSON> thông tin danh sách nhóm khách hàng", "operationId": "getCustomerGroupList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerGroupListResponse"}}}}}}}, "/customer-group-use/{customerGroupId}/customer-group": {"get": {"tags": ["customer-group-use-controller-impl"], "summary": "<PERSON><PERSON><PERSON> danh sách đối tượng sử dụng nhóm khách hàng", "operationId": "getCustomerGroupUseList", "parameters": [{"name": "customerGroupId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCustomerGroupUseListResponse"}}}}}}}, "/api/v1/liveness/transaction/{transactionNo}/sessions/{sessionId}/result": {"get": {"tags": ["Liveness resource"], "summary": "Get liveness transaction session result", "description": "Get the result of a transaction liveness detection session", "operationId": "getTransactionLivenessSessionResult", "parameters": [{"name": "transactionNo", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetTransactionLivenessSessionResultResponse"}}}}, "404": {"description": "Session not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetTransactionLivenessSessionResultResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseGetTransactionLivenessSessionResultResponse"}}}}}, "security": []}}, "/api/terms/v1/check": {"get": {"tags": ["terms-controller"], "operationId": "checkTermsAgreed", "parameters": [{"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBoolean"}}}}}}}, "/address/v1/wards/getWardsByProvinceCodeAndDistrictCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách Phường/ <PERSON><PERSON> theo mã tỉnh/ thành phố và quận/huyện", "description": "Sử dụng để tra cứu thông tin danh sách Phường/ Xã\n\nThông tin trả về danh sách gồm: \n\nwardCode: Mã Phường/ Xã\nwardNameVi: Tên <PERSON>ờ<PERSON>/ Xã bằng tiếng việt\n\nwardNameEn: Tên <PERSON>ờ<PERSON>/ Xã bằng tiếng anh\n\nprovinceCode: Mã tỉnh/ thành phố\n\ndistrictCode: Mã quận/ huyện pho", "operationId": "getWardsByProvinceCodeAndDistrictCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportWardResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportWardResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportWardResponse"}}}}}, "security": []}}, "/address/v1/wards/getWardsAndNetworkByProvinceCodeAndDistrictCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "operationId": "getWardsAndNetworkByProvinceCodeAndDistrictCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportWardNetworkResponse"}}}}}, "security": []}}, "/address/v1/wards/getNetworksByProvinceCodeAndDistrictCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "operationId": "getNetworksByProvinceCodeAndDistrictCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportNetworkResponse"}}}}}, "security": []}}, "/address/v1/province/getAllProvinces": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu toàn bộ danh sách các tỉnh", "description": "Sử dụng để tra cứu danh sách thông tin tỉnh theo mã. \n\nThông tin trả về danh sách gồm: \n\nprovinceCode: Mã tỉnh\n\nprovinceNameVn: Tên tỉnh bằng tiếng việt\n\nprovinceNameEn: Tên tỉnh bằng tiếng anh\n\ncoreProvinceCode: Mã tỉnh trong core", "operationId": "getAllProvinces", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}}, "security": []}}, "/address/v1/district/getDistrictsByProvinceCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách quận/ huyện/ thị xã theo mã tỉnh/thành phố", "description": "Sử dụng để tra cứu thông tin danh sách huyện/ thị xã theo mã. \n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictsByProvinceCode", "parameters": [{"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}}, "security": []}}, "/address/province/getProvincesWithBranchesByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách tỉnh có chi nhánh theo mã", "description": "Sử dụng để tra cứu thông tin danh sách tỉnh có chi nhánh theo mã. \n\nThông tin trả về danh sách gồm: \n\nprovinceCode: Mã tỉnh\n\nprovinceNameVn: Tên tỉnh bằng tiếng việt\n\nprovinceNameEn: Tên tỉnh bằng tiếng anh\n\ncoreProvinceCode: Mã tỉnh trong core", "operationId": "getProvincesWithBranchesByCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}}, "security": []}}, "/address/province/getProvincesByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu danh sách thông tin tỉnh theo mã", "description": "Sử dụng để tra cứu danh sách thông tin tỉnh theo mã. \n\nThông tin trả về danh sách gồm: \n\nprovinceCode: Mã tỉnh\n\nprovinceNameVn: Tên tỉnh bằng tiếng việt\n\nprovinceNameEn: Tên tỉnh bằng tiếng anh\n\ncoreProvinceCode: Mã tỉnh trong core", "operationId": "getProvincesByCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportProvinceResponse"}}}}}, "security": []}}, "/address/province/getProvinceWithBranchesByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin tỉnh có chi nhánh theo mã", "description": "Sử dụng để tra cứu thông tin tỉnh có chi nhánh theo mã. \n\nThông tin trả về gồm: \n\nprovinceCode: Mã tỉnh\n\nprovinceNameVn: Tên tỉnh bằng tiếng việt\n\nprovinceNameEn: Tên tỉnh bằng tiếng anh\n\ncoreProvinceCode: Mã tỉnh trong core", "operationId": "getProvinceWithBranchesByCode", "parameters": [{"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}}, "security": []}}, "/address/province/getProvinceByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin tỉnh theo mã", "description": "Sử dụng để tra cứu thông tin tỉnh theo mã. \n\nThông tin trả về gồm: \n\nprovinceCode: Mã tỉnh\n\nprovinceNameVn: Tên tỉnh bằng tiếng việt\n\nprovinceNameEn: Tên tỉnh bằng tiếng anh\n\ncoreProvinceCode: Mã tỉnh trong core", "operationId": "getProvinceByCode", "parameters": [{"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseProvinceResponse"}}}}}, "security": []}}, "/address/district/getDistrictsWithBranchesByProvinceCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã tỉnh", "description": "Sử dụng để tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã tỉnh\n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictsWithBranchesByProvinceCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "provinceCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}}, "security": []}}, "/address/district/getDistrictsWithBranchesByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã", "description": "Sử dụng để tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã. \n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictsWithBranchesByCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}}, "security": []}}, "/address/district/getDistrictsByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách huyện/ thị xã theo mã", "description": "Sử dụng để tra cứu thông tin danh sách huyện/ thị xã theo mã. \n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictsByCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportDistrictResponse"}}}}}, "security": []}}, "/address/district/getDistrictWithBranchesByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin huyện/ thị xã có chi nhánh theo mã", "description": "Sử dụng để tra cứu thông tin huyện/ thị xã có chi nhánh theo mã. \n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictWithBranchesByCode", "parameters": [{"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}}, "security": []}}, "/address/district/getDistrictByCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin huyện/ thị xã theo mã", "description": "Sử dụng để tra cứu thông tin huyện/ thị xã theo mã. \n\nThông tin trả về gồm: \n\ndistrictCode: Mã huyện\n\ndistrictNameVn: Tên huyện bằng tiếng việt\n\ndistrictNameEn: Tên huyện bằng tiếng anh\n\nprovinceCode: Mã tỉnh mà huyện trực thuộc", "operationId": "getDistrictByCode", "parameters": [{"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDistrictResponse"}}}}}, "security": []}}, "/address/branch/getBranchesByDistrictCode": {"get": {"tags": ["API địa chỉ các chi nh<PERSON>h"], "summary": "API tra cứu thông tin danh sách chi nh<PERSON>h theo theo mã huyện/ thị xã", "description": "Sử dụng để tra cứu thông tin danh sách chi nhánh theo theo mã huyện/ thị xã\n\nThông tin trả về danh sách gồm: \n\nbranchCode: Mã chi nhánh\n\nbranchName: Tên chi nhánh\n\ndistrictCode: Mã huyện/ thị xã\n\naddress: Địa chỉ chi nhánh", "operationId": "getBranchesByDistrictCode", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "districtCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}, "500": {"description": "Lỗi máy chủ", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportBranchResponse"}}}}}, "security": []}}, "/users/logout/current-device": {"delete": {"tags": ["User command resource"], "summary": "API đăng xuất trên thiết bị hiện tại", "description": "Sử dụng để đăng xuất trên thiết bị hiện tại", "operationId": "logoutSingleSession", "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/users/logout/all-devices": {"delete": {"tags": ["User command resource"], "summary": "API đăng xuất tất cả các thiết bị", "description": "Sử dụng để đăng xuất tất cả các thiết bị", "operationId": "logoutAllSession", "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}, "/users/deletePlayerId": {"delete": {"tags": ["User command resource"], "summary": "API xóa player<PERSON> khi ấn nút đăng xuất", "description": "<PERSON><PERSON> nhấn nút đăng xuất phải xóa playerID", "operationId": "deletePlayerId", "responses": {"204": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVoid"}}}}}}}}, "components": {"schemas": {"BaseResponseUpdateCustomerGroupUseResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateCustomerGroupUseResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateCustomerGroupUseResponse": {"type": "object"}, "UpdateEbankingPackageRequest": {"type": "object", "properties": {"ebankingTypeId": {"type": "string", "description": "Id g<PERSON><PERSON> dịch vụ khách hàng muốn cậ<PERSON>h<PERSON>, trừ gói EBANK5", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "description": "Mã otp", "nullable": true, "x-nullable": true}}}, "BaseResponseVoid": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "object", "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateInfoRequest": {"type": "object", "properties": {"avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "actionType": {"type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}, "updateInfo": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateInfoResponse": {"type": "object", "properties": {"loginType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "otpMethod": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "aliasName": {"type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "facebookLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "facebookId": {"type": "string", "nullable": true, "x-nullable": true}, "facebookName": {"type": "string", "nullable": true, "x-nullable": true}, "googleLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "googleId": {"type": "string", "nullable": true, "x-nullable": true}, "googleName": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "enabled2fa": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "UpdateUserAddressRequest": {"type": "object", "properties": {"provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}}}, "UserNotificationSettingRequest": {"type": "object", "properties": {"smsNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}, "emailNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}, "pushNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}, "systemNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}, "promotionNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseUserNotificationSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UserNotificationSettingResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UserNotificationSettingResponse": {"type": "object", "properties": {"smsNotificationEnable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> báo biến động số dư bằng SMS", "nullable": true, "x-nullable": true}, "emailNotificationEnable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> báo biến động số dư bằng email", "nullable": true, "x-nullable": true}, "pushNotificationEnable": {"type": "boolean", "description": "Thông báo biến động số dư bằng thông báo đẩy", "nullable": true, "x-nullable": true}, "systemNotificationEnable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống", "nullable": true, "x-nullable": true}, "promotionNotificationEnable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> báo chư<PERSON>ng trình khu<PERSON>ến mại", "nullable": true, "x-nullable": true}}}, "PasswordChangeRequest": {"type": "object", "properties": {"newPassword": {"type": "string", "nullable": true, "x-nullable": true}, "oldPassword": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseBoolean": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "boolean", "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateLoginByBiometricsSettingRequest": {"type": "object", "properties": {"enable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateLoginByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateLoginByBiometricsSettingResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateLoginByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "UpdateAuthenTransByBiometricsSettingRequest": {"type": "object", "properties": {"enable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateAuthenTransByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateAuthenTransByBiometricsSettingResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateAuthenTransByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CreateUserFromVNPRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "email": {"pattern": "(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))", "type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "dob": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "nationality": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssuePlc": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDt": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "permanentAddress": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddress": {"type": "string", "nullable": true, "x-nullable": true}, "district": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "province": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "job": {"type": "string", "nullable": true, "x-nullable": true}, "postalCode": {"type": "string", "nullable": true, "x-nullable": true}, "wrnMessage": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageUp": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageDown": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "identityType": {"type": "string", "nullable": true, "x-nullable": true}, "placeOfIssue": {"type": "string", "nullable": true, "x-nullable": true}, "sexCode": {"type": "string", "nullable": true, "x-nullable": true}, "groupCode": {"type": "string", "nullable": true, "x-nullable": true}, "channel": {"type": "string", "nullable": true, "x-nullable": true}, "ward_Vlg": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateUserFromVNPostResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateUserFromVNPostResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateUserFromVNPostResponse": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckUserProfileExistRequest": {"type": "object", "properties": {"phoneNumber": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"pattern": "(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))", "type": "string", "description": "Email", "example": "<EMAIL>", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckUserProfileExistResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckUserProfileExistResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckUserProfileExistResponse": {"type": "object", "properties": {"accountName": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNumber": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "accountType": {"type": "string", "nullable": true, "x-nullable": true}, "branch": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegistrationV1Request": {"type": "object", "properties": {"username": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreProvinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreDistrictCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "isVvip": {"type": "boolean", "description": "<PERSON><PERSON> phải mở tài khoản vvip không.", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateUserFinalResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateUserFinalResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateUserFinalResponse": {"type": "object", "properties": {"accountName": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNumber": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "accountType": {"type": "string", "nullable": true, "x-nullable": true}, "branch": {"type": "string", "nullable": true, "x-nullable": true}}}, "ConfirmQuickVerifyRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["PROCESSING", "SMS_OTP_VERIFIED", "EMAIL_OTP_VERIFIED", "SUCCESS", "FAILURE", "CANCEL", "CONFIRMED", "REJECTED", "PENDING"], "nullable": true, "x-nullable": true}}}, "BaseResponseCreateQuickVerifyResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateQuickVerifyResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateQuickVerifyResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["PROCESSING", "SMS_OTP_VERIFIED", "EMAIL_OTP_VERIFIED", "SUCCESS", "FAILURE", "CANCEL", "CONFIRMED", "REJECTED", "PENDING"], "nullable": true, "x-nullable": true}}}, "VerifyImportantInfoRequest": {"type": "object", "properties": {"actionType": {"type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}, "updateInfo": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateUserInfoRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "phoneNumber": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "email": {"pattern": "(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))", "type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateUserInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateUserInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateUserInfoResponse": {"type": "object", "properties": {"otpType": {"type": "string", "enum": ["EMAIL", "SMS", "SOLF", "AUTO"], "nullable": true, "x-nullable": true}}}, "VerifySmSOtpToUpdateUserInfoRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyPasswordRequest": {"type": "object", "properties": {"username": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyPasswordResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyPasswordResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyPasswordResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "VerifyEmailOtpToUpdateInfoRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyOtpRequest": {"type": "object", "properties": {"otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "AdvanceOTPRequest": {"type": "object", "properties": {"bankCif": {"type": "string", "nullable": true, "x-nullable": true}, "accountNoFrom": {"type": "string", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "nullable": true, "x-nullable": true}, "bankCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNoTo": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "object", "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseOTPToken": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/OTPToken"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "OTPToken": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "tokenType": {"type": "string", "enum": ["SOFT_TOKEN", "SMS_OTP", "HARD_TOKEN"], "nullable": true, "x-nullable": true}, "activated": {"type": "boolean", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}, "shareSecret": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "deviceName": {"type": "string", "nullable": true, "x-nullable": true}, "brand": {"type": "string", "nullable": true, "x-nullable": true}, "os": {"type": "string", "nullable": true, "x-nullable": true}, "imei": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseEtokenStatus": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "string", "enum": ["ACTIVATED", "UNACTIVATED", "UNREGISTERED", "ACTIVE_ON_ANOTHER_DEVICE"], "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateThemeScheduleRequest": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "nullable": true, "x-nullable": true}, "startTime": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu", "format": "date-time", "nullable": true, "x-nullable": true}, "endTime": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> điểm kết thúc", "format": "date-time", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateThemeScheduleResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateThemeScheduleResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateThemeScheduleResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "nullable": true, "x-nullable": true}, "startTime": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu", "format": "date-time", "nullable": true, "x-nullable": true}, "startJobName": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "nullable": true, "x-nullable": true}, "endTime": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> điểm kết thúc", "format": "date-time", "nullable": true, "x-nullable": true}, "endJobName": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "nullable": true, "x-nullable": true}}}, "BaseResponseObject": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "object", "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdentityRequest": {"type": "object", "properties": {"username": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "udid": {"type": "string", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "hasSupportNFC": {"type": "boolean", "description": "Mobile truyền device có hỗ trợ NFC hay không?", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyIdentityResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyIdentityResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdentityResponse": {"type": "object", "properties": {"branchCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "ConfirmOtpRequest": {"type": "object", "properties": {"otpCode": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}}}, "AccessTokenResponse": {"type": "object", "properties": {"require_change": {"type": "boolean", "nullable": true, "x-nullable": true}, "access_token": {"type": "string", "nullable": true, "x-nullable": true}, "expires_in": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "refresh_expires_in": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "refresh_token": {"type": "string", "nullable": true, "x-nullable": true}, "token_type": {"type": "string", "nullable": true, "x-nullable": true}, "id_token": {"type": "string", "nullable": true, "x-nullable": true}, "not-before-policy": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "session_state": {"type": "string", "nullable": true, "x-nullable": true}, "scope": {"type": "string", "nullable": true, "x-nullable": true}}}, "ReinstallEtokenRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "VerifyUserInfoRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}}}, "VerifyOtpToResetEtokenRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "otp": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyPasswordToResetEtokenResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyPasswordToResetEtokenResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyPasswordToResetEtokenResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "ForgotPasswordRequest": {"type": "object", "properties": {"identityInfo": {"type": "string", "nullable": true, "x-nullable": true}, "recoveryType": {"type": "string", "nullable": true, "x-nullable": true}, "udid": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseString": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "string", "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "RecoverPasswordRequest": {"type": "object", "properties": {"newPassword": {"type": "string", "nullable": true, "x-nullable": true}, "identityInfo": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyOtpForUnActiveUserRequest": {"type": "object", "properties": {"otp": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}}}, "GenerateOtpRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true, "x-nullable": true}, "otpType": {"type": "string", "nullable": true, "x-nullable": true}}}, "SignInRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}, "udid": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true, "x-nullable": true}, "deviceName": {"type": "string", "nullable": true, "x-nullable": true}, "deviceVersion": {"type": "string", "nullable": true, "x-nullable": true}, "deviceProvider": {"type": "string", "nullable": true, "x-nullable": true}, "playerId": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateNotificationInfoRequest": {"type": "object", "properties": {"playerId": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetListUserInfoByListUserIdRequest": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetListUserInfoByListUserIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListUserInfoByListUserIdResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetListUserInfoByListUserIdResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UserInfoData"}, "nullable": true, "x-nullable": true}}}, "UserInfoData": {"type": "object", "properties": {"cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "playerId": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "GetListUserInfoByListUserIdAndBranchRequest": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyOtpAndGetUserInfoRequest": {"type": "object", "properties": {"identityType": {"type": "string", "enum": ["PHONE", "ID_CARD"], "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyOtpAndGetUserInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyOtpAndGetUserInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyOtpAndGetUserInfoResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "secret": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "firstSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "secondSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "nullable": true, "x-nullable": true}, "bankVerified": {"type": "boolean", "nullable": true, "x-nullable": true}, "locked": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceIdUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createdFrom": {"type": "string", "nullable": true, "x-nullable": true}, "lockedOldApp": {"type": "boolean", "nullable": true, "x-nullable": true}, "verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceId": {"type": "string", "nullable": true, "x-nullable": true}, "suspiciousEkycStatus": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetQuickVerifyByIdentityResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetQuickVerifyByIdentityResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetQuickVerifyByIdentityResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["PROCESSING", "SMS_OTP_VERIFIED", "EMAIL_OTP_VERIFIED", "SUCCESS", "FAILURE", "CANCEL", "CONFIRMED", "REJECTED", "PENDING"], "nullable": true, "x-nullable": true}}}, "CreateQuickVerifyRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["PROCESSING", "SMS_OTP_VERIFIED", "EMAIL_OTP_VERIFIED", "SUCCESS", "FAILURE", "CANCEL", "CONFIRMED", "REJECTED", "PENDING"], "nullable": true, "x-nullable": true}}}, "CheckUserKlbRequest": {"type": "object", "properties": {"identityType": {"type": "string", "enum": ["PHONE", "ID_CARD"], "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckUserKlbResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckUserKlbResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckUserKlbResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckUserAndSendOtpRequest": {"type": "object", "properties": {"identityType": {"type": "string", "enum": ["PHONE", "ID_CARD"], "nullable": true, "x-nullable": true}, "identity": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckUserAndSendOtpResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckUserAndSendOtpResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckUserAndSendOtpResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["PROCESSING", "SMS_OTP_VERIFIED", "EMAIL_OTP_VERIFIED", "SUCCESS", "FAILURE", "CANCEL", "CONFIRMED", "REJECTED", "PENDING"], "nullable": true, "x-nullable": true}}}, "VerifyIdCardSideRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "description": "SĐT KH đăng ký EKYC", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "description": "Loại GTTT KH chọn khi EKYC", "nullable": true, "x-nullable": true}, "hasSupportNFC": {"type": "boolean", "description": "Mobile truyền device KH dùng có hỗ trợ NFC hay không?", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyIdCardSideResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyIdCardSideResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdCardSideResponse": {"type": "object", "properties": {"valid": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "VerifyIdCardFaceIdRequest": {"type": "object", "properties": {"frontIdCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "faceImageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseEkycFinalResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/EkycFinalResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "EkycFinalResponse": {"type": "object", "properties": {"ekycId": {"type": "string", "nullable": true, "x-nullable": true}, "document": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "id": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyEkycV1Request": {"type": "object", "properties": {"username": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "idCardSide": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateOcrRequest": {"type": "object", "properties": {"ekycId": {"type": "string", "nullable": true, "x-nullable": true}, "document": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "id": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "avatarBase64": {"type": "string", "nullable": true, "x-nullable": true}, "dsCert": {"type": "string", "nullable": true, "x-nullable": true}, "sodData": {"type": "string", "nullable": true, "x-nullable": true}, "dg1Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg2Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg3Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg4Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg5Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg6Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg7Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg8Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg9Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg10Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg11Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg12Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg13Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg14Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg15Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg16Data": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateOcrResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateOcrResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateOcrResponse": {"type": "object", "properties": {"ekycId": {"type": "string", "nullable": true, "x-nullable": true}, "document": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "id": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartLiveNessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageWidth": {"type": "string", "nullable": true, "x-nullable": true}, "imageHeight": {"type": "string", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "easyMode": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseStartLiveNessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StartLiveNessResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartLiveNessResponse": {"type": "object", "properties": {"faceRatioMin": {"type": "string", "nullable": true, "x-nullable": true}, "areaTop": {"type": "string", "nullable": true, "x-nullable": true}, "areaLeft": {"type": "string", "nullable": true, "x-nullable": true}, "areaWidth": {"type": "string", "nullable": true, "x-nullable": true}, "areaHeight": {"type": "string", "nullable": true, "x-nullable": true}, "noseTop": {"type": "string", "nullable": true, "x-nullable": true}, "noseLeft": {"type": "string", "nullable": true, "x-nullable": true}, "noseWidth": {"type": "string", "nullable": true, "x-nullable": true}, "noseHeight": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveLiveNessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "timestamp": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyEkycRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "idCardSide": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyProfileLivenessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyProfileLivenessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyProfileLivenessResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyProfileLivenessResponse": {"type": "object", "properties": {"verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartProfileLivenessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageWidth": {"type": "string", "nullable": true, "x-nullable": true}, "imageHeight": {"type": "string", "nullable": true, "x-nullable": true}, "easyMode": {"type": "boolean", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseStartProfileLivenessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StartProfileLivenessResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartProfileLivenessResponse": {"type": "object", "properties": {"exercise": {"type": "string", "nullable": true, "x-nullable": true}, "challenge_id": {"type": "string", "nullable": true, "x-nullable": true}, "user_id": {"type": "string", "nullable": true, "x-nullable": true}, "image_width": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "image_height": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_top": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_left": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_width": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_height": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "min_face_area_percent": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "task_order": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "SaveVideoEKYCProfileLivenessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "videoUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSaveVideoEKYCProfileLivenessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveVideoEKYCProfileLivenessResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveVideoEKYCProfileLivenessResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "metaType": {"type": "string", "nullable": true, "x-nullable": true}, "fileUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLivenessRequest": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "task": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSaveProfileLivenessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveProfileLivenessResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLivenessResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "metaType": {"type": "string", "nullable": true, "x-nullable": true}, "fileUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetLivenessTypeRequest": {"type": "object", "properties": {"phone": {"type": "string", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetLivenessTypeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetLivenessTypeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetLivenessTypeResponse": {"type": "object", "properties": {"phone": {"type": "string", "nullable": true, "x-nullable": true}, "livenessType": {"type": "string", "enum": ["TURN_FACE", "TURN_NOSE"], "nullable": true, "x-nullable": true}}}, "RegistrationRequest": {"type": "object", "properties": {"username": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "password": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreProvinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreDistrictCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdCardSide2345Request": {"type": "object", "properties": {"hasSupportNFC": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyIdCardSide2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyIdCardSide2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdCardSide2345Response": {"type": "object", "properties": {"valid": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "VerifyIdCardFaceId2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "frontIdCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "faceImageUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyIdCardFaceId2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyIdCardFaceId2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyIdCardFaceId2345Response": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "placeOfOrigin": {"type": "string", "nullable": true, "x-nullable": true}, "placeOfResidence": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateOCRByNFC2345Request": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "oldIdCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "placeOfOrigin": {"type": "string", "nullable": true, "x-nullable": true}, "placeOfResidence": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "avatarBase64": {"type": "string", "nullable": true, "x-nullable": true}, "dsCert": {"type": "string", "nullable": true, "x-nullable": true}, "sodData": {"type": "string", "nullable": true, "x-nullable": true}, "dg1Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg2Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg3Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg4Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg5Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg6Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg7Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg8Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg9Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg10Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg11Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg12Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg13Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg14Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg15Data": {"type": "string", "nullable": true, "x-nullable": true}, "dg16Data": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateOCRByNFC2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateOCRByNFC2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UpdateOCRByNFC2345Response": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "licenseClass": {"type": "string", "nullable": true, "x-nullable": true}, "expiry": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCheck": {"type": "string", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "placeOfOrigin": {"type": "string", "nullable": true, "x-nullable": true}, "placeOfResidence": {"type": "string", "nullable": true, "x-nullable": true}, "national": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "precinctCode": {"type": "string", "nullable": true, "x-nullable": true}, "ethnicity": {"type": "string", "nullable": true, "x-nullable": true}, "religion": {"type": "string", "nullable": true, "x-nullable": true}, "characteristics": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveAndReadIdCard2345Request": {"type": "object", "properties": {"username": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "idCardSide": {"type": "string", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}}}, "User2345VerifyType": {"type": "string", "enum": ["CHIP_ID", "FACE_ID", "NONE", "GTTT_EXPIRE", "GTTT_EXPIRING_SOON"]}, "VerifyProfileLiveness2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "verifyType": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseVerifyProfileLiveness2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyProfileLiveness2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyProfileLiveness2345Response": {"type": "object", "properties": {"verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartProfileLiveness2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageWidth": {"type": "string", "nullable": true, "x-nullable": true}, "imageHeight": {"type": "string", "nullable": true, "x-nullable": true}, "easyMode": {"type": "boolean", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "verifyType": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseStartProfileLiveness2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StartProfileLiveness2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartProfileLiveness2345Response": {"type": "object", "properties": {"exercise": {"type": "string", "nullable": true, "x-nullable": true}, "challenge_id": {"type": "string", "nullable": true, "x-nullable": true}, "user_id": {"type": "string", "nullable": true, "x-nullable": true}, "image_width": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "image_height": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_top": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_left": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_width": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "area_height": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "min_face_area_percent": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "task_order": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "SaveVideoEKYCProfileLiveness2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "videoUrl": {"type": "string", "nullable": true, "x-nullable": true}, "verifyType": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseSaveVideoEKYCProfileLiveness2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveVideoEKYCProfileLiveness2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveVideoEKYCProfileLiveness2345Response": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "metaType": {"type": "string", "nullable": true, "x-nullable": true}, "fileUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLiveness2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "task": {"type": "string", "nullable": true, "x-nullable": true}, "verifyType": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseSaveProfileLiveness2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveProfileLiveness2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLiveness2345Response": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "metaType": {"type": "string", "nullable": true, "x-nullable": true}, "fileUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "nullable": true, "x-nullable": true}}}, "FinishVerify2345Request": {"type": "object", "properties": {"verifyType": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseFinishVerify2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/FinishVerify2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "FinishVerify2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CompareCustomerFace2345Request": {"type": "object", "properties": {"newFaceImageUrl": {"type": "string", "description": "Ảnh center face KH", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "description": "<PERSON><PERSON><PERSON> cho compare face giao dich", "nullable": true, "x-nullable": true}, "compareFaceForTransaction": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCompareCustomerFace2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CompareCustomerFace2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CompareCustomerFace2345Response": {"type": "object", "properties": {"samePerson": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "VerifyProfileLivenessTransaction2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyProfileLivenessTransaction2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyProfileLivenessTransaction2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyProfileLivenessTransaction2345Response": {"type": "object", "properties": {"verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartProfileLivenessTransaction2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageWidth": {"type": "string", "nullable": true, "x-nullable": true}, "imageHeight": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveVideoProfileLivenessTransaction2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "videoUrl": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSaveVideoProfileLivenessTransaction2345Request": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveVideoProfileLivenessTransaction2345Request"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLivenessTransaction2345Request": {"type": "object", "properties": {"userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "imageUrl": {"type": "string", "nullable": true, "x-nullable": true}, "task": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSaveProfileLivenessTransaction2345Response": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveProfileLivenessTransaction2345Response"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveProfileLivenessTransaction2345Response": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "metaType": {"type": "string", "nullable": true, "x-nullable": true}, "fileUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}, "createAt": {"type": "string", "nullable": true, "x-nullable": true}}}, "ValidateUserRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateUserFromSTMRequest": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"pattern": "((09|03|07|08|05)+([0-9]{8})\\b)", "type": "string", "nullable": true, "x-nullable": true}, "email": {"pattern": "(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))", "type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "dob": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "nullable": true, "x-nullable": true}, "avatar": {"type": "string", "nullable": true, "x-nullable": true}, "nationality": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssuePlc": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDt": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "permanentAddress": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddress": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageUp": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageDown": {"type": "string", "nullable": true, "x-nullable": true}, "openAccountFormUp": {"type": "string", "nullable": true, "x-nullable": true}, "openAccountFormDown": {"type": "string", "nullable": true, "x-nullable": true}, "contractFormUp": {"type": "string", "nullable": true, "x-nullable": true}, "contractFormDown": {"type": "string", "nullable": true, "x-nullable": true}, "servicePack": {"type": "string", "nullable": true, "x-nullable": true}, "cardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "issueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "expirationDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "track1": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "faceId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateUserFromSTMResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateUserFromSTMResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateUserFromSTMResponse": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegisterSaleAppCollabRequest": {"type": "object", "properties": {"branchCode": {"type": "string", "description": "Mã branch", "nullable": true, "x-nullable": true}, "email": {"type": "string", "description": "<PERSON><PERSON> (n<PERSON>u có)", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "Mã CIF người giới thi<PERSON> (nếu có)", "nullable": true, "x-nullable": true}}}, "BaseResponseRegisterSaleAppCollabResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/RegisterSaleAppCollabResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegisterSaleAppCollabResponse": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "nullable": true, "x-nullable": true}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "positionName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "example": "CTV", "nullable": true, "x-nullable": true}}}, "NotificationRequest": {"type": "object", "properties": {"cif": {"type": "string", "nullable": true, "x-nullable": true}, "subject": {"type": "string", "nullable": true, "x-nullable": true}, "body": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateNotificationResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateNotificationResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateNotificationResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "recipients": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "errors": {"type": "object", "nullable": true, "x-nullable": true}}}, "BaseResponseMigrateUserCrmResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MigrateUserCrmResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "MigrateUserCrmResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseMigrateCustomerFromOldSysResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MigrateCustomerFromOldSysResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "MigrateCustomerFromOldSysResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseMigrateKsfCustomerResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MigrateKsfCustomerResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "MigrateKsfCustomerResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CreateTransactionLivenessSessionRequest": {"type": "object", "properties": {"transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateTransactionLivenessSessionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateTransactionLivenessSessionResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateTransactionLivenessSessionResponse": {"type": "object", "properties": {"sessionId": {"type": "string", "nullable": true, "x-nullable": true}, "sessionToken": {"type": "string", "nullable": true, "x-nullable": true}, "region": {"type": "string", "nullable": true, "x-nullable": true}, "accessKeyId": {"type": "string", "nullable": true, "x-nullable": true}, "secretAccessKey": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateTermsRequest": {"type": "object", "properties": {"requestId": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true, "x-nullable": true}, "type": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true, "x-nullable": true}, "title": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true, "x-nullable": true}, "content": {"type": "string", "nullable": true, "x-nullable": true}}}, "CustomerPasswordChangeRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegisterCifCustomerRequest": {"type": "object", "properties": {"servicePackId": {"type": "string", "description": "Mã gói dịch vụ ebank", "nullable": true, "x-nullable": true}, "otpMethod": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thức xác thực: SMS_OTP/SOFT_OTP", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON>p", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "nullable": true, "x-nullable": true}, "email": {"type": "string", "description": "<PERSON><PERSON><PERSON> thư điện tử", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "description": "Mã số định danh cá nhân", "nullable": true, "x-nullable": true}, "issuedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON>p", "nullable": true, "x-nullable": true}, "issuedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON>", "format": "date", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "description": "<PERSON><PERSON> tên đ<PERSON>y đủ", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "format": "date", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "nullable": true, "x-nullable": true}, "national": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "description": "Địa chỉ thường trú", "nullable": true, "x-nullable": true}, "address": {"type": "string", "description": "Đ<PERSON>a chỉ liên lạc", "nullable": true, "x-nullable": true}, "firstSignUrl": {"type": "string", "description": "<PERSON> <PERSON><PERSON> chử ký mẫu thứ nhất", "nullable": true, "x-nullable": true}, "secondSignUrl": {"type": "string", "description": "<PERSON> <PERSON><PERSON> chử ký mẫu thứ hai", "nullable": true, "x-nullable": true}, "cifNumber": {"type": "string", "description": "Mã số định danh CIF", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "openDate": {"type": "string", "description": "Ngày mở hồ sơ CIF", "format": "date", "nullable": true, "x-nullable": true}}}, "OpenCustomerRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "nullable": true, "x-nullable": true}}}, "CloseCustomerRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetBranchRequest": {"type": "object", "properties": {"provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportBranchResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportBranchResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BranchResponse": {"type": "object", "properties": {"branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "branchName": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportBranchResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/BranchResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseQueryInfoResponseV2": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/QueryInfoResponseV2"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "QueryInfoResponseV2": {"type": "object", "properties": {"loginType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "otpMethod": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "aliasName": {"type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "facebookLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "facebookId": {"type": "string", "nullable": true, "x-nullable": true}, "facebookName": {"type": "string", "nullable": true, "x-nullable": true}, "googleLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "googleId": {"type": "string", "nullable": true, "x-nullable": true}, "googleName": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "nullable": true, "x-nullable": true}, "enabled2fa": {"type": "boolean", "nullable": true, "x-nullable": true}, "bankVerified": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdFrom": {"type": "string", "nullable": true, "x-nullable": true}, "lockedOldApp": {"type": "boolean", "nullable": true, "x-nullable": true}, "ekycId": {"type": "string", "nullable": true, "x-nullable": true}, "suspiciousEkycStatus": {"type": "string", "nullable": true, "x-nullable": true}, "customerGroupUserId": {"type": "string", "nullable": true, "x-nullable": true}, "organization": {"type": "string", "enum": ["VNPOST", "KL", "OTHER"], "nullable": true, "x-nullable": true}, "identityStatus": {"type": "string", "nullable": true, "x-nullable": true}, "isVerifiedSTH": {"type": "boolean", "description": "Trạng thái xác thực STH. L<PERSON>y theo trạng thái xác thực BCA.", "nullable": true, "x-nullable": true}, "isNobleEmployee": {"type": "boolean", "description": "<PERSON><PERSON> phải CBBH hay không.", "nullable": true, "x-nullable": true}, "passEkyc": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseQueryInfoResponseV1": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/QueryInfoResponseV1"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "QueryInfoResponseV1": {"type": "object", "properties": {"loginType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "otpMethod": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "aliasName": {"type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "facebookLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "facebookId": {"type": "string", "nullable": true, "x-nullable": true}, "facebookName": {"type": "string", "nullable": true, "x-nullable": true}, "googleLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "googleId": {"type": "string", "nullable": true, "x-nullable": true}, "googleName": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "enabled2fa": {"type": "boolean", "nullable": true, "x-nullable": true}, "bankVerified": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetUserBranchResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetUserBranchResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetUserBranchResponse": {"type": "object", "properties": {"branchCode": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListThemeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListThemeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetListThemeResponse": {"type": "object", "properties": {"amount": {"type": "integer", "description": "S<PERSON> lượng kết quả tìm thấy", "format": "int32", "nullable": true, "x-nullable": true}, "records": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch kết quả", "items": {"$ref": "#/components/schemas/ThemeData"}, "nullable": true, "x-nullable": true}}}, "ThemeData": {"type": "object", "properties": {"id": {"type": "string", "description": "ID", "nullable": true, "x-nullable": true}, "name": {"type": "string", "description": "Tên theme", "nullable": true, "x-nullable": true}, "description": {"type": "string", "description": "mô tả", "nullable": true, "x-nullable": true}, "category": {"type": "string", "description": "<PERSON><PERSON>", "nullable": true, "x-nullable": true}, "thumbnailUrl": {"type": "string", "description": "Link tới file thumbnail", "nullable": true, "x-nullable": true}, "thumbnailSize": {"type": "string", "description": "Size thumbnail", "nullable": true, "x-nullable": true}, "thumbnailType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "active": {"type": "boolean", "description": "<PERSON><PERSON> đ<PERSON> sử dụng hay không?", "nullable": true, "x-nullable": true}}, "description": "<PERSON><PERSON> s<PERSON>ch kết quả"}, "BaseResponseGetThemeDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetThemeDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetThemeDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "nullable": true, "x-nullable": true}, "name": {"type": "string", "description": "Tên theme", "nullable": true, "x-nullable": true}, "description": {"type": "string", "description": "mô tả", "nullable": true, "x-nullable": true}, "category": {"type": "string", "description": "<PERSON><PERSON>", "nullable": true, "x-nullable": true}, "customerRank": {"type": "string", "description": "<PERSON><PERSON><PERSON>(b<PERSON><PERSON>, DIAMOND,...)", "enum": ["NORMAL", "DIAMOND", "RUBY", "SAPPHIRE", "DIAMOND_ELITE"], "nullable": true, "x-nullable": true}, "startTime": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu", "format": "date-time", "nullable": true, "x-nullable": true}, "endTime": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> điểm kết thúc", "format": "date-time", "nullable": true, "x-nullable": true}, "thumbnailUrl": {"type": "string", "description": "Link tới file thumbnail", "nullable": true, "x-nullable": true}, "thumbnailSize": {"type": "string", "description": "Size thumbnail", "nullable": true, "x-nullable": true}, "thumbnailType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "bannerUrl": {"type": "string", "description": "Link tới file banner", "nullable": true, "x-nullable": true}, "bannerSize": {"type": "string", "description": "<PERSON>ze banner", "nullable": true, "x-nullable": true}, "bannerType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "introUrl": {"type": "string", "description": "Flash screen", "nullable": true, "x-nullable": true}, "introSize": {"type": "string", "description": "Flash screen size", "nullable": true, "x-nullable": true}, "introType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "loginUrl": {"type": "string", "description": "Link tới file màn login", "nullable": true, "x-nullable": true}, "loginSize": {"type": "string", "description": "Size file màn login", "nullable": true, "x-nullable": true}, "loginType": {"type": "string", "description": "Loại file màn login", "nullable": true, "x-nullable": true}, "logoUrl": {"type": "string", "description": "Link tới file logo", "nullable": true, "x-nullable": true}, "logoSize": {"type": "string", "description": "Size file logo", "nullable": true, "x-nullable": true}, "logoType": {"type": "string", "description": "Loại file logo", "nullable": true, "x-nullable": true}, "logoRankUrl": {"type": "string", "description": "Logo phân hạng", "nullable": true, "x-nullable": true}, "logoRankSize": {"type": "string", "description": "Logo rank size", "nullable": true, "x-nullable": true}, "logoRankType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "bannerRankUrl": {"type": "string", "description": "Banner phân hạng", "nullable": true, "x-nullable": true}, "bannerRankSize": {"type": "string", "description": "Banner rank size", "nullable": true, "x-nullable": true}, "bannerRankType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "logoShortUrl": {"type": "string", "description": "Logo ng<PERSON>n không có chữ", "nullable": true, "x-nullable": true}, "logoShortSize": {"type": "string", "description": "Logo short size", "nullable": true, "x-nullable": true}, "logoShortType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "logoLoginUrl": {"type": "string", "description": "Logo ở màn hình login", "nullable": true, "x-nullable": true}, "logoLoginSize": {"type": "string", "description": "Logo login size", "nullable": true, "x-nullable": true}, "logoLoginType": {"type": "string", "description": "Loại file", "nullable": true, "x-nullable": true}, "faceIdUrl": {"type": "string", "description": "Link tới file ảnh của button faceId", "nullable": true, "x-nullable": true}, "faceIdSize": {"type": "string", "description": "Size file ảnh faceId", "nullable": true, "x-nullable": true}, "faceIdType": {"type": "string", "description": "Loại file ảnh faceId", "nullable": true, "x-nullable": true}, "backgroundColor": {"type": "string", "description": "Mã màu background", "nullable": true, "x-nullable": true}, "textColor": {"type": "string", "description": "Mã màu text", "nullable": true, "x-nullable": true}, "iconColorLogin": {"type": "string", "description": "Mã màu icon và text trong màn hình <PERSON>gin", "nullable": true, "x-nullable": true}, "iconColorMain": {"type": "string", "description": "Mã màu icon và text trong màn hình ch<PERSON>h", "nullable": true, "x-nullable": true}, "brightLogo": {"type": "boolean", "description": "Sử dụng logo sáng màu hay không?", "nullable": true, "x-nullable": true}, "active": {"type": "boolean", "description": "<PERSON><PERSON> đ<PERSON> sử dụng hay không?", "nullable": true, "x-nullable": true}, "defaultTheme": {"type": "boolean", "description": "<PERSON>ó phải theme mặc định hay không?", "nullable": true, "x-nullable": true}, "deleted": {"type": "boolean", "description": "Đã xóa/ngưng hoạt động hay chưa?", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON> nhật gần nhất bởi", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t gần nhất", "format": "date-time", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> tạo bởi", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "format": "date-time", "nullable": true, "x-nullable": true}}}, "BaseResponseMapStringObject": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"type": "object", "additionalProperties": {"type": "object"}, "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetUserKlbInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetUserKlbInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetUserKlbInfoResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "secret": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "firstSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "secondSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "nullable": true, "x-nullable": true}, "bankVerified": {"type": "boolean", "nullable": true, "x-nullable": true}, "locked": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceIdUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createdFrom": {"type": "string", "nullable": true, "x-nullable": true}, "lockedOldApp": {"type": "boolean", "nullable": true, "x-nullable": true}, "verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceId": {"type": "string", "nullable": true, "x-nullable": true}, "suspiciousEkycStatus": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetUserInfoByCifResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetUserInfoByCifResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetUserInfoByCifResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "secret": {"type": "string", "nullable": true, "x-nullable": true}, "frontCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "backCardUrl": {"type": "string", "nullable": true, "x-nullable": true}, "firstSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "secondSignUrl": {"type": "string", "nullable": true, "x-nullable": true}, "ekycId": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "nullable": true, "x-nullable": true}, "bankVerified": {"type": "boolean", "nullable": true, "x-nullable": true}, "locked": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceIdUrl": {"type": "string", "nullable": true, "x-nullable": true}, "createdFrom": {"type": "string", "nullable": true, "x-nullable": true}, "lockedOldApp": {"type": "boolean", "nullable": true, "x-nullable": true}, "verified": {"type": "boolean", "nullable": true, "x-nullable": true}, "faceId": {"type": "string", "nullable": true, "x-nullable": true}, "suspiciousEkycStatus": {"type": "string", "nullable": true, "x-nullable": true}, "enabled": {"type": "boolean", "nullable": true, "x-nullable": true}, "veinId": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddressVi": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddressEn": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListUserIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListUserIdResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetListUserIdResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UserIdAndPhone"}, "nullable": true, "x-nullable": true}}}, "UserIdAndPhone": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseBirthdayUserResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/BirthdayUserResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BirthdayUserResponse": {"type": "object", "properties": {"cifList": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "BaseResponseEkycStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/EkycStatusResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "EkycStatusResponse": {"type": "object", "properties": {"userLevelType": {"type": "string", "enum": ["COUNTERS_ACCOUNT", "VIDEO_COUNTERS_KYC", "COUNTERS_KYC", "VIDEO_KYC", "E_KYC", "NO_KYC"], "nullable": true, "x-nullable": true}}}, "BaseResponseQueryInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/QueryInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "QueryInfoResponse": {"type": "object", "properties": {"loginType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "avatarUrl": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "otpMethod": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "aliasName": {"type": "string", "nullable": true, "x-nullable": true}, "username": {"type": "string", "nullable": true, "x-nullable": true}, "facebookLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "facebookId": {"type": "string", "nullable": true, "x-nullable": true}, "facebookName": {"type": "string", "nullable": true, "x-nullable": true}, "googleLinked": {"type": "boolean", "nullable": true, "x-nullable": true}, "googleId": {"type": "string", "nullable": true, "x-nullable": true}, "googleName": {"type": "string", "nullable": true, "x-nullable": true}, "sex": {"type": "string", "nullable": true, "x-nullable": true}, "birthday": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardType": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardExpireDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardIssuePlace": {"type": "string", "nullable": true, "x-nullable": true}, "resAddr": {"type": "string", "nullable": true, "x-nullable": true}, "resCity": {"type": "string", "nullable": true, "x-nullable": true}, "nationCode": {"type": "string", "nullable": true, "x-nullable": true}, "taxCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardVlg": {"type": "string", "nullable": true, "x-nullable": true}, "street": {"type": "string", "nullable": true, "x-nullable": true}, "no123": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "nullable": true, "x-nullable": true}, "enabled2fa": {"type": "boolean", "nullable": true, "x-nullable": true}, "suspiciousEkycStatus": {"type": "string", "nullable": true, "x-nullable": true}, "createdFrom": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "locked": {"type": "boolean", "nullable": true, "x-nullable": true}, "enabled": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNobleEmployee": {"type": "boolean", "description": "<PERSON><PERSON> phải CBBH hay không.", "nullable": true, "x-nullable": true}}}, "BaseResponseGetCustomerRankResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetCustomerRankResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetCustomerRankResponse": {"type": "object", "properties": {"customerRank": {"type": "string", "description": "<PERSON><PERSON> hạng của khách hàng", "nullable": true, "x-nullable": true}, "customerRankCode": {"type": "string", "description": "Code phân hạng của khách hàng", "nullable": true, "x-nullable": true}, "customerRankDescription": {"type": "string", "description": "<PERSON><PERSON> tả cụ thể phân hạng khách hàng", "nullable": true, "x-nullable": true}, "startDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON>nh danh: yyyy-MM-dd", "format": "date", "nullable": true, "x-nullable": true}, "dueDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> hết hạn định danh: yyyy-MM-dd", "format": "date", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "description": "Mã đơn vị (branch)", "nullable": true, "x-nullable": true}}}, "BaseResponseGetBranchDetailByCodeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetBranchDetailByCodeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BranchImageResponse": {"type": "object", "properties": {"uuid": {"type": "string", "description": "UUID", "nullable": true, "x-nullable": true}, "url": {"type": "string", "description": "<PERSON> t<PERSON>i h<PERSON>nh <PERSON>nh", "nullable": true, "x-nullable": true}, "avatar": {"type": "boolean", "description": "Có dùng làm ảnh đại diện hay không?", "nullable": true, "x-nullable": true}, "deleted": {"type": "boolean", "description": "Đã xóa/ngưng hoạt động hay chưa?", "nullable": true, "x-nullable": true}}, "description": "<PERSON><PERSON> s<PERSON> h<PERSON>"}, "GetBranchDetailByCodeResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "addressFull": {"type": "string", "description": "Địa chỉ đơn vị", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "nullable": true, "x-nullable": true}, "longitude": {"type": "string", "description": "<PERSON>nh đ<PERSON>", "nullable": true, "x-nullable": true}, "latitude": {"type": "string", "description": "<PERSON><PERSON>", "nullable": true, "x-nullable": true}, "images": {"type": "array", "description": "<PERSON><PERSON> s<PERSON> h<PERSON>", "items": {"$ref": "#/components/schemas/BranchImageResponse"}, "nullable": true, "x-nullable": true}}}, "BaseResponseUserBankInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UserBankInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "UserBankInfoResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cifNumber": {"type": "string", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "ebankingTypeId": {"type": "string", "nullable": true, "x-nullable": true}, "rollbackEbankingTypeId": {"type": "string", "nullable": true, "x-nullable": true}, "rollbackEbankingTypeTime": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}}}, "BaseResponseLoginByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/LoginByBiometricsSettingResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "LoginByBiometricsSettingResponse": {"type": "object", "properties": {"enable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "AuthenTransByBiometricsSettingResponse": {"type": "object", "properties": {"enable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseAuthenTransByBiometricsSettingResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/AuthenTransByBiometricsSettingResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckNeedVerifyTypeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckNeedVerifyTypeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckNeedVerifyTypeResponse": {"type": "object", "properties": {"type": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/User2345VerifyType"}, {"$ref": "#/components/schemas/NullType"}]}, "title": {"type": "string", "nullable": true, "x-nullable": true}, "content": {"type": "string", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetPlayerIdsByCifNoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetPlayerIdsByCifNoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetPlayerIdsByCifNoResponse": {"type": "object", "properties": {"playerIds": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetNotificationInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetNotificationInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetNotificationInfoResponse": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "playerIds": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}, "verifiedEmail": {"type": "boolean", "nullable": true, "x-nullable": true}, "pushNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}, "emailNotificationEnable": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportDeviceInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportDeviceInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "DeviceInfoResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "udid": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "userIdentity": {"type": "string", "nullable": true, "x-nullable": true}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "loginSession": {"type": "string", "nullable": true, "x-nullable": true}, "deviceName": {"type": "string", "nullable": true, "x-nullable": true}, "deviceVersion": {"type": "string", "nullable": true, "x-nullable": true}, "deviceProvider": {"type": "string", "nullable": true, "x-nullable": true}, "etokenDevice": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "PageSupportDeviceInfoResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceInfoResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetCustomerGroupListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetCustomerGroupListResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CustomerGroupListDto": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "nullable": true, "x-nullable": true}, "groupCode": {"type": "string", "description": "mã nhóm khách hàng", "nullable": true, "x-nullable": true}, "groupName": {"type": "string", "description": "tên <PERSON>", "nullable": true, "x-nullable": true}}}, "GetCustomerGroupListResponse": {"type": "object", "properties": {"customerGroupList": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerGroupListDto"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetCustomerGroupUseListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetCustomerGroupUseListResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CustomerGroupUseListDto": {"type": "object", "properties": {"id": {"type": "string", "description": "id", "nullable": true, "x-nullable": true}, "customerGroupId": {"type": "string", "description": "mã nhóm khách hàng", "nullable": true, "x-nullable": true}, "customerGroupUseName": {"type": "string", "description": "tên <PERSON>", "nullable": true, "x-nullable": true}}}, "GetCustomerGroupUseListResponse": {"type": "object", "properties": {"customerGroupUseList": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerGroupUseListDto"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetTransactionLivenessSessionResultResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetTransactionLivenessSessionResultResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetTransactionLivenessSessionResultResponse": {"type": "object", "properties": {"sessionId": {"type": "string", "nullable": true, "x-nullable": true}, "verified": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportWardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportWardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportWardResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WardResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "WardResponse": {"type": "object", "properties": {"wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardNameEn": {"type": "string", "nullable": true, "x-nullable": true}, "wardNameVi": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportWardNetworkResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportWardNetworkResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "NetworkResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceName": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtName": {"type": "string", "nullable": true, "x-nullable": true}, "unitCode": {"type": "string", "nullable": true, "x-nullable": true}, "unitName": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportWardNetworkResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WardNetworkResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "WardNetworkResponse": {"type": "object", "properties": {"wardCode": {"type": "string", "nullable": true, "x-nullable": true}, "wardNameVi": {"type": "string", "nullable": true, "x-nullable": true}, "wardNameEn": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "networks": {"type": "array", "items": {"$ref": "#/components/schemas/NetworkResponse"}, "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportNetworkResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportNetworkResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportNetworkResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/NetworkResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportProvinceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportProvinceResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportProvinceResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/ProvinceResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "ProvinceResponse": {"type": "object", "properties": {"provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreProvinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "provinceNameVn": {"type": "string", "nullable": true, "x-nullable": true}, "provinceNameEn": {"type": "string", "nullable": true, "x-nullable": true}, "hasBranches": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportDistrictResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportDistrictResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "DistrictResponse": {"type": "object", "properties": {"districtCode": {"type": "string", "nullable": true, "x-nullable": true}, "coreDistrictCode": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "districtNameVn": {"type": "string", "nullable": true, "x-nullable": true}, "districtNameEn": {"type": "string", "nullable": true, "x-nullable": true}, "provinceCode": {"type": "string", "nullable": true, "x-nullable": true}, "hasBranches": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "PageSupportDistrictResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/DistrictResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseProvinceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ProvinceResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseDistrictResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/DistrictResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "NullType": {"description": "for adding nullability to a ref", "enum": [null]}}, "securitySchemes": {"Authorization": {"type": "http", "description": "Access token", "in": "header", "scheme": "bearer", "bearerFormat": "Bearer [token]"}}}}