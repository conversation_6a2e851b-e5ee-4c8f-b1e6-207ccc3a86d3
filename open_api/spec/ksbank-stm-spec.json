{"openapi": "3.0.1", "info": {"title": "STM API", "description": "Documentation STM service API", "version": "1.0"}, "servers": [{"url": "https://dev-ksapi.ssf.vn/stm"}], "security": [{"Authorization": []}], "tags": [{"name": "API upload media cho STM", "description": "API upload media"}, {"name": "API mở tài k<PERSON>n qua STM", "description": "Tất cả các API phục vụ cho việc mở tài khoản tại máy STM"}, {"name": "QR PAYMENT STM API", "description": "API for withdraw with QR code function"}, {"name": "API Mở thẻ bằng mã QR", "description": "<PERSON><PERSON><PERSON> a<PERSON> đ<PERSON><PERSON><PERSON> thực hiện 1 luồng theo số thứ tự"}, {"name": "BANK BUSINESS FOR STM API", "description": "API for Bank business"}], "paths": {"/qr-payment/qr-code": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "1. [STM API] get QR code", "description": "STM call this API to get QR code.", "operationId": "getQrCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetQRCodeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQrCodeResponse"}}}}}}}, "/qr-payment/old/execute/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "6.[STM gọi] API hạch toán.", "description": "STM gọi API này sau khi người dùng đã chọn tài khoản để thanh toán.", "operationId": "executeQrTransfer", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseExecuteQRTransferResponse"}}}}}}}, "/qr-payment/old/confirm/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "7.[STM gọi] API confirm vi<PERSON><PERSON> rú<PERSON> tiền. ", "description": "STM gọi api này để xác nhận việc rút tiền có thành công hay không, nếu rút tiền thành công để status là SUCCESSnếu thất bại để status là  FAILED gửi kèm message ghi lý do thất bại nhả tiền.", "operationId": "confirmQRTransfer", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmQRPaymentApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseConfirmQRTransactionResponse"}}}}}}}, "/qr-payment/fulfil/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "4. API fulfill transaction form.", "description": "Mobile api: mobile gọi api này để để chọn tài khoản cũng như số tiền muốn rút", "operationId": "setTransactionAmount", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapTemplateToSTMRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSetAmountCommandResponse"}}}}}}}, "/qr-payment/execute/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "6.[STM gọi] API hạch toán. (Version2)", "description": "STM gọi API này sau khi người dùng đã chọn tài khoản để thanh toán.", "operationId": "executeQrTransferV2", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseExecuteQRTransferResponseV2"}}}}}}}, "/qr-payment/detail/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "3. API get STM info via Scan QR code.", "description": "Mobile call this API to get STM info for next step.", "operationId": "getStmInfo", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetQRPaymentResponse"}}}}}}}, "/qr-payment/confirm/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "summary": "7.[STM gọi] API confirm vi<PERSON><PERSON> rút tiền. (Version2)", "description": "STM gọi api này để xác nhận việc rút tiền có thành công hay không, nếu rút tiền thành công để status là SUCCESSnếu thất bại để status là  FAILED gửi kèm message ghi lý do thất bại nhả tiền.", "operationId": "confirmQRTransferV2", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmQRPaymentApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseConfirmQRTransactionResponseV2"}}}}}}}, "/qr-payment/cancel/{qrPaymentId}": {"post": {"tags": ["QR PAYMENT STM API"], "operationId": "cancelQRPayment", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelQRPaymentResponse"}}}}}}}, "/open-card/qr-code": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "1. [STM API] get QR code", "description": "<PERSON> đầu khi người dùng đến cây STM, ấn vào phần mở thẻ thì sẽ gọi API này để lấy nội dungmã QR và hiển thị cho người dùng", "operationId": "getQrCode_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenCardQrRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOpenCardQRResponse"}}}}}}}, "/open-card/mobile/cancel/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "11. [Mobile] <PERSON><PERSON><PERSON> giao dịch mở thẻ", "description": "Mobile hủy giao dịch", "operationId": "mobileCancelTransaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelOpenCardResponse"}}}}}}}, "/open-card/execute-open-card/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "7. [STM API] Api thực hiện mở thẻ", "description": "<PERSON>u khi chọn số tài khoản cần mở thẻ và ấn tiếp tục, màn hình sẽ hiển thị lên phần nhậpmã PIN cho etoken, nhập mã pin, l<PERSON>y được etoken và truyền vào body của API này để gọi việc mở thẻ", "operationId": "openCard", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteOpenCardApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOpenCardResponse"}}}}}}}, "/open-card/confirm-take-card/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "9. [STM API] báo việc in thẻ và nhả thẻ thành công hay thất bại", "description": "Sau khi STM tiến hành in thẻ, nếu thành công hay trục trặc gì và thất bại thì báo lên server qua api này", "operationId": "confirmTakeCard", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOpenCardApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOpenCardConfirmTakeCardResponse"}}}}}}}, "/open-card/card-info/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "6. [STM API] đ<PERSON><PERSON> thông tin của thẻ lên", "description": "<PERSON>hi người dùng đã quét xong mã QR, STM lấy một thẻ và đẩy số thẻ lên cho hệ thốngđể dùng cho mở thẻ ở luồng tiếp theo", "operationId": "submitCardInfo", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitCardInfoApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSubmitCardInfoResponse"}}}}}}}, "/open-card/cancel/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "10. [STM API] H<PERSON>y giao dịch mở thẻ", "description": "Gọi api này khi ấn vào nút hủy hoặc quá thời gian đợi của luồng mà chưa hoàn thành việc mở thẻ", "operationId": "cancelTransaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelOpenCardResponse"}}}}}}}, "/open-card/account-info/{transactionId}": {"post": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "4. [Mobile API] <PERSON><PERSON><PERSON> lên thông tin tài khoản đư<PERSON><PERSON> chọn của người dùng", "description": "Sau khi quét mã QR nhận được thông tin máy STM (id và vị trí) hiển thị nó lên cùng với cảmột select button cho phép chọn tài khoản để mở thẻ, sau khi chọn tài khoản xong thì đẩy thông tinnày lên qua api này sau khi ấn tiếp tục", "operationId": "mappingAccountInfo", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MappingAccountApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseMappingAccountResponse"}}}}}}}, "/open-account/wait-verify-photo/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "4.2. <PERSON><PERSON><PERSON> confirm <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, enum trả về là COMPLETED hoặc RECAPTURE", "operationId": "waiteVerifyPhoto", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitVerifyPhotoStatusResponse"}}}}}}}, "/open-account/wait-verify-info/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "7. <PERSON><PERSON><PERSON> x<PERSON>c thực thông tin người dùng", "description": "Đợi lấy kết quả thành công: 2000000, lúc này xem trạng thái ở phần data trong response làn \nđược phê duyệt hay không (VERIFIED_USER_INFO và VERIFY_USER_INFO_FAILED). Nếu quá thời gian quy định code 4000021 \n, nếu không tìm thấy yêu cầu mở tài khoản trên mã 4040000", "operationId": "waiteVerifyInfo", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitVerifyUserInfoResponse"}}}}}}}, "/open-account/wait-verify-contract/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "8. <PERSON><PERSON><PERSON> x<PERSON>c thực thông tin hợp đồng", "description": "Đợi lấy kết quả thành công: 2000000, lúc này xem trạng thái ở phần data trong response làn \nđược phê duyệt hay không (VERIFIED_CONTRACT và VERIFY_CONTRACT_FAILED). Nếu quá thời gian quy định code 4000021 \n, nếu không tìm thấy yêu cầu mở tài khoản trên mã 4040000", "operationId": "waitVerifyContract", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitVerifyContractResponse"}}}}}}}, "/open-account/wait-capture-action/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "4.1. <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, enum trả về là CAPTURE", "operationId": "waitCaptureAction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitCaptureActionResponse"}}}}}}}, "/open-account/verify-otp/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "4. <PERSON><PERSON> <PERSON><PERSON><PERSON> OTP mở tài k<PERSON>n", "description": "API xác thực mã OTP của khách hàng để tiếp tục giao dịch mở thẻ.", "operationId": "verifyOtpToOpenAccount", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseVerifyOtpToOpenAccountResponse"}}}}}}}, "/open-account/submit-info/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "1.5 Api submit info", "description": "Api summit lai thong tin cua nguoi dung", "operationId": "submitInfo", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitInfoApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSubmitInfoResponse"}}}}}}}, "/open-account/submit-contract/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "5.1 Api submit contract", "description": "Api summit lai thong tin hợp đồng", "operationId": "submitContract", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitContractApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSubmitContractResponse"}}}}}}}, "/open-account/submit-avatar/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "1.6 Api submit avatar", "description": "Api summit lai thong tin cua nguoi dung", "operationId": "submitAvatar", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitAvatarApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSubmitAvatarResponse"}}}}}}}, "/open-account/start-open-account": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "1. <PERSON><PERSON> b<PERSON><PERSON> đầu mở tài k<PERSON>n", "description": "<PERSON><PERSON> nh<PERSON>n vào thông tin người dùng, ki<PERSON><PERSON> tra người dùng có tồn tại hồ sơ tại KS Bank hay chưa.\n  Nếu là người dùng mới thì lưu thông tin vào bảng tạm.\n  Nếu người dùng đã có tài <PERSON> (CMND, SĐT, Email tồn tại) thì trả về lỗi cho STM.", "operationId": "startOpenAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartOpenAccountRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStartOpenAccountResponse"}}}}}}}, "/open-account/send-otp/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "3. <PERSON><PERSON><PERSON><PERSON>", "description": "API yêu c<PERSON>u gửi OTP đến số điện thoại kh<PERSON>ch hàng.", "operationId": "sendOtpToOpenAccount", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSendOtpToOpenAccountResponse"}}}}}}}, "/open-account/create-account/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "5. API tạo tài k<PERSON>n", "description": "API tạo cif cho <PERSON><PERSON><PERSON> h<PERSON>, mapping số tài khoản được tạo với số thẻ.", "operationId": "createAccount", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAccountApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateAccountResponse"}}}}}}}, "/open-account/confirm-take-card/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "6. <PERSON> xác nhận người dùng đã lấy thẻ", "description": "API xác nhận người dùng đã lấy thẻ..", "operationId": "confirmTakeCard_1", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmTakeCardApiRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseConfirmTakeCardResponse"}}}}}}}, "/open-account/cancel/{transactionId}": {"post": {"tags": ["API mở tài k<PERSON>n qua STM"], "summary": "2.  <PERSON><PERSON> h<PERSON><PERSON> giao dịch tạo tài <PERSON>n", "description": "API giúp xóa bản ghi tạm thông tin người dùng khi hủy giao dịch mở tài khoản", "operationId": "cancelOpenAccount", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelOpenAccountResponse"}}}}}}}, "/media/upload": {"post": {"tags": ["API upload media cho STM"], "operationId": "upload", "parameters": [{"name": "bucketName", "in": "query", "required": false, "schema": {"type": "string", "default": "salt"}}, {"name": "fileType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["AVATAR", "IDCARD", "LIVENESS", "ICON", "COLLOCATION", "PROPOSAL", "STATEMENT", "SIGN", "OTHER"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}}, "security": []}}, "/bank/stm-deposit-cash": {"post": {"tags": ["BANK BUSINESS FOR STM API"], "operationId": "depositCash", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StmDepositCashRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStmDepositCashResponse"}}}}}}}, "/bank/operation/alarm": {"post": {"tags": ["BANK BUSINESS FOR STM API"], "summary": "3.4. [STM API] Alarm to Admin", "description": "<PERSON><PERSON> g<PERSON>i thông báo ATM bị can thiệp tr<PERSON>i phép (Vận h<PERSON>nh).", "operationId": "alarmOperation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmOperationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAlarmOperationResponse"}}}}}}}, "/bank/get-account-by-card-no": {"post": {"tags": ["BANK BUSINESS FOR STM API"], "operationId": "getAccountByCardNo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAccountByCardNoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAccountByCardNoResponse"}}}}}}}, "/bank/checkCardTypeByCardNo": {"post": {"tags": ["BANK BUSINESS FOR STM API"], "operationId": "checkCardTypeByCardNo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckCardTypeByCardNoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckCardTypeByCardNoResponse"}}}}}}}, "/qr-payment/wait-fulfil-template/{qrPaymentId}": {"get": {"tags": ["QR PAYMENT STM API"], "summary": "2. <PERSON><PERSON>, đ<PERSON>i người dùng chọn xong tài k<PERSON>n ", "description": "<PERSON><PERSON>i người dùng chọn xong tài <PERSON>ho<PERSON>, sau khi chọn xong tài khoản thì chạy luôn đến api hạch toán", "operationId": "waitScanQRDone", "parameters": [{"name": "qrPaymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitScanQRDoneResponse"}}}}}}}, "/open-card/wait-select-account/{transactionId}": {"get": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "5. [STM API] đợi mobile chọn tài khoản", "description": "<PERSON>pi này cho phép STM đợi thông tin mở tài khoản bao gồm số tài khoản mở thẻ, loại thẻ (bổ sung sau) và hiển thị lên màn hình STM luôn và đợi đến phần đợi thực hiện tạo thẻ.", "operationId": "waitSelectAccount", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitSelectAccountResponse"}}}}}}}, "/open-card/wait-scan-qr-code/{transactionId}": {"get": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "3. [STM API] đợi mobile scan qr QR code", "description": "Khi mobile scan QR code xong thì sẽ trả về thông tin cho máy STM là đã scan xong và thôngtin của người dùng, STM nhận được thì nên tắt cái màn mã QR đi và hiển thị thông báo cùng với hiệu ứng loading đợi, thông báo kiểu :Đang tiến hành mở thẻ cho khách hàng ABC ... (API này có thể sau sẽ update thêm là chọn loại thẻ phát hành nếu như có URD)", "operationId": "waitScanQr", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseWaitScanQrResponse"}}}}}}}, "/open-card/stm-info/{transactionId}": {"get": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "2. [Mobile API] <PERSON><PERSON><PERSON> thông tin của máy STM qua mã QR", "description": "Sau khi quét mã QR, mobile sẽ nhận được một deeplink trong đó có chứa action là mở thẻbằng mã QR và transactionId của luồng mở thẻ này, lấy transactionId đó post vào API này để lấythông tin máy STM và hiển thị thông tin máy STM cho người dùng sau khi quét.", "operationId": "getStmInfo_1", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetOpenCardDetailResponse"}}}}}}}, "/open-card/mobile/getCardProductSupportedOpen": {"get": {"tags": ["API Mở thẻ bằng mã QR"], "summary": "[Mobile] Mobile gọi danh sách thẻ STM hỗ trợ mở", "description": "Mobile gọi danh sách thẻ STM hỗ trợ mở", "operationId": "getCardProductSupportedOpen", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetCardProductSupportedOpenResponse"}}}}}}}, "/bank/transactions/withdrawal/limit-info": {"get": {"tags": ["BANK BUSINESS FOR STM API"], "summary": "3.2. [STM API] Get account limit", "description": "<PERSON><PERSON> l<PERSON>y thông tin về giới hạn giao dịch của thẻ/tài kho<PERSON>n", "operationId": "getAccountLimit", "parameters": [{"name": "number", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAccountLimitResponse"}}}}}}}, "/bank/transactions/receipt/fee": {"get": {"tags": ["BANK BUSINESS FOR STM API"], "summary": "3.3. [STM API] Get bill fee", "description": "<PERSON><PERSON> l<PERSON>y thông tin phí in hóa đơn.", "operationId": "getBillFee", "parameters": [{"name": "number", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "transactionType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetBillFeeResponse"}}}}}}}, "/bank/cards/active-status": {"get": {"tags": ["BANK BUSINESS FOR STM API"], "summary": "3.1. [STM API] Check active card", "description": "<PERSON><PERSON> kiểm tra thẻ có được active", "operationId": "checkCardActive", "parameters": [{"name": "cardNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckCardResponse"}}}}}}}}, "components": {"schemas": {"GetQRCodeRequest": {"type": "object", "properties": {"atmID": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}, "availableAmount": {"type": "number", "nullable": true, "x-nullable": true}}}, "BaseResponseGetQrCodeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetQrCodeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetQrCodeResponse": {"type": "object", "properties": {"qrCodeUrl": {"type": "string", "nullable": true, "x-nullable": true}, "qrPaymentId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseExecuteQRTransferResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ExecuteQRTransferResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "ExecuteQRTransferResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "printReceipt": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "ConfirmQRPaymentApiRequest": {"type": "object", "properties": {"confirmQRStatus": {"type": "string", "enum": ["SUCCESS", "FAILED"], "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseConfirmQRTransactionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ConfirmQRTransactionResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "ConfirmQRTransactionResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "atmId": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "printReceipt": {"type": "boolean", "nullable": true, "x-nullable": true}, "qrPaymentStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "EXECUTED", "COMPLETED", "CANCELED"], "nullable": true, "x-nullable": true}}}, "MapTemplateToSTMRequest": {"type": "object", "properties": {"atmId": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "printReceipt": {"type": "boolean", "nullable": true, "x-nullable": true}, "otp": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSetAmountCommandResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SetAmountCommandResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SetAmountCommandResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "atmId": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "qrPaymentStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "EXECUTED", "COMPLETED", "CANCELED"], "nullable": true, "x-nullable": true}, "transactionTime": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}}}, "BaseResponseExecuteQRTransferResponseV2": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ExecuteQRTransferResponseV2"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "ExecuteQRTransferResponseV2": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "printReceipt": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetQRPaymentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetQRPaymentResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetQRPaymentResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "atmId": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "qrPaymentStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "EXECUTED", "COMPLETED", "CANCELED"], "nullable": true, "x-nullable": true}}}, "BaseResponseConfirmQRTransactionResponseV2": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ConfirmQRTransactionResponseV2"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "ConfirmQRTransactionResponseV2": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "atmId": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "transactionAmount": {"type": "number", "nullable": true, "x-nullable": true}, "printReceipt": {"type": "boolean", "nullable": true, "x-nullable": true}, "qrPaymentStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "EXECUTED", "COMPLETED", "CANCELED"], "nullable": true, "x-nullable": true}}}, "BaseResponseCancelQRPaymentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelQRPaymentResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CancelQRPaymentResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "qrPaymentStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "EXECUTED", "COMPLETED", "CANCELED"], "nullable": true, "x-nullable": true}}}, "OpenCardQrRequest": {"type": "object", "properties": {"atmID": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseOpenCardQRResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/OpenCardQRResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "OpenCardQRResponse": {"type": "object", "properties": {"qrCodeUrl": {"type": "string", "nullable": true, "x-nullable": true}, "transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCancelOpenCardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelOpenCardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CancelOpenCardResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "ExecuteOpenCardApiRequest": {"type": "object", "properties": {"cardNumber": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseOpenCardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/OpenCardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "OpenCardResponse": {"type": "object", "properties": {"cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "cardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "openCardTime": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}}}, "ConfirmOpenCardApiRequest": {"type": "object", "properties": {"confirmTakeCardStatus": {"type": "string", "enum": ["SUCCESS", "FAILED"], "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseOpenCardConfirmTakeCardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/OpenCardConfirmTakeCardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "OpenCardConfirmTakeCardResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitCardInfoApiRequest": {"type": "object", "properties": {"cardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSubmitCardInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SubmitCardInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitCardInfoResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "MappingAccountApiRequest": {"type": "object", "properties": {"otp": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cardType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponseMappingAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MappingAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "MappingAccountResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseWaitVerifyPhotoStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitVerifyPhotoStatusResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitVerifyPhotoStatusResponse": {"type": "object", "properties": {"takePhotoStatus": {"type": "string", "enum": ["PENDING", "CAPTURE", "COMPLETED", "RECAPTURE"], "nullable": true, "x-nullable": true}}}, "BaseResponseWaitVerifyUserInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitVerifyUserInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitVerifyUserInfoResponse": {"type": "object", "properties": {"openAccountStatus": {"type": "string", "enum": ["PENDING", "VERIFIED_OTP", "VERIFIED_USER_INFO", "VERIFY_USER_INFO_FAILED", "VERIFIED_CONTRACT", "RESCAN_CONTRACT", "COMPLETED", "CANCELED", "REPRINT_CONTRACT"], "nullable": true, "x-nullable": true}, "agentId": {"type": "string", "nullable": true, "x-nullable": true}, "agentName": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseWaitVerifyContractResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitVerifyContractResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitVerifyContractResponse": {"type": "object", "properties": {"openAccountStatus": {"type": "string", "enum": ["PENDING", "VERIFIED_OTP", "VERIFIED_USER_INFO", "VERIFY_USER_INFO_FAILED", "VERIFIED_CONTRACT", "RESCAN_CONTRACT", "COMPLETED", "CANCELED", "REPRINT_CONTRACT"], "nullable": true, "x-nullable": true}}}, "BaseResponseWaitCaptureActionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitCaptureActionResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitCaptureActionResponse": {"type": "object", "properties": {"takePhotoStatus": {"type": "string", "enum": ["PENDING", "CAPTURE", "COMPLETED", "RECAPTURE"], "nullable": true, "x-nullable": true}}}, "VerifyOtpApiRequest": {"type": "object", "properties": {"otp": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseVerifyOtpToOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifyOtpToOpenAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "VerifyOtpToOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "SubmitInfoApiRequest": {"type": "object", "properties": {"idCardIssuePlc": {"type": "string", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "enum": ["M", "F"], "nullable": true, "x-nullable": true}, "idCardImageDown": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageUp": {"type": "string", "nullable": true, "x-nullable": true}, "city": {"type": "string", "nullable": true, "x-nullable": true}, "state": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddress": {"type": "string", "nullable": true, "x-nullable": true}, "permanentAddress": {"type": "string", "nullable": true, "x-nullable": true}, "issueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "nationality": {"type": "string", "enum": ["VN"], "nullable": true, "x-nullable": true}, "dob": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSubmitInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SubmitInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitInfoResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitContractApiRequest": {"type": "object", "properties": {"contractFormUp": {"type": "string", "nullable": true, "x-nullable": true}, "contractFormDown": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSubmitContractResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SubmitContractResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitContractResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitAvatarApiRequest": {"type": "object", "properties": {"avatar": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSubmitAvatarResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SubmitAvatarResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SubmitAvatarResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartOpenAccountRequest": {"type": "object", "properties": {"atmID": {"type": "string", "description": "Mã định danh ATM", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "description": "Địa chỉ ATM", "nullable": true, "x-nullable": true}, "idCardNumber": {"type": "string", "description": "Số CMND/CCCD", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "nullable": true, "x-nullable": true}, "email": {"type": "string", "description": "Đ<PERSON>a chỉ email", "nullable": true, "x-nullable": true}, "agentId": {"type": "string", "description": "Thông tin tổng đài viên thực hiện hướng dẫn mở thẻ", "nullable": true, "x-nullable": true}}}, "BaseResponseStartOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StartOpenAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "StartOpenAccountResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSendOtpToOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SendOtpToOpenAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "SendOtpToOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CreateAccountApiRequest": {"type": "object", "properties": {"idCardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "dob": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "gender": {"type": "string", "enum": ["M", "F"], "nullable": true, "x-nullable": true}, "nationality": {"type": "string", "enum": ["VN"], "nullable": true, "x-nullable": true}, "hometown": {"type": "string", "nullable": true, "x-nullable": true}, "city": {"type": "string", "nullable": true, "x-nullable": true}, "state": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssuePlc": {"type": "string", "nullable": true, "x-nullable": true}, "idCardIssueDt": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "permanentAddress": {"type": "string", "nullable": true, "x-nullable": true}, "contactAddress": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageUp": {"type": "string", "nullable": true, "x-nullable": true}, "idCardImageDown": {"type": "string", "nullable": true, "x-nullable": true}, "openAccountFormUp": {"type": "string", "nullable": true, "x-nullable": true}, "openAccountFormDown": {"type": "string", "nullable": true, "x-nullable": true}, "contractFormUp": {"type": "string", "nullable": true, "x-nullable": true}, "contractFormDown": {"type": "string", "nullable": true, "x-nullable": true}, "servicePack": {"type": "string", "nullable": true, "x-nullable": true}, "cardNumber": {"type": "string", "nullable": true, "x-nullable": true}, "issueDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "expirationDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "track1": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "isOpenCard": {"type": "boolean", "nullable": true, "x-nullable": true}, "openCard": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateAccountResponse": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "openCardMessage": {"type": "string", "nullable": true, "x-nullable": true}, "openCard": {"type": "boolean", "nullable": true, "x-nullable": true}, "openCardSuccess": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "ConfirmTakeCardApiRequest": {"type": "object", "properties": {"confirmTakeCardStatus": {"type": "string", "enum": ["SUCCESS", "FAILED"], "nullable": true, "x-nullable": true}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseConfirmTakeCardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ConfirmTakeCardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "ConfirmTakeCardResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true, "x-nullable": true}, "success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCancelOpenAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelOpenAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CancelOpenAccountResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "x-nullable": true}}}, "FileUploadResponse": {"type": "object", "properties": {"previewUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "TokenResponse": {"type": "object", "properties": {"access_token": {"type": "string", "nullable": true, "x-nullable": true}, "expires_in": {"type": "string", "nullable": true, "x-nullable": true}, "refresh_expires_in": {"type": "string", "nullable": true, "x-nullable": true}, "token_type": {"type": "string", "nullable": true, "x-nullable": true}, "scope": {"type": "string", "nullable": true, "x-nullable": true}}}, "StmDepositCashRequest": {"type": "object", "properties": {"svTransNoRef": {"type": "string", "description": "Mã giao d<PERSON><PERSON> (SmartVista) -> truyền vào CORE để map gd", "nullable": true, "x-nullable": true}, "stmId": {"type": "string", "nullable": true, "x-nullable": true}, "fromCardNo": {"type": "string", "nullable": true, "x-nullable": true}, "fromIdCard": {"type": "string", "nullable": true, "x-nullable": true}, "isMyAcct": {"type": "string", "nullable": true, "x-nullable": true}, "tranAmount": {"type": "string", "nullable": true, "x-nullable": true}, "descText": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseStmDepositCashResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StmDepositCashResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "StmDepositCashResponse": {"type": "object", "properties": {"accountBalance": {"type": "string", "nullable": true, "x-nullable": true}}}, "AlarmOperationRequest": {"type": "object", "properties": {"atmId": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}}}, "AlarmOperationResponse": {"type": "object", "properties": {"done": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseAlarmOperationResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/AlarmOperationResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetAccountByCardNoRequest": {"type": "object", "properties": {"stmId": {"type": "string", "nullable": true, "x-nullable": true}, "cardNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetAccountByCardNoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetAccountByCardNoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetAccountByCardNoResponse": {"type": "object", "properties": {"accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "userCode": {"type": "string", "nullable": true, "x-nullable": true}, "cardName": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckCardTypeByCardNoRequest": {"type": "object", "properties": {"cardNo": {"type": "string", "description": "Số thẻ", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckCardTypeByCardNoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckCardTypeByCardNoResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckCardTypeByCardNoResponse": {"type": "object", "properties": {"cardType": {"type": "string", "nullable": true, "x-nullable": true}, "canDeposit": {"type": "boolean", "nullable": true, "x-nullable": true}, "canWithdraw": {"type": "boolean", "nullable": true, "x-nullable": true}, "cardNo": {"type": "string", "nullable": true, "x-nullable": true}, "embossedName": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseWaitScanQRDoneResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitScanQRDoneResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitScanQRDoneResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "userName": {"type": "string", "nullable": true, "x-nullable": true}, "accountNumber": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseWaitSelectAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitSelectAccountResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitSelectAccountResponse": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cardType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponseWaitScanQrResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/WaitScanQrResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "WaitScanQrResponse": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetOpenCardDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetOpenCardDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetOpenCardDetailResponse": {"type": "object", "properties": {"atmID": {"type": "string", "nullable": true, "x-nullable": true}, "atmLocation": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetCardProductSupportedOpenResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetCardProductSupportedOpenResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CardProductSupportedOpen": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "cardType": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "iconURL": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetCardProductSupportedOpenResponse": {"type": "object", "properties": {"cardProduct": {"type": "array", "items": {"$ref": "#/components/schemas/CardProductSupportedOpen"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetAccountLimitResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetAccountLimitResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetAccountLimitResponse": {"type": "object", "properties": {"minAmount": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "maxAmountByService": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "maxAmountByUser": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "transactionsPerDay": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "remainTransactionsPerDay": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseGetBillFeeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetBillFeeResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "GetBillFeeResponse": {"type": "object", "properties": {"fee": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckCardResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckCardResponse"}, {"$ref": "#/components/schemas/NullType"}]}, "message": {"type": "string", "nullable": true, "x-nullable": true}}}, "CheckCardResponse": {"type": "object", "properties": {"active": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "NullType": {"description": "for adding nullability to a ref", "enum": [null]}}, "securitySchemes": {"Authorization": {"type": "http", "description": "Access token", "in": "header", "scheme": "bearer", "bearerFormat": "Bearer [token]"}}}}