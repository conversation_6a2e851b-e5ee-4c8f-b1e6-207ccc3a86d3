{"openapi": "3.0.1", "info": {"title": "MyShop API", "description": "Documentation MyShop API v1.0", "version": "1.0"}, "servers": [{"url": "http://localhost:80"}], "security": [{"Authorization": []}], "tags": [{"name": "API biến động số dư", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin biến động số dư"}, {"name": "API: <PERSON><PERSON><PERSON><PERSON> hiện việc đăng ký gói cước MyShop, MagicPaybox", "description": "<PERSON><PERSON><PERSON><PERSON> hiện việc đăng ký gói cước MyShop, MagicPaybox"}, {"name": "API quản lý kết nối Paybox tới My shop", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin kết nối Paybox với My shoph"}, {"name": "API Shop", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin Shop"}, {"name": "API: Đồng bộ data", "description": "Đồng bộ data"}, {"name": "API User referral", "description": "<PERSON><PERSON><PERSON><PERSON> lý mã giới thiệu"}, {"name": "API Industry", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin Ngành nghề cho Shop "}, {"name": "API quản lý giao d<PERSON>ch", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin giao dịch"}, {"name": "API: <PERSON><PERSON><PERSON> hóa đơn thu phí ", "description": "<PERSON><PERSON><PERSON> hóa đơn thu phí cho <PERSON> (chỉ dùng để test) "}, {"name": "API MagicPaybox", "description": "<PERSON><PERSON><PERSON><PERSON> lý gói c<PERSON><PERSON><PERSON> d<PERSON>ch vụ magic paybox"}, {"name": "API Package", "description": "<PERSON><PERSON><PERSON><PERSON> lý gói c<PERSON>"}, {"name": "API: <PERSON><PERSON> vụ My Shop", "description": "<PERSON><PERSON><PERSON><PERSON> lí thu phí dịch vụ"}], "paths": {"/api/shop/v1/updateStatusOrgShops": {"put": {"tags": ["API Shop"], "summary": "API khóa/mở khóa shop doanh nghiệp liên kết với doanh nghiệp", "description": "API khóa/mở khóa shop doanh nghiệp liên kết với doanh nghiệp", "operationId": "updateStatusOrgShops", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrgShopsStatusRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOrgShopsStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateOrgShopsStatusResponse"}}}}}}}, "/api/shop/v1/edit-nickname": {"put": {"tags": ["API Shop"], "summary": "API chỉnh sửa nickname", "description": "API chỉnh sửa nickname", "operationId": "editNickname", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditInForNickNameRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}}}}}, "/api/shop/v1/cancelRequestMagicPaybox": {"put": {"tags": ["API Shop"], "summary": "API Hủy gói MagicPaybox và unpair thiết bị ", "description": "API dùng để hủy gói Magic Paybox và unpair thiết bị đi kèm ", "operationId": "cancelRequestMagicPaybox", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelRequestMagicPayboxRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelRequestMagicPayboxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelRequestMagicPayboxResponse"}}}}}}}, "/api/packages/v1/cancel": {"put": {"tags": ["API Package"], "summary": "API hủy gói cước của tài khoản đang đăng nhập", "description": "API hủy gói cước của tài khoản đang đăng nhập", "operationId": "cancelPackagesOfCurrentUser", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelMyShopPackageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelMyShopPackageResponse"}}}}}}}, "/api/fee/v1/notifyPayment/off": {"put": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "<PERSON> thực hiện tắt thông báo thu phí", "description": "<PERSON> thực hiện tắt thông báo thu phí", "operationId": "offNotifyPayment", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/transfer/v1/request-transfer": {"post": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "API tạo yêu cầu thanh toán", "description": "API tạo yêu cầu thanh toán", "operationId": "requestTransfer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRequestTransferRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateRequestTransferResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateRequestTransferResponse"}}}}}}}, "/api/transfer/internal/v1/saveTransHis": {"post": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "API lưu lại lịch sử giao dịch của shop", "description": "Khi IBFT gửi request noti thì notify-service gọi hàm này để lưu lại lịch sử giao dịch của Shop", "operationId": "saveTransHis", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveTransHisRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveTransHisResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSaveTransHisResponse"}}}}}}}, "/api/sync/v1/internal/user-my-shop": {"post": {"tags": ["API: Đồng bộ data"], "summary": "API đồng bộ thông tin người dùng myshop", "description": "API đồng bộ thông tin người dùng myshop", "operationId": "syncUserMyShop", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/sync/v1/internal/branch-code": {"post": {"tags": ["API: Đồng bộ data"], "summary": "API đồng bộ branch code", "description": "API đồng bộ branch code", "operationId": "syncBranchCodeShop", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/shop/v1/updateShopInfo": {"post": {"tags": ["API Shop"], "summary": "API update thông tin Shop", "description": "API update thông tin Shop", "operationId": "updateShopInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShopInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateShopInfoResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUpdateShopInfoResponse"}}}}}}}, "/api/shop/v1/setting_allow_join/{id}": {"post": {"tags": ["API Shop"], "summary": "API bật tắt yêu cầu tham gia shop", "description": "API bật tắt yêu cầu tham gia shop", "operationId": "settingAllowRequest", "parameters": [{"name": "id", "in": "path", "description": "Id của shop c<PERSON>n bật tắt yêu cầu", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingAllowJoinShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingAllowJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingAllowJoinShopResponse"}}}}}}}, "/api/shop/v1/settingNotifyTransferShop": {"post": {"tags": ["API Shop"], "summary": "API cài đặt thông báo giao dịch của shop cho chủ shop và nhân viên", "description": "API cài đặt thông báo giao dịch của shop cho chủ shop và nhân viên", "operationId": "settingNotifyTransferShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingNotifyTransferShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingNotifyTransferResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingNotifyTransferResponse"}}}}}}}, "/api/shop/v1/rejectRequestToJoinShop": {"post": {"tags": ["API Shop"], "summary": "API từ chối yêu cầu tham gia Shop", "description": "API từ chối yêu cầu tham gia Shop", "operationId": "rejectRequestToJoinShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectRequestToJoinShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRejectRequestToJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRejectRequestToJoinShopResponse"}}}}}}}, "/api/shop/v1/leave_shop": {"post": {"tags": ["API Shop"], "summary": "API rời shop", "description": "API rời shop", "operationId": "leaveShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseLeaveShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseLeaveShopResponse"}}}}}}}, "/api/shop/v1/internal/rejectShop/{nickname}": {"post": {"tags": ["API Shop"], "summary": "API từ chối Shop từ nickname", "description": "API từ chối Shop từ nickname", "operationId": "rejectShop", "parameters": [{"name": "nickname", "in": "path", "description": "Id của shop c<PERSON><PERSON>", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectShopForNicknameRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveShopResponse"}}}}}}}, "/api/shop/v1/internal/approveShop/{id}": {"post": {"tags": ["API Shop"], "summary": "API duyệt Shop", "description": "API duyệt Shop", "operationId": "approveShop", "parameters": [{"name": "id", "in": "path", "description": "Id của shop c<PERSON><PERSON>", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveShopResponse"}}}}}}}, "/api/shop/v1/deleteUserFromShop": {"post": {"tags": ["API Shop"], "summary": "API xóa user khỏi Shop", "description": "API xóa user khỏi Shop", "operationId": "deleteUserFromShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserFromShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteUserFromShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteUserFromShopResponse"}}}}}}}, "/api/shop/v1/create": {"post": {"tags": ["API Shop"], "summary": "API tạo Shop", "description": "API tạo Shop", "operationId": "createShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterShopResponse"}}}}}}}, "/api/shop/v1/createRequestToJoinShop": {"post": {"tags": ["API Shop"], "summary": "API tạo yêu cầu tham gia Shop", "description": "API tạo yêu cầu tham gia Shop", "operationId": "createRequestToJoinShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRequestToJoinShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateRequestToJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateRequestToJoinShopResponse"}}}}}}}, "/api/shop/v1/createDefaultTransferDescShop": {"post": {"tags": ["API Shop"], "summary": "API Tạo nội dung chuyển tiền mặc định cho Shop", "description": "API Tạo nội dung chuyển tiền mặc định cho Shop", "operationId": "createDefaultTransferDescShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDefaultTransferDescShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateDefaultTransferDescShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCreateDefaultTransferDescShopResponse"}}}}}}}, "/api/shop/v1/cancelRequestToJoinShop": {"post": {"tags": ["API Shop"], "summary": "API hủy yêu cầu tham gia Shop", "description": "API hủy yêu cầu tham gia Shop", "operationId": "cancelRequestToJoinShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelRequestToJoinShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelRequestToJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCancelRequestToJoinShopResponse"}}}}}}}, "/api/shop/v1/approveRequestToJoinShop": {"post": {"tags": ["API Shop"], "summary": "API duyệt yêu cầu tham gia Shop", "description": "API duyệt yêu cầu tham gia Shop", "operationId": "approveRequestToJoinShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveRequestToJoinShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveRequestToJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseApproveRequestToJoinShopResponse"}}}}}}}, "/api/shop/v1/addEmailShop": {"post": {"tags": ["API Shop"], "summary": "API thêm email shop", "description": "API thêm email shop", "operationId": "addEmailShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddShopEmailRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAddShopEmailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAddShopEmailResponse"}}}}}}}, "/api/paybox/v1/unPair": {"post": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API tạo yêu cầu ng<PERSON>t kết nối", "description": "API tạo yêu cầu ngắt kết nối PayBox", "operationId": "unPair", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnPairPayBoxShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUnPairPayBoxShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseUnPairPayBoxShopResponse"}}}}}}}, "/api/paybox/v1/setStatusShopPaybox": {"post": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API bật tắt trạng thái hoạt động của Paybox", "description": "<PERSON>hi tắt trạng thái hoạt động thì Paybox sẽ không nhận được thông tin chuyển tiền từ app", "operationId": "settingStatusShopPaybox", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingStatusShopPayboxRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingStatusShopPayboxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSettingStatusShopPayboxResponse"}}}}}}}, "/api/paybox/v1/pair": {"post": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API tạo yêu cầu kết nối", "description": "API tạo yêu cầu kết nối PayBox", "operationId": "pair", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PairPayBoxShopRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePairPayBoxShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePairPayBoxShopResponse"}}}}}}}, "/api/paybox/v1/notifyTransaction": {"post": {"tags": ["API quản lý kết nối Paybox tới My shop"], "operationId": "notifyTransaction", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyTransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseNotifyTransactionResponse"}}}}}}}, "/api/packages/v1/register": {"post": {"tags": ["API Package"], "summary": "API đăng ký gói cước", "description": "API đăng ký gói cước", "operationId": "registerPackages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterMyShopPackageRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterMyShopPackageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterMyShopPackageResponse"}}}}}}}, "/api/packages/v1/changeMagicPackage": {"post": {"tags": ["API: <PERSON><PERSON><PERSON><PERSON> hiện việc đăng ký gói cước MyShop, MagicPaybox"], "summary": "API đổi gói c<PERSON><PERSON> dành cho magicPaybox", "description": "API đổi gói cước Magic Paybox ", "operationId": "changeMagicPayboxPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeMagicPayboxPackageRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseChangeMagicPayboxPackageReponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseChangeMagicPayboxPackageReponse"}}}}}}}, "/api/invoice/v1/internal/generateInvoice": {"post": {"tags": ["API: <PERSON><PERSON><PERSON> hóa đơn thu phí "], "summary": "<PERSON><PERSON><PERSON><PERSON> hiện sinh hóa đơn thu phí định kỳ", "operationId": "generateInvoiceJob", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/invoice/v1/internal/executeFee": {"post": {"tags": ["API: <PERSON><PERSON><PERSON> hóa đơn thu phí "], "summary": "<PERSON><PERSON><PERSON><PERSON> hiện thanh to<PERSON> hóa đơn", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/fee/v1/payment": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "<PERSON> thực hiện thanh toán hoá đơn", "description": "<PERSON> thực hiện thanh toán hoá đơn", "operationId": "paymentFee", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserExecutePaymentRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/fee/v1/payment/register": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "<PERSON> thực hiện thanh toán và đăng ký gói", "description": "<PERSON> thực hiện thanh toán và đăng ký gói", "operationId": "paymentFeeRegister", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserExecutePaymentResgisterRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterMyShopPackageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseRegisterMyShopPackageResponse"}}}}}}}, "/api/fee/v1/internal/update-service": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "<PERSON> thực hiện cập nhật gói cước cho khách hàng đã thanh toán hoá đơn - service internal gọi", "description": "<PERSON> thực hiện cập nhật gói cước cho khách hàng đã thanh toán hoá đơn - service internal gọi", "operationId": "executeUpdateService", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/fee/v1/internal/unblock/{cifNo}": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API thực hiện mở khoá sử dụng dịch vụ - service internal gọi", "description": "API thực hiện mở khoá sử dụng dịch vụ - service internal gọi", "operationId": "unBlockService", "parameters": [{"name": "cifNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/fee/v1/internal/startJob": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API thực hiện thanh toán hoá đơn - service internal gọi", "description": "API thực hiện thanh toán hoá đơn - service internal gọi", "operationId": "startJobCalculateFee", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartJobCalculateFeeRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/fee/v1/internal/startJobBlock": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API thực hiện kho<PERSON> sử dụng dịch vụ - service internal gọi", "description": "API thực hiện kho<PERSON> sử dụng dịch vụ - service internal gọi", "operationId": "startJobBlockService", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/fee/v1/internal/payment": {"post": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API thực hiện thanh toán hoá đơn - service internal gọi", "description": "API thực hiện thanh toán hoá đơn - service internal gọi", "operationId": "executePayment", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/balance-change/v1": {"post": {"tags": ["API biến động số dư"], "summary": "API nhận biến động số dư từ IBFT", "description": "API nhận biến động số dư từ IBFT", "operationId": "balanceChangeIBFT", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceChangeIbftRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBalanceChangeIbftResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseBalanceChangeIbftResponse"}}}}}}}, "/api/userReferrals/v1/checkExistedReferral": {"get": {"tags": ["API User referral"], "summary": "API kiểm tra shop shop đã nhập mã giới thiệu ?", "description": "API kiểm tra shop shop đã nhập mã giới thiệu ?", "operationId": "checkExistedReferral", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckExistedReferralResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckExistedReferralResponse"}}}}}}}, "/api/userReferrals/v1/checkCanUseCreateShop": {"get": {"tags": ["API User referral"], "summary": "API kiểm tra mã giới thiệu hợp lệ để đăng kí tạo shop", "description": "API kiểm tra mã giới thiệu hợp lệ để đăng kí tạo shop", "operationId": "checkReferralCanUseCreateShop", "parameters": [{"name": "referralCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckReferralCanCreateShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckReferralCanCreateShopResponse"}}}}}}}, "/api/transfer/v1/transactionDetail/{transactionId}": {"get": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết lịch sử giao dịch", "operationId": "getTransactionDetail", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetDetailTransactionResponse"}}}}}}}, "/api/transfer/v1/internal/getTransHisInfoShop/{shopId}": {"get": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "API lấy danh sách giao dich cua Shop cho CMS", "description": "API lấy danh sách giao dich cua Shop cho CMS", "operationId": "getTransHisInfoShopForCms", "parameters": [{"name": "shopId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "toDate", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetTransHisInfoShopForCmsResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetTransHisInfoShopForCmsResponse"}}}}}}}, "/api/transfer/v1/internal/getDetailTransfer/{id}": {"get": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "Lấy detail yêu cầu chuyển tiền theo id", "operationId": "getDetailTransferRequest", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseTransferRequestDetailResponse"}}}}}}}, "/api/transfer/v1/getTransHisInfoShop": {"get": {"tags": ["API quản lý giao d<PERSON>ch"], "summary": "API lấy danh sách giao dich cua Shop", "description": "API lấy danh sách giao dich cua Shop", "operationId": "getTransHisInfoShop", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "toDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetTransHisInfoShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetTransHisInfoShopResponse"}}}}}}}, "/api/shop/v1/sendEmailHistoryTransShop": {"get": {"tags": ["API Shop"], "summary": "API gửi lịch sử giao dịch shop qua email", "description": "API gửi lịch sử giao dịch shop qua email", "operationId": "sendEmailHistoryTransShop", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "shopEmailIds", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "deviceName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": true, "schema": {"type": "string"}}, {"name": "toDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": true, "schema": {"type": "string"}}, {"name": "otp", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSendMailHistoryTransResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseSendMailHistoryTransResponse"}}}}}}}, "/api/shop/v1/printHistoryTransShop": {"get": {"tags": ["API Shop"], "summary": "API in lịch sử giao dịch shop", "description": "API in lịch sử giao dịch shop", "operationId": "printHistoryTransShop", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "description": "format dd/MM/yyyy", "required": true, "schema": {"type": "string"}}, {"name": "toDate", "in": "query", "description": "format dd/MM/yyyy", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/shop/v1/internal/shops/org/list": {"get": {"tags": ["API Shop"], "summary": "Lấy list shop đư<PERSON><PERSON> liên kết với do<PERSON>h nghiệp", "description": "Lấy list shop đư<PERSON><PERSON> liên kết với do<PERSON>h nghiệp", "operationId": "orgGetListShop", "parameters": [{"name": "orgCifNumber", "in": "query", "description": "Cif number của org", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOrgGetListShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseOrgGetListShopResponse"}}}}}}}, "/api/shop/v1/internal/shopDetail/{id}": {"get": {"tags": ["API Shop"], "summary": "API lấy chi tiết shop", "description": "API lấy chi tiết shop", "operationId": "getShopDetail", "parameters": [{"name": "id", "in": "path", "description": "Id shop", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailResponse"}}}}}}}, "/api/shop/v1/internal/shareInfoDetail/{id}": {"get": {"tags": ["API Shop"], "summary": "API lấy chi tiết thông tin chia sẻ", "description": "API lấy chi tiết thông tin chia sẻ", "operationId": "getShareInfoDetail", "parameters": [{"name": "id", "in": "path", "description": "Id share info user", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShareInfoDetailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShareInfoDetailResponse"}}}}}}}, "/api/shop/v1/internal/printHistoryTransShop": {"get": {"tags": ["API Shop"], "summary": "API in lịch sử giao dịch shop cho cms", "description": "API in lịch sử giao dịch shop cho cms", "operationId": "printHistoryTransShopForCms", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "description": "format dd/MM/yyyy", "required": true, "schema": {"type": "string"}}, {"name": "toDate", "in": "query", "description": "format dd/MM/yyyy", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/shop/v1/internal/getUsersShopByNickname": {"get": {"tags": ["API Shop"], "summary": "API Internal lấy thông tin uses của shop bằng nicknameShop", "description": "API Internal lấy thông tin uses của shop bằng nicknameShop", "operationId": "getUsersShopByNickname", "parameters": [{"name": "nickname", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUsersInfoByNicknameInternalResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUsersInfoByNicknameInternalResponse"}}}}}}}, "/api/shop/v1/internal/getUsersShopByNicknameAndTrans": {"get": {"tags": ["API Shop"], "summary": "API lấy danh thành viên trong shop theo nick name, request tranfer để notify tới user shop theo trạng thái cài đặt thông báo của shop", "description": "API lấy danh thành viên trong shop theo nick name, request tranfer để notify tới user shop theo trạng thái cài đặt thông báo của shop", "operationId": "getUsersShopByNicknameAndTrans", "parameters": [{"name": "nickname", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "transDesc", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUsersInfoByNicknameInternalResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetUsersInfoByNicknameInternalResponse"}}}}}}}, "/api/shop/v1/internal/getListShop": {"get": {"tags": ["API Shop"], "summary": "API quản lý shop", "description": "API quản lý shop", "operationId": "getListShop", "parameters": [{"name": "shopName", "in": "query", "description": "Tên shop", "required": false, "schema": {"type": "string"}}, {"name": "shopCode", "in": "query", "description": "Mã shop", "required": false, "schema": {"type": "string"}}, {"name": "ownerPhoneNo", "in": "query", "description": "Số điện thoại chủ shop", "required": false, "schema": {"type": "string"}}, {"name": "branchId", "in": "query", "description": "<PERSON> n<PERSON>h", "required": false, "schema": {"type": "string"}}, {"name": "shopStatus", "in": "query", "description": "ACTIVE: <PERSON><PERSON><PERSON>, INACTIVE: <PERSON><PERSON> hó<PERSON>,DELETED: <PERSON><PERSON> xóa", "required": false, "schema": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"]}}, {"name": "postInspectionStatus", "in": "query", "description": "APPROVED: <PERSON><PERSON><PERSON> kiểm thành công, REJECTED: <PERSON><PERSON><PERSON> kiểm bị từ chối, WAITING: <PERSON><PERSON> hậu kiểm", "required": false, "schema": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"]}}, {"name": "editStatus", "in": "query", "description": "CREATED: <PERSON><PERSON> tạo, UPDATED: Đã chỉnh sửa", "required": false, "schema": {"type": "string", "enum": ["CREATED", "UPDATED"]}}, {"name": "customerType", "in": "query", "description": "PERSONAL: loại tk liên kết là cá nhân, ORGANIZATION: loại tài khoản liên kết là doanh nghiệp", "required": false, "schema": {"type": "string", "enum": ["PERSONAL", "ORGANIZATION"]}}, {"name": "createFromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "createToDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListShopResponse"}}}}}}}, "/api/shop/v1/internal/getListShopNoPage": {"get": {"tags": ["API Shop"], "summary": "API quản lý shop không paginate", "description": "API quản lý shop không paginate", "operationId": "getListShopNoPage", "parameters": [{"name": "shopName", "in": "query", "description": "Tên shop", "required": false, "schema": {"type": "string"}}, {"name": "shopCode", "in": "query", "description": "Mã shop", "required": false, "schema": {"type": "string"}}, {"name": "ownerPhoneNo", "in": "query", "description": "Số điện thoại chủ shop", "required": false, "schema": {"type": "string"}}, {"name": "branchId", "in": "query", "description": "<PERSON> n<PERSON>h", "required": false, "schema": {"type": "string"}}, {"name": "shopStatus", "in": "query", "description": "ACTIVE: <PERSON><PERSON><PERSON>, INACTIVE: <PERSON><PERSON> hó<PERSON>,DELETED: <PERSON><PERSON> xóa", "required": false, "schema": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"]}}, {"name": "postInspectionStatus", "in": "query", "description": "APPROVED: <PERSON><PERSON><PERSON> kiểm thành công, REJECTED: <PERSON><PERSON><PERSON> kiểm bị từ chối, WAITING: <PERSON><PERSON> hậu kiểm", "required": false, "schema": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"]}}, {"name": "editStatus", "in": "query", "description": "CREATED: <PERSON><PERSON> tạo, UPDATED: Đã chỉnh sửa", "required": false, "schema": {"type": "string", "enum": ["CREATED", "UPDATED"]}}, {"name": "customerType", "in": "query", "description": "PERSONAL: loại tk liên kết là cá nhân, ORGANIZATION: loại tài khoản liên kết là doanh nghiệp", "required": false, "schema": {"type": "string", "enum": ["PERSONAL", "ORGANIZATION"]}}, {"name": "createFromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "createToDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "transFromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "transToDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListShopNoPageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListShopNoPageResponse"}}}}}}}, "/api/shop/v1/internal/getListShareInfo": {"get": {"tags": ["API Shop"], "summary": "API quản lý quyền chia sẻ thông tin", "description": "API quản lý quyền chia sẻ thông tin", "operationId": "getListShareInfo", "parameters": [{"name": "shopCode", "in": "query", "description": "Mã shop", "required": false, "schema": {"type": "string"}}, {"name": "ownerPhoneNo", "in": "query", "description": "Số điện thoại chủ shop", "required": false, "schema": {"type": "string"}}, {"name": "staffPhoneNo", "in": "query", "description": "<PERSON><PERSON> điện thoại nhân viên", "required": false, "schema": {"type": "string"}}, {"name": "branchId", "in": "query", "description": "<PERSON> n<PERSON>h", "required": false, "schema": {"type": "string"}}, {"name": "userShopStatus", "in": "query", "description": "WAITING: <PERSON><PERSON> chia sẻ, ACTIVE: <PERSON><PERSON> chia sẻ, CANCEL_REQUEST, REJECTED_REQUEST, INACTIVE: <PERSON><PERSON> hủy", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"]}}}, {"name": "customerType", "in": "query", "description": "PERSONAL: loại tk liên kết là cá nhân, ORGANIZATION: loại tài khoản liên kết là doanh nghiệp", "required": false, "schema": {"type": "string", "enum": ["PERSONAL", "ORGANIZATION"]}}, {"name": "fromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "toDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListShareInfoResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListShareInfoResponse"}}}}}}}, "/api/shop/v1/internal/getListShareInfoNoPage": {"get": {"tags": ["API Shop"], "summary": "API quản lý quyền chia sẻ thông tin không paginate", "description": "API quản lý quyền chia sẻ thông tin không paginate", "operationId": "getListShareInfoNoPage", "parameters": [{"name": "shopCode", "in": "query", "description": "Mã shop", "required": false, "schema": {"type": "string"}}, {"name": "ownerPhoneNo", "in": "query", "description": "Số điện thoại chủ shop", "required": false, "schema": {"type": "string"}}, {"name": "staffPhoneNo", "in": "query", "description": "<PERSON><PERSON> điện thoại nhân viên", "required": false, "schema": {"type": "string"}}, {"name": "branchId", "in": "query", "description": "<PERSON> n<PERSON>h", "required": false, "schema": {"type": "string"}}, {"name": "userShopStatus", "in": "query", "description": "WAITING: <PERSON><PERSON> chia sẻ, ACTIVE: <PERSON><PERSON> chia sẻ, CANCEL_REQUEST, REJECTED_REQUEST, INACTIVE: <PERSON><PERSON> hủy", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"]}}}, {"name": "customerType", "in": "query", "description": "PERSONAL: loại tk liên kết là cá nhân, ORGANIZATION: loại tài khoản liên kết là doanh nghiệp", "required": false, "schema": {"type": "string", "enum": ["PERSONAL", "ORGANIZATION"]}}, {"name": "fromDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "toDate", "in": "query", "description": "Format: dd/MM/yyyy", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListShareInfoNoPageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListShareInfoNoPageResponse"}}}}}}}, "/api/shop/v1/internal/checkExistAccountNoLinkToShop": {"get": {"tags": ["API Shop"], "summary": "API Internal kiểm tra có Shop nào đc mở với số TK hay không?", "description": "API Internal kiểm tra có Shop nào đc mở với số TK hay không?", "operationId": "checkExistAccountNoLinkToShop", "parameters": [{"name": "accountNo", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckExistAccountNoLinkToShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckExistAccountNoLinkToShopResponse"}}}}}}}, "/api/shop/v1/getShopsUserCreatedOrJoined": {"get": {"tags": ["API Shop"], "summary": "API danh sách shop (ACTIVE, INACTIVE) mà KH đang đăng nhập đã tạo, đã tham gia, chờ tham gia", "description": "API danh sách shop mà KH đang đăng nhập đã tạo, đã tham gia, chờ tham gia", "operationId": "getShopsUserCreatedOrJoined", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetShopsUserCreatedOrJoinedResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetShopsUserCreatedOrJoinedResponse"}}}}}}}, "/api/shop/v1/getShopsActivatedButNotLinkedToPayBox": {"get": {"tags": ["API Shop"], "summary": "API danh sách shop (ACTIVE) nhưng chưa liên kết PayBox", "description": "API danh sách shop mà KH đang đăng nhập đã tạo nhưng chưa liên kết PayBox", "operationId": "getShopActivatedButNotConnectedPayBoxRequest", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetShopsActivatedButNotLinkedToPayBoxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetShopsActivatedButNotLinkedToPayBoxResponse"}}}}}}}, "/api/shop/v1/getListUser": {"get": {"tags": ["API Shop"], "summary": "API lấy danh sách User(cả User đã đư<PERSON><PERSON> duyệt và chưa được duyệt)", "description": "API lấy danh sách User(cả User đã đư<PERSON><PERSON> duyệt và chưa được duyệt)", "operationId": "getListUsers", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListUserResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListUserResponse"}}}}}}}, "/api/shop/v1/getListEmailOfShop": {"get": {"tags": ["API Shop"], "summary": "API lấy danh sách emai của shop", "description": "API lấy danh sách emai của shop", "operationId": "getListEmailOfShop", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "email", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListEmailOfShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListEmailOfShopResponse"}}}}}}}, "/api/shop/v1/getListEmailOfShopPage": {"get": {"tags": ["API Shop"], "summary": "API lấy danh sách emai của shop phân trang", "description": "API lấy danh sách emai của shop phân trang", "operationId": "getListEmailOfShopPage", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "email", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListEmailShopPageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetListEmailShopPageResponse"}}}}}}}, "/api/shop/v1/getAccountsOrg": {"get": {"tags": ["API Shop"], "summary": "API lấy danh sách TKTT doanh nghiệp đ<PERSON><PERSON><PERSON> liên kết với cá nhân", "description": "API lấy danh sách TKTT doanh nghiệp đ<PERSON><PERSON><PERSON> liên kết với cá nhân", "operationId": "getAccountsOrg", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAccountsOrgLinkPersonResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAccountsOrgLinkPersonResponse"}}}}}}}, "/api/shop/v1/detail": {"get": {"tags": ["API Shop"], "summary": "API lấy chi tiết Shop theo ShopId", "description": "API lấy chi tiết Shop theo ShopId", "operationId": "getShopDetailByShopId", "parameters": [{"name": "shopId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "shopCode", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailByShopIdResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailByShopIdResponse"}}}}}}}, "/api/shop/v1/detailShopRecent": {"get": {"tags": ["API Shop"], "summary": "API lấy thông tin chi tiết shop user t<PERSON>y cập gần nh<PERSON>t", "description": "API lấy thông tin chi tiết shop user t<PERSON>y cập gần nh<PERSON>t", "operationId": "getShopDetailRecent", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailRecentResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetShopDetailRecentResponse"}}}}}}}, "/api/shop/v1/checkValidNickname": {"get": {"tags": ["API Shop"], "summary": "API kiểm tra nickname đã có trên hệ thống hay chưa?", "description": "API kiểm tra nickname đã có trên hệ thống hay chưa?", "operationId": "checkValidNickname", "parameters": [{"name": "nickname", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckValidNicknameResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckValidNicknameResponse"}}}}}}}, "/api/shop/v1/checkUserCreatedOrJoinShop": {"get": {"tags": ["API Shop"], "summary": "API kiểm tra user đang đăng nhập đã có Shop hoặc join vào Shop nào hay chưa?", "description": "API kiểm tra user đang đăng nhập đã có Shop hoặc join vào Shop nào hay chưa?", "operationId": "checkUserCreatedOrJoinShop", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserCreatedOrJoinShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckUserCreatedOrJoinShopResponse"}}}}}}}, "/api/shop/v1/checkShopName": {"get": {"tags": ["API Shop"], "summary": "API kiểm tra shopname", "description": "API kiểm tra shopname", "operationId": "checkShopName", "parameters": [{"name": "shopName", "in": "query", "description": "Shop name của shop c<PERSON><PERSON>", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckValidShopnameResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckValidShopnameResponse"}}}}}}}, "/api/shop/v1/checkShopCodeExist": {"get": {"tags": ["API Shop"], "summary": "API kiểm tra shopCode đã có trên hệ thống hay chưa?", "description": "API kiểm tra shopCode đã có trên hệ thống hay chưa?", "operationId": "checkShopCodeExist", "parameters": [{"name": "shopCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckShopCodeExistResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckShopCodeExistResponse"}}}}}}}, "/api/shop/v1/checkLinkWithOrg": {"get": {"tags": ["API Shop"], "summary": "API Kiểm tra tài khoản cá nhân có liên kết với doanh nghiệp ?", "description": "API Kiểm tra tài khoản cá nhân có liên kết với doanh nghiệp ?", "operationId": "checkLinkWithOrg", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckLinkWithOrgResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckLinkWithOrgResponse"}}}}}}}, "/api/shop/v1/checkCanCreateShop": {"get": {"tags": ["API Shop"], "summary": "API kiểm tra có thể tạo thêm shop hay không", "description": "API kiểm tra có thể tạo thêm shop hay không", "operationId": "checkCanCreateShop", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckCanCreateShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckCanCreateShopResponse"}}}}}}}, "/api/paybox/v1/type/list": {"get": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API Lấy nhóm loại paybox theo loại thiết bị  ", "description": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> loại paybox theo loại thiết bị ", "operationId": "getPayboxTypeList", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListDevicePayboxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListDevicePayboxResponse"}}}}}}}, "/api/paybox/v1/model/list": {"get": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API lấy danh sách các thiết bị Paybox theo lo<PERSON> (QR Động, QR Tĩnh)  ", "description": "API lấy danh sách các thiết bị Paybox theo lo<PERSON> (QR Động, QR Tĩnh) ", "operationId": "getPayboxModelList", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListAllPayboxModelResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListAllPayboxModelResponse"}}}}}}}, "/api/paybox/v1/listPayBox": {"get": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API lấy danh sách thông tin thiết bị PayBox", "description": "API lấy danh sách thông tin thiết bị PayBox", "operationId": "getAllPayBox", "parameters": [{"name": "shopId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAllPayBoxOfShopResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetAllPayBoxOfShopResponse"}}}}}}}, "/api/paybox/v1/devicePayBox": {"get": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "API lưu thông tin thiết bị PayBox", "description": "API lưu tất cả thông tin thiết bị PayBox", "operationId": "device", "parameters": [{"name": "deviceId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetDevicePayBoxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetDevicePayBoxResponse"}}}}}}}, "/api/paybox/v1/checkDevice": {"get": {"tags": ["API quản lý kết nối Paybox tới My shop"], "summary": "<PERSON> kiểm tra thiết bị đã bán đứt chưa", "description": "API kiểm tra thiết bị đã bán đứt chưa. Th chưa bán đứt trả về thêm danh sách gói magic paybox đang sử dụng chưa liên kết", "operationId": "checkDevice", "parameters": [{"name": "serialNumber", "in": "query", "description": "<PERSON><PERSON> th<PERSON><PERSON><PERSON> bị <PERSON>", "required": true, "schema": {"type": "string"}}, {"name": "model", "in": "query", "description": "Model thi<PERSON><PERSON> bị Paybox", "required": true, "schema": {"type": "string", "description": "Model thi<PERSON><PERSON> bị Paybox"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckDevicePurchasedResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseCheckDevicePurchasedResponse"}}}}}}}, "/api/packages/v1/{id}/detail": {"get": {"tags": ["API Package"], "summary": "API lấy thông tin chi tiết của gói cước bằng id", "description": "API lấy thông tin chi tiết của gói cước bằng id", "operationId": "getPackageById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "magicPackageId", "in": "query", "description": "ID của gói magic paybox", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetMyShopPackageByIdResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetMyShopPackageByIdResponse"}}}}}}}, "/api/packages/v1/register/detail": {"get": {"tags": ["API Package"], "summary": "API lấy thông tin đăng ký gói cước của tài khoản đang đăng nhập", "description": "API lấy thông tin đăng ký gói cước của tài khoản đang đăng nhập", "operationId": "getRegisterPackageDetail", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetRegisterPackagesDetailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetRegisterPackagesDetailResponse"}}}}}}}, "/api/packages/v1/list": {"get": {"tags": ["API Package"], "summary": "API lấy danh sách thông tin gói cước", "description": "API lấy danh sách thông tin gói cước", "operationId": "getListPackage", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListMyShopPackageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListMyShopPackageResponse"}}}}}}}, "/api/magicPaybox/v1/register/list": {"get": {"tags": ["API MagicPaybox"], "summary": "API lấy thông tin list gói cước magic paybox của tài khoản đang đăng nhập", "description": "API lấy thông tin list gói cước magic paybox của tài khoản đang đăng nhập", "operationId": "getRegisterPackageMagicPayboxList", "parameters": [{"name": "userPlanId", "in": "query", "description": "ID của user plan", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Trạng thái ACTIVE: đang sử dụng", "required": true, "schema": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID"]}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListRegisterMagicPayboxResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListRegisterMagicPayboxResponse"}}}}}}}, "/api/magicPaybox/v1/list": {"get": {"tags": ["API MagicPaybox"], "summary": "API lấy danh sách thông tin gói cước dịch vụ magic paybox", "description": "API lấy danh sách thông tin gói cước dịch vụ magic paybox", "operationId": "getMagicPackageList", "parameters": [{"name": "payboxModelId", "in": "query", "description": "ID của paybox model", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListMagicPackageResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListMagicPackageResponse"}}}}}}}, "/api/magicPaybox/v1/detail/{id}": {"get": {"tags": ["API MagicPaybox"], "summary": "API lấy  thông tin gói cước dịch vụ magic paybox bằng id", "description": "API lấy thông tin gói cước dịch vụ magic paybox bằng id", "operationId": "getMagicPackageDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetMagicPackageDetailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetMagicPackageDetailResponse"}}}}}}}, "/api/magicPaybox/v1/checkCancel": {"get": {"tags": ["API MagicPaybox"], "summary": "API kiểm tra điều kiện huỷ gói magic paybox", "description": "API kiểm tra điều kiện huỷ gói magic paybox", "operationId": "checkCancelOrChange", "parameters": [{"name": "magicPlanId", "in": "query", "description": "ID của magic paybox user plan khách hàng muốn huỷ", "required": false, "schema": {"type": "string"}}, {"name": "planId", "in": "query", "description": "ID cua gói MYSHOP: TIDE, UNICORN,... kh<PERSON>ch hàng muốn đ<PERSON>i sang", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseStatusResponse"}}}}}}}, "/api/industry/v1/list": {"get": {"tags": ["API Industry"], "summary": "API Lấy thông tin danh sách các ngành nghề, lãnh vực ho<PERSON>t động", "description": "API Lấy thông tin danh sách các ngành nghề, lãnh vực ho<PERSON>t động", "operationId": "getListIndustry", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListIndustryResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetListIndustryResponse"}}}}}}}, "/api/fee/v1/notifyPayment/show": {"get": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API lấy thông tin chi tiết hoá đơn", "description": "API lấy thông tin chi tiết hoá đơn", "operationId": "showNotifyPayment", "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseShowNotifyPaymentResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseShowNotifyPaymentResponse"}}}}}}}, "/api/fee/v1/invoice/{invoiceId}": {"get": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API lấy thông tin chi tiết hoá đơn", "description": "API lấy thông tin chi tiết hoá đơn", "operationId": "previewInvoicePayment", "parameters": [{"name": "invoiceId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePreviewInvoiceResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePreviewInvoiceResponse"}}}}}}}, "/api/fee/v1/internal/invoices": {"get": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API lấy thông tin báo cáo phí", "description": "API lấy thông tin báo cáo phí", "operationId": "getListReportFee", "parameters": [{"name": "cifNo", "in": "query", "description": "Mã cif khách hàng", "required": false, "schema": {"type": "string", "description": "Mã cif khách hàng"}}, {"name": "phone", "in": "query", "description": "<PERSON><PERSON> điện tho<PERSON><PERSON> kh<PERSON>ch hàng", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON><PERSON> kh<PERSON>ch hàng"}}, {"name": "branchCode", "in": "query", "description": "Mã đơn vị ", "required": false, "schema": {"type": "string", "description": "Mã đơn vị "}}, {"name": "paymentStatus", "in": "query", "description": "Tr<PERSON>ng thái thanh toán phí: PAID - <PERSON><PERSON> toán, UNPAID - Chưa thanh toán", "required": false, "schema": {"type": "string", "description": "Tr<PERSON>ng thái thanh toán phí: PAID - <PERSON><PERSON> toán, UNPAID - Chưa thanh toán", "enum": ["PAID", "UNPAID"]}}, {"name": "serviceStatus", "in": "query", "description": "Trạng thái sử dụng dịch vụ: ACTIVE - đang sửa dụng, BLOCK_UNPAID - tạm kho<PERSON> do nợ phí", "required": false, "schema": {"type": "string", "description": "Trạng thái sử dụng dịch vụ: ACTIVE - đang sửa dụng, BLOCK_UNPAID - tạm kho<PERSON> do nợ phí", "enum": ["ACTIVE", "BLOCK_UNPAID"]}}, {"name": "registerFromDate", "in": "query", "description": "<PERSON>h<PERSON><PERSON> gian đăng ký gói - <PERSON><PERSON> ngày (Định dạng: dd/MM/yyyy)", "required": false, "schema": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian đăng ký gói - <PERSON><PERSON> ngày (Định dạng: dd/MM/yyyy)"}}, {"name": "registerToDate", "in": "query", "description": "<PERSON>h<PERSON><PERSON> gian đăng ký gói - <PERSON><PERSON><PERSON> ng<PERSON> (Định dạng: dd/MM/yyyy)", "required": false, "schema": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian đăng ký gói - <PERSON><PERSON><PERSON> ng<PERSON> (Định dạng: dd/MM/yyyy)"}}, {"name": "paymentFromDate", "in": "query", "description": "<PERSON>h<PERSON><PERSON> gian thanh to<PERSON> - <PERSON><PERSON> ng<PERSON> (Định dạng: dd/MM/yyyy)", "required": false, "schema": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian thanh to<PERSON> - <PERSON><PERSON> ng<PERSON> (Định dạng: dd/MM/yyyy)"}}, {"name": "paymentToDate", "in": "query", "description": "<PERSON>h<PERSON><PERSON> gian thanh toán - <PERSON><PERSON><PERSON> (Định dạng: dd/MM/yyyy)", "required": false, "schema": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian thanh toán - <PERSON><PERSON><PERSON> (Định dạng: dd/MM/yyyy)"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetReportFeeResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportGetReportFeeResponse"}}}}}}}, "/api/fee/v1/internal/invoices/{cifNo}": {"get": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API lấy thông tin chi tiết báo cáo phí người dùng", "description": "API lấy thông tin chi tiết báo cáo phí người dùng", "operationId": "getReportFeeDetail", "parameters": [{"name": "cifNo", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "transactionNo", "in": "query", "description": "Mã giao d<PERSON>ch", "required": false, "schema": {"type": "string", "description": "Mã giao d<PERSON>ch"}}, {"name": "rtxnNo", "in": "query", "description": "<PERSON><PERSON> hạch toán", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON> hạch toán"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["PAID", "UNPAID"]}}, {"name": "fromDate", "in": "query", "description": "Thời gian TT phí", "required": false, "schema": {"type": "string", "description": "Thời gian TT phí"}}, {"name": "toDate", "in": "query", "description": "Thời gian TT phí", "required": false, "schema": {"type": "string", "description": "Thời gian TT phí"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetReportFeeDetailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetReportFeeDetailResponse"}}}}}}}, "/api/fee/v1/getInvoices": {"get": {"tags": ["API: <PERSON><PERSON> vụ My Shop"], "summary": "API lấy danh sách thông tin hoá đơn", "description": "API lấy danh sách thông tin hoá đơn", "operationId": "getInvoices", "parameters": [{"name": "year", "in": "query", "description": "year", "required": false, "schema": {"minimum": 0, "type": "string", "description": "year"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> thái", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái", "enum": ["PAID", "UNPAID"]}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportInvoiceData"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponsePageSupportInvoiceData"}}}}}}}, "/api/shop/v1/deleteShop": {"delete": {"tags": ["API Shop"], "summary": "API xóa Shop", "description": "API xóa Shop", "operationId": "deleteShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteShopByIdRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteShopByIdResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteShopByIdResponse"}}}}}}}, "/api/shop/v1/deleteEmailShop": {"delete": {"tags": ["API Shop"], "summary": "API xóa email shop", "description": "API xóa email shop", "operationId": "deleteEmailShop", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteShopEmailRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteShopEmailResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseDeleteShopEmailResponse"}}}}}}}}, "components": {"schemas": {"UpdateOrgShopsStatusRequest": {"type": "object", "properties": {"personalCifNumber": {"type": "string", "nullable": true, "x-nullable": true}, "paymentAccounts": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateOrgShopsStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateOrgShopsStatusResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "UpdateOrgShopsStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "EditInForNickNameRequest": {"type": "object", "properties": {"oldNickname": {"maxLength": 16, "minLength": 4, "type": "string", "description": "Tên nick name", "example": "old nickname", "nullable": true, "x-nullable": true}, "newNickname": {"maxLength": 16, "minLength": 4, "type": "string", "description": "Tên nick name", "example": "new nickname", "nullable": true, "x-nullable": true}}}, "BaseResponseObject": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"type": "object", "nullable": true, "x-nullable": true}}}, "BaseResponseCancelMyShopPackageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelMyShopPackageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CancelMyShopPackageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/StatusResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "StatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CreateRequestTransferRequest": {"type": "object", "properties": {"shopId": {"type": "string", "description": "Id shop", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "description": "<PERSON><PERSON> tiền thanh toán", "nullable": true, "x-nullable": true}, "desc": {"pattern": "^[a-zA-Z\\s\\d]+$|^$", "type": "string", "description": "<PERSON><PERSON><PERSON> dung chuyển tiền", "nullable": true, "x-nullable": true}, "defaultTransfer": {"type": "boolean", "description": "Khi vào shop detail tạo QR mặc định: giá trị = true, khi user nhập giá trị = false", "nullable": true, "x-nullable": true}, "payBoxIds": {"type": "array", "description": "<PERSON><PERSON> sách các paybox nhận thông tin giao dịch và thông báo giao dịch", "items": {"type": "string", "description": "<PERSON><PERSON> sách các paybox nhận thông tin giao dịch và thông báo giao dịch"}, "nullable": true, "x-nullable": true}}}, "BaseResponseCreateRequestTransferResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateRequestTransferResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CreateRequestTransferResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "accountName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}, "requestType": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "bankName": {"type": "string", "nullable": true, "x-nullable": true}, "transferRequestLink": {"type": "string", "nullable": true, "x-nullable": true}, "vietQrCode": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}}}, "SaveTransHisRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "traceId": {"type": "string", "nullable": true, "x-nullable": true}, "transType": {"type": "string", "nullable": true, "x-nullable": true}, "transStatus": {"type": "string", "nullable": true, "x-nullable": true}, "fromAccount": {"type": "string", "nullable": true, "x-nullable": true}, "fromName": {"type": "string", "nullable": true, "x-nullable": true}, "toAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toName": {"type": "string", "nullable": true, "x-nullable": true}, "toNickname": {"type": "string", "nullable": true, "x-nullable": true}, "transAmount": {"type": "number", "nullable": true, "x-nullable": true}, "transCurrency": {"type": "string", "nullable": true, "x-nullable": true}, "availableBalances": {"type": "string", "nullable": true, "x-nullable": true}, "transDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "transNumber": {"type": "string", "nullable": true, "x-nullable": true}, "transDesc": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "refTransaction": {"type": "string", "nullable": true, "x-nullable": true}, "createBy": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseSaveTransHisResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SaveTransHisResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "SaveTransHisResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "UpdateShopInfoRequest": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "S<PERSON> tài khoản nhận tiền của Shop", "nullable": true, "x-nullable": true}, "name": {"maxLength": 140, "minLength": 0, "type": "string", "description": "Tên Shop", "nullable": true, "x-nullable": true}, "owner": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Tên chủ shop", "nullable": true, "x-nullable": true}, "address": {"type": "string", "description": "Địa chỉ shop", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "description": "Địa chỉ chi tiết shop", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "description": "Tọa độ địa chỉ shop", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "description": "Tọa độ địa chỉ shop", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "description": "Ảnh mặt trước của Shop", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "description": "<PERSON><PERSON> gửi Push đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "description": "<PERSON>ó gửi sms đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "description": "<PERSON><PERSON>i email đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"pattern": "^[a-zA-Z\\s\\d]+$|^$", "type": "string", "description": "<PERSON><PERSON><PERSON> dung chuyển tiền mặc định", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}, "accountNoNickname": {"type": "string", "description": "Số tài k<PERSON>n tạo nickname của Shop", "nullable": true, "x-nullable": true}, "nickName": {"type": "string", "description": "nickName của shop", "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "description": "Thông tin nghành nghề Shop", "nullable": true, "x-nullable": true}}}, "CancelRequestMagicPayboxRequest": {"type": "object", "properties": {"magicPayboxUserPlanId": {"type": "string", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}}}, "VerifySoftOtpRequest": {"type": "object", "properties": {"otp": {"type": "string", "nullable": true, "x-nullable": true}}, "description": "Thông tin về softOtp"}, "BaseResponseCancelRequestMagicPayboxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelRequestMagicPayboxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CancelRequestMagicPayboxResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "dayStartService": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "dayChargeServiceTo": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "payment": {"type": "number", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "modelType": {"type": "string", "nullable": true, "x-nullable": true}, "packageRegister": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}, "hardwareId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseUpdateShopInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UpdateShopInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "UpdateShopInfoResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "isOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopStatus": {"type": "string", "nullable": true, "x-nullable": true}, "isShopNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNickNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "industryName": {"type": "string", "nullable": true, "x-nullable": true}}}, "SettingAllowJoinShopRequest": {"type": "object", "properties": {"allowRequest": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseSettingAllowJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SettingAllowJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "SettingAllowJoinShopResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true, "x-nullable": true}, "allowRequest": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "SettingNotifyTransferShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "isNotifyOwner": {"type": "boolean", "description": "true: chủ shop có thể nhận tất cả thông báo giao dịch \nfalse: Chủ shop chỉ nhận được thông báo giao dịch do chính mình tạo", "nullable": true, "x-nullable": true}, "isNotifyMember": {"type": "boolean", "description": "true: <PERSON><PERSON>ậ<PERSON> viên có thể nhận tất cả thông báo giao dịch \nfalse: <PERSON><PERSON><PERSON>n viên chỉ nhận được thông báo giao dịch do chính mình tạo", "nullable": true, "x-nullable": true}}}, "BaseResponseSettingNotifyTransferResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SettingNotifyTransferResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "SettingNotifyTransferResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "RejectRequestToJoinShopRequest": {"type": "object", "properties": {"requestId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseRejectRequestToJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/RejectRequestToJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "RejectRequestToJoinShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "LeaveShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseLeaveShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/LeaveShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "LeaveShopResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true, "x-nullable": true}, "leaveShop": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "RejectShopForNicknameRequest": {"type": "object", "properties": {"approvalBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "postChecks": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON>", "nullable": true, "x-nullable": true}}}, "ApproveShopResponse": {"type": "object", "properties": {"successful": {"type": "string", "nullable": true, "x-nullable": true}, "isRejected": {"type": "boolean", "nullable": true, "x-nullable": true}, "isApproved": {"type": "boolean", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseApproveShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ApproveShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "ApproveShopRequest": {"type": "object", "properties": {"approvalBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "userIdApproval": {"type": "string", "description": "ID người <PERSON>", "nullable": true, "x-nullable": true}, "postChecks": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON>", "nullable": true, "x-nullable": true}, "checkNick": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> nickname", "nullable": true, "x-nullable": true}, "checkShop": {"type": "boolean", "description": "Duyệt shopname", "nullable": true, "x-nullable": true}, "checkFrontImage": {"type": "boolean", "description": "Duyệt h<PERSON>nh <PERSON>nh SHOP ", "nullable": true, "x-nullable": true}}}, "DeleteUserFromShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseDeleteUserFromShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/DeleteUserFromShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "DeleteUserFromShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegisterShopRequest": {"type": "object", "properties": {"accountNo": {"type": "string", "description": "Số tài k<PERSON>n tạo Shop", "nullable": true, "x-nullable": true}, "name": {"maxLength": 32, "minLength": 0, "type": "string", "description": "Tên Shop", "nullable": true, "x-nullable": true}, "owner": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Tên chủ shop", "nullable": true, "x-nullable": true}, "nickName": {"pattern": "[a-z0-9]*$", "type": "string", "description": "nickName của shop", "nullable": true, "x-nullable": true}, "address": {"type": "string", "description": "Địa chỉ shop", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "description": "Địa chỉ chi tiết shop", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "description": "Tọa độ địa chỉ shop", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "description": "Tọa độ địa chỉ shop", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "description": "Ảnh mặt trước của Shop", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "description": "<PERSON><PERSON> gửi Push đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "description": "<PERSON>ó gửi sms đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "description": "<PERSON><PERSON>i email đến nhân viên Shop khi KH thanh toán hay không?", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}, "accountNoNickname": {"type": "string", "description": "Số tài k<PERSON> gán nickname của Shop", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "description": "Thông tin nghành nghề Shop", "nullable": true, "x-nullable": true}}}, "BaseResponseRegisterShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/RegisterShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "RegisterShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "industryName": {"type": "string", "nullable": true, "x-nullable": true}}}, "CreateRequestToJoinShopRequest": {"type": "object", "properties": {"shopCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateRequestToJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateRequestToJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CreateRequestToJoinShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "shopStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CreateDefaultTransferDescShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"pattern": "^[a-zA-Z\\s\\d]+$|^$", "type": "string", "description": "<PERSON><PERSON><PERSON> dung chuyển tiền", "nullable": true, "x-nullable": true}}}, "BaseResponseCreateDefaultTransferDescShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CreateDefaultTransferDescShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CreateDefaultTransferDescShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "isOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopStatus": {"type": "string", "nullable": true, "x-nullable": true}, "isShopNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNickNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CancelRequestToJoinShopRequest": {"type": "object", "properties": {"requestId": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCancelRequestToJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CancelRequestToJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CancelRequestToJoinShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "ApproveRequestToJoinShopRequest": {"type": "object", "properties": {"requestId": {"type": "string", "nullable": true, "x-nullable": true}}}, "ApproveRequestToJoinShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseApproveRequestToJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ApproveRequestToJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "AddShopEmailRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}}}, "AddShopEmailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseAddShopEmailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/AddShopEmailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "UnPairPayBoxShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "description": "Id shop", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "description": "Id device", "nullable": true, "x-nullable": true}}}, "BaseResponseUnPairPayBoxShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/UnPairPayBoxShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "UnPairPayBoxShopResponse": {"type": "object", "properties": {"shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}}}, "SettingStatusShopPayboxRequest": {"type": "object", "properties": {"shopId": {"type": "string", "description": "Id shop", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "description": "Id device", "nullable": true, "x-nullable": true}, "active": {"type": "boolean", "description": "Trạng thái hiện tại của shop", "nullable": true, "x-nullable": true}}}, "BaseResponseSettingStatusShopPayboxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SettingStatusShopPayboxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "SettingStatusShopPayboxResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}}}, "PairPayBoxShopRequest": {"type": "object", "properties": {"shopId": {"type": "string", "description": "Id shop", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "description": "<PERSON><PERSON> th<PERSON><PERSON><PERSON> bị <PERSON>", "nullable": true, "x-nullable": true}, "pairingKey": {"type": "string", "description": "<PERSON><PERSON> th<PERSON><PERSON><PERSON> bị <PERSON>", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "<PERSON>ã nhân viên giới thiệu ", "nullable": true, "x-nullable": true}, "referralBranchCode": {"type": "string", "description": "<PERSON>ã đơn vị quản lý người giới thiệu ", "nullable": true, "x-nullable": true}, "magicPlanId": {"type": "string", "description": "Mã của gói magic paybox", "nullable": true, "x-nullable": true}}}, "BaseResponsePairPayBoxShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PairPayBoxShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "PairPayBoxShopResponse": {"type": "object", "properties": {"shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}}}, "NotifyTransactionRequest": {"type": "object", "properties": {"shopId": {"type": "string", "description": "Id shop", "nullable": true, "x-nullable": true}, "payBoxDeviceId": {"type": "string", "description": "Id của paybox", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}, "rtxNumber": {"type": "string", "nullable": true, "x-nullable": true}, "fromAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toNickname": {"type": "string", "nullable": true, "x-nullable": true}, "fromBin": {"type": "string", "nullable": true, "x-nullable": true}, "fromName": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseNotifyTransactionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/NotifyTransactionResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "NotifyTransactionResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "accountName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}, "requestType": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "bankName": {"type": "string", "nullable": true, "x-nullable": true}, "transferRequestLink": {"type": "string", "nullable": true, "x-nullable": true}, "vietQrCode": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}}}, "RegisterMyShopPackageRequest": {"type": "object", "properties": {"planId": {"type": "string", "description": "Mã gói c<PERSON> myshop", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "description": "Mã giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "<PERSON><PERSON> tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}, "magicPackageId": {"type": "string", "description": "Mã gói magicPaybox theo thi<PERSON>t bị", "nullable": true, "x-nullable": true}, "callPayment": {"type": "boolean", "nullable": true, "x-nullable": true}, "model": {"type": "string", "description": "<PERSON><PERSON> thiết bị paybox", "nullable": true, "x-nullable": true}, "modelType": {"type": "string", "description": "<PERSON><PERSON><PERSON> thiết bị-> DYNAMIC: động. STATIC: tĩnh", "nullable": true, "x-nullable": true}}}, "BaseResponseRegisterMyShopPackageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/RegisterMyShopPackageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "MagicPackageData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "myshopPackageId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "payboxModelId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "price": {"type": "number", "nullable": true, "x-nullable": true}, "priceSt": {"type": "number", "nullable": true, "x-nullable": true}, "vat": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "priceNextYear": {"type": "number", "nullable": true, "x-nullable": true}, "priceNextYearSt": {"type": "number", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ENABLED", "DISABLED", "NOT_DISPLAYED"], "nullable": true, "x-nullable": true}, "avgMonthlyPrice": {"type": "number", "nullable": true, "x-nullable": true}, "totalPayment": {"type": "number", "nullable": true, "x-nullable": true}, "isBestValue": {"type": "boolean", "nullable": true, "x-nullable": true}, "freePeriod": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "MyShopPackageData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "packageCode": {"type": "string", "nullable": true, "x-nullable": true}, "packageName": {"type": "string", "nullable": true, "x-nullable": true}, "maxMember": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "maxShop": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "maxTransaction": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "vat": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "price": {"type": "number", "nullable": true, "x-nullable": true}, "amountPaybox": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "memberType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "shopType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "transactionType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ENABLE", "DISABLE"], "nullable": true, "x-nullable": true}, "iconUrl": {"type": "string", "nullable": true, "x-nullable": true}, "priceWithMagic": {"type": "number", "nullable": true, "x-nullable": true}, "totalPayments": {"type": "number", "nullable": true, "x-nullable": true}, "default": {"type": "boolean", "nullable": true, "x-nullable": true}, "isDisplay": {"type": "boolean", "nullable": true, "x-nullable": true}, "isMagicPaybox": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "RegisterMyShopPackageResponse": {"type": "object", "properties": {"userPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "magicPayboxUserPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "expired": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "trial": {"type": "boolean", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID", "CANCELED"], "nullable": true, "x-nullable": true}, "packageMagicRegister": {"type": "string", "nullable": true, "x-nullable": true}, "planDetail": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MyShopPackageData"}, {"$ref": "#/components/schemas/NullType"}]}, "planMagicPaybox": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MagicPackageData"}, {"$ref": "#/components/schemas/NullType"}]}}}, "ChangeMagicPayboxPackageRequest": {"type": "object", "properties": {"magicPayboxUserPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "magicPayboxPackageId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "Số tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseChangeMagicPayboxPackageReponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ChangeMagicPayboxPackageReponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "ChangeMagicPayboxPackageReponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "magicPayboxName": {"type": "string", "nullable": true, "x-nullable": true}, "payment": {"type": "number", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "string", "nullable": true, "x-nullable": true}, "nextPaymentDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}}}, "UserExecutePaymentRequest": {"type": "object", "properties": {"invoiceId": {"type": "string", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}, "transactionNo": {"type": "string", "description": "Mã giao d<PERSON>ch", "nullable": true, "x-nullable": true}}}, "UserExecutePaymentResgisterRequest": {"type": "object", "properties": {"planId": {"type": "string", "description": "Mã gói c<PERSON> myshop", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "description": "Mã giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "<PERSON><PERSON> tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}, "magicPackageId": {"type": "string", "description": "Mã gói magicPaybox theo thi<PERSON>t bị", "nullable": true, "x-nullable": true}, "model": {"type": "string", "description": "<PERSON><PERSON> thiết bị paybox", "nullable": true, "x-nullable": true}, "modelType": {"type": "string", "description": "<PERSON><PERSON><PERSON> thiết bị-> DYNAMIC: động. STATIC: tĩnh", "nullable": true, "x-nullable": true}}}, "StartJobCalculateFeeRequest": {"type": "object", "properties": {"month": {"type": "integer", "description": "<PERSON>h<PERSON>g thu phí", "format": "int32", "default": 12, "nullable": true, "x-nullable": true}, "year": {"type": "integer", "description": "<PERSON><PERSON>m thu phí", "format": "int32", "default": 2023, "nullable": true, "x-nullable": true}}}, "BalanceChangeIbftRequest": {"type": "object", "properties": {"traceId": {"type": "string", "nullable": true, "x-nullable": true}, "fromName": {"type": "string", "nullable": true, "x-nullable": true}, "toName": {"type": "string", "nullable": true, "x-nullable": true}, "fromAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toNickname": {"type": "string", "nullable": true, "x-nullable": true}, "transactionDate": {"type": "string", "nullable": true, "x-nullable": true}, "amount": {"type": "string", "nullable": true, "x-nullable": true}, "statusCode": {"type": "string", "nullable": true, "x-nullable": true}, "statusDesc": {"type": "string", "nullable": true, "x-nullable": true}, "depositContent": {"type": "string", "nullable": true, "x-nullable": true}, "acquirerCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BalanceChangeIbftResponse": {"type": "object"}, "BaseResponseBalanceChangeIbftResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/BalanceChangeIbftResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseCheckExistedReferralResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckExistedReferralResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckExistedReferralResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "typeReferral": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "hitCount": {"type": "string", "nullable": true, "x-nullable": true}, "referralPhone": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "cifId": {"type": "string", "nullable": true, "x-nullable": true}, "referrerUsername": {"type": "string", "nullable": true, "x-nullable": true}, "organization": {"type": "string", "enum": ["VNPOST", "KL", "OTHER"], "nullable": true, "x-nullable": true}, "referralBranch": {"type": "string", "nullable": true, "x-nullable": true}, "existed": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "CheckUserReferralData": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "typeReferral": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "hitCount": {"type": "string", "nullable": true, "x-nullable": true}, "referralPhone": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "cifId": {"type": "string", "nullable": true, "x-nullable": true}, "referrerUsername": {"type": "string", "nullable": true, "x-nullable": true}, "organization": {"type": "string", "enum": ["VNPOST", "KL", "OTHER"], "nullable": true, "x-nullable": true}, "referralBranch": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckReferralCanCreateShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckReferralCanCreateShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckReferralCanCreateShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckUserReferralData"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseGetDetailTransactionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetDetailTransactionResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetDetailTransactionResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "description": "Số bankCif của người dùng", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "S<PERSON> tài kho<PERSON>n của người dùng", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "description": "Số giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "refTransactionNo": {"type": "string", "description": "Số giao dịch lấy từ core", "nullable": true, "x-nullable": true}, "transactionAt": {"type": "string", "description": "<PERSON><PERSON><PERSON> giao d<PERSON>", "format": "date-time", "nullable": true, "x-nullable": true}, "isAccount": {"type": "integer", "description": "1: <PERSON><PERSON><PERSON>, 2: thẻ", "format": "int32", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "description": "<PERSON><PERSON> tiền giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "fees": {"type": "number", "description": "Phí giao dịch", "nullable": true, "x-nullable": true}, "vat": {"type": "number", "description": "VAT", "nullable": true, "x-nullable": true}, "coefficient": {"type": "integer", "description": "<PERSON><PERSON><PERSON> dịch tăng hay giảm (CREDIT, DEBIT), 1: <PERSON><PERSON><PERSON> dịch tăng, -1: <PERSON><PERSON><PERSON> dịch giảm ", "format": "int32", "nullable": true, "x-nullable": true}, "description": {"type": "string", "description": "<PERSON><PERSON> tả giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "partnerAccountNo": {"type": "string", "description": "<PERSON>ố tài kho<PERSON>n của người thụ hưởng", "nullable": true, "x-nullable": true}, "partnerAccountName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thụ hưởng", "nullable": true, "x-nullable": true}, "partnerAccountAlias": {"type": "string", "description": "<PERSON><PERSON><PERSON> b<PERSON>nh", "nullable": true, "x-nullable": true}, "partnerAccountAvatar": {"type": "string", "description": "Avatar", "nullable": true, "x-nullable": true}, "partnerBankcodeId": {"type": "string", "description": "<PERSON><PERSON> <PERSON>ân hàng  dùng để join c<PERSON><PERSON> bản", "nullable": true, "x-nullable": true}, "partnerBankIdNapas": {"type": "string", "description": "<PERSON><PERSON>ân hàng người thụ hưởng", "nullable": true, "x-nullable": true}, "partnerCitadBankCode": {"type": "integer", "description": "<PERSON><PERSON> ngân hàng trong trư<PERSON><PERSON> hợp chuyển thường ", "format": "int32", "nullable": true, "x-nullable": true}, "partnerBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> hàng người thụ hưởng", "nullable": true, "x-nullable": true}, "partnerBankShortName": {"type": "string", "description": "<PERSON><PERSON>n rút gọn Ngân hàng người thụ hưởng", "nullable": true, "x-nullable": true}, "partnerBankCommonName": {"type": "string", "description": "<PERSON><PERSON>n thường gọi Ngân hàng người thụ hưởng", "nullable": true, "x-nullable": true}, "partnerBankUrl": {"type": "string", "description": "url icon c<PERSON><PERSON> ngân hàng ", "nullable": true, "x-nullable": true}, "partnerBankUrlType": {"type": "string", "description": "loại icon c<PERSON><PERSON> ng<PERSON> hàng ", "nullable": true, "x-nullable": true}, "accountName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "nullable": true, "x-nullable": true}, "transactionCategoryId": {"type": "integer", "description": "<PERSON><PERSON> lo<PERSON>i giao d<PERSON>ch", "format": "int32", "nullable": true, "x-nullable": true}, "transactionCategoryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "nullable": true, "x-nullable": true}, "transactionCategoryIconUrl": {"type": "string", "description": "icon link lo<PERSON>i giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "transactionTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "statusName": {"type": "string", "description": "<PERSON>ên trạng thái", "nullable": true, "x-nullable": true}, "transactionTypeId": {"type": "string", "description": "Id c<PERSON>a lo<PERSON>i giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "cardNo": {"type": "string", "description": "Số thẻ", "nullable": true, "x-nullable": true}, "customerCode": {"type": "string", "description": "<PERSON><PERSON> kh<PERSON>ch hàng <PERSON>h toán hóa đơn", "nullable": true, "x-nullable": true}, "phoneCardValue": {"type": "number", "description": "<PERSON>ệnh giá thẻ nạp điện thoại", "nullable": true, "x-nullable": true}, "serviceId": {"type": "string", "description": "Mã d<PERSON>ch vụ", "format": "uuid", "nullable": true, "x-nullable": true}, "serviceName": {"type": "string", "description": "<PERSON><PERSON><PERSON> v<PERSON>", "nullable": true, "x-nullable": true}, "serviceIconUrl": {"type": "string", "description": "Ảnh icon service", "nullable": true, "x-nullable": true}, "supplierId": {"type": "string", "description": "Mã nhà cung cấp dịch vụ", "format": "uuid", "nullable": true, "x-nullable": true}, "currency": {"type": "integer", "description": "<PERSON><PERSON><PERSON> ti<PERSON>n", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetTransHisInfoShopForCmsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetTransHisInfoShopForCmsResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetTransHisInfoShopForCmsResponse": {"type": "object", "properties": {"transDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "account": {"type": "string", "nullable": true, "x-nullable": true}, "nickNameShop": {"type": "string", "nullable": true, "x-nullable": true}, "transDesc": {"type": "string", "nullable": true, "x-nullable": true}, "transAmount": {"type": "number", "nullable": true, "x-nullable": true}, "transNumber": {"type": "string", "nullable": true, "x-nullable": true}, "fromAccount": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportGetTransHisInfoShopForCmsResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetTransHisInfoShopForCmsResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseTransferRequestDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/TransferRequestDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "TransferRequestDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "accountName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "amount": {"type": "number", "nullable": true, "x-nullable": true}, "description": {"type": "string", "nullable": true, "x-nullable": true}, "type": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "bankName": {"type": "string", "nullable": true, "x-nullable": true}, "bankNameFull": {"type": "string", "nullable": true, "x-nullable": true}, "transferRequestLink": {"type": "string", "nullable": true, "x-nullable": true}, "vietQrCode": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetTransHisInfoShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetTransHisInfoShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetTransHisInfoShopResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "traceId": {"type": "string", "nullable": true, "x-nullable": true}, "transType": {"type": "string", "nullable": true, "x-nullable": true}, "transTypeName": {"type": "string", "nullable": true, "x-nullable": true}, "transTypeIconUrl": {"type": "string", "nullable": true, "x-nullable": true}, "transStatus": {"type": "string", "nullable": true, "x-nullable": true}, "fromAccount": {"type": "string", "nullable": true, "x-nullable": true}, "fromName": {"type": "string", "nullable": true, "x-nullable": true}, "toAccount": {"type": "string", "nullable": true, "x-nullable": true}, "toName": {"type": "string", "nullable": true, "x-nullable": true}, "toNickname": {"type": "string", "nullable": true, "x-nullable": true}, "transAmount": {"type": "number", "nullable": true, "x-nullable": true}, "transCurrency": {"type": "string", "nullable": true, "x-nullable": true}, "availableBalances": {"type": "string", "nullable": true, "x-nullable": true}, "transDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "transNumber": {"type": "string", "nullable": true, "x-nullable": true}, "transDesc": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "totalAmount": {"type": "number", "nullable": true, "x-nullable": true}}}, "PageSupportGetTransHisInfoShopResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetTransHisInfoShopResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseSendMailHistoryTransResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/SendMailHistoryTransResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "SendMailHistoryTransResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseOrgGetListShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/OrgGetListShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "OrgGetListShopDTO": {"type": "object", "properties": {"shopId": {"type": "string", "description": "<PERSON>d c<PERSON><PERSON> hàng", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "Số tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "status": {"type": "string", "description": "Trạng thái shop", "nullable": true, "x-nullable": true}, "postCheckStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "editStatus": {"type": "string", "description": "Trạng thái chỉnh sửa của shop", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng đăng ký", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "description": "Mã số Cif", "nullable": true, "x-nullable": true}, "createDate": {"type": "string", "description": "Ngày đ<PERSON>ng ký shop", "nullable": true, "x-nullable": true}, "postCheckDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "postCheckName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "statusTypeName": {"type": "string", "description": "Tên thái shop", "nullable": true, "x-nullable": true}, "postCheckBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "registerPackageDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON> ký gói", "nullable": true, "x-nullable": true}, "changePackageTotal": {"type": "integer", "description": "<PERSON><PERSON> lần thay đổi gói", "format": "int32", "nullable": true, "x-nullable": true}, "payments": {"type": "number", "description": "<PERSON><PERSON> tiền thanh to<PERSON> gói c<PERSON>c", "nullable": true, "x-nullable": true}, "totalTrans": {"type": "integer", "description": "Tổng số giao dịch", "format": "int32", "nullable": true, "x-nullable": true}, "transRevenue": {"type": "number", "description": "<PERSON><PERSON><PERSON> số giao dịch", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "Mã giới thiệu người dùng", "nullable": true, "x-nullable": true}, "indexShopByCif": {"type": "integer", "description": "Số thứ tự shop theo số CIF", "format": "int32", "nullable": true, "x-nullable": true}, "membersCif": {"type": "array", "description": "<PERSON>h s<PERSON>ch cif thành viên shop", "items": {"type": "string", "description": "<PERSON>h s<PERSON>ch cif thành viên shop"}, "nullable": true, "x-nullable": true}}}, "OrgGetListShopResponse": {"type": "object", "properties": {"listShop": {"type": "array", "items": {"$ref": "#/components/schemas/OrgGetListShopDTO"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetShopDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetShopDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShopDetailResponse": {"type": "object", "properties": {"shopId": {"type": "string", "description": "<PERSON>d c<PERSON><PERSON> hàng", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "address": {"type": "string", "description": "Địa chỉ shop", "nullable": true, "x-nullable": true}, "fullAddress": {"type": "string", "description": "Đ<PERSON><PERSON> chỉ chi tiết", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "description": "Ảnh chụp shop", "nullable": true, "x-nullable": true}, "ownerName": {"type": "string", "description": "Tên chủ shop", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "status": {"type": "string", "description": "Trạng thái shop", "nullable": true, "x-nullable": true}, "statusTypeName": {"type": "string", "description": "Tên thái shop", "nullable": true, "x-nullable": true}, "postCheckStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "postCheckName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "postCheckDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> k<PERSON>", "format": "date-time", "nullable": true, "x-nullable": true}, "editStatus": {"type": "string", "description": "Trạng thái chỉnh sửa của shop", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t gần nhất", "format": "date-time", "nullable": true, "x-nullable": true}, "accountPayment": {"type": "string", "description": "Số TKTT", "nullable": true, "x-nullable": true}, "ownerAccount": {"type": "string", "description": "Chủ TKTT", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "nickName": {"type": "string", "description": "Nickname", "nullable": true, "x-nullable": true}, "contentTransfer": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung chuyển tiền", "nullable": true, "x-nullable": true}, "approvedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "reason": {"type": "string", "description": "<PERSON>ý do từ chối du<PERSON>", "nullable": true, "x-nullable": true}, "packageId": {"type": "string", "description": "Mã gói d<PERSON>ch vụ", "nullable": true, "x-nullable": true}, "registerPackageDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON> ký dịch vụ", "format": "date-time", "nullable": true, "x-nullable": true}, "createDate": {"type": "string", "description": "Ngày tạo shop", "format": "date-time", "nullable": true, "x-nullable": true}, "customerType": {"type": "string", "description": "Loại shop doanh nghiệp / cá nhân", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "description": "cif no", "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "description": "industry_id", "nullable": true, "x-nullable": true}, "industryName": {"type": "string", "description": "industry_name", "nullable": true, "x-nullable": true}}}, "BaseResponseGetShareInfoDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetShareInfoDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShareInfoDetailResponse": {"type": "object", "properties": {"shopId": {"type": "string", "description": "<PERSON>d c<PERSON><PERSON> hàng", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "description": "Nickname shop c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "accountPayment": {"type": "string", "description": "Số TKTT", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "ownerName": {"type": "string", "description": "Tên chủ shop", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "formatShare": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức chia sẻ", "nullable": true, "x-nullable": true}, "shareFee": {"type": "number", "description": "<PERSON><PERSON> chia sẻ", "nullable": true, "x-nullable": true}, "userStatus": {"type": "string", "description": "Tr<PERSON>ng thái chia sẻ", "nullable": true, "x-nullable": true}, "staffName": {"type": "string", "description": "<PERSON><PERSON><PERSON> nhân viên", "nullable": true, "x-nullable": true}, "staffPhone": {"type": "string", "description": "SĐT nhân viên", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t gần nhất", "format": "date-time", "nullable": true, "x-nullable": true}}}, "BaseResponseGetUsersInfoByNicknameInternalResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetUsersInfoByNicknameInternalResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetUsersInfoByNicknameInternalResponse": {"type": "object", "properties": {"shopInfo": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ShopInfoData"}, {"$ref": "#/components/schemas/NullType"}]}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserInfoData"}, "nullable": true, "x-nullable": true}}}, "ShopInfoData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "payBoxDeviceId": {"type": "string", "nullable": true, "x-nullable": true}}}, "UserInfoData": {"type": "object", "properties": {"cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "playerId": {"type": "array", "items": {"type": "string"}, "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetListShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetListShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListShopResponse": {"type": "object", "properties": {"shopId": {"type": "string", "description": "<PERSON>d c<PERSON><PERSON> hàng", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "Số tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "status": {"type": "string", "description": "Trạng thái shop", "nullable": true, "x-nullable": true}, "statusTypeName": {"type": "string", "description": "Tên thái shop", "nullable": true, "x-nullable": true}, "postCheckStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "postCheckName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "editStatus": {"type": "string", "description": "Trạng thái chỉnh sửa của shop", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng đăng ký", "nullable": true, "x-nullable": true}, "createDate": {"type": "string", "description": "Ngày đ<PERSON>ng ký shop", "nullable": true, "x-nullable": true}, "packageId": {"type": "string", "description": "Mã gói đăng ký", "nullable": true, "x-nullable": true}, "registerPackageDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON> ký gói", "nullable": true, "x-nullable": true}, "changePackageTotal": {"type": "integer", "description": "<PERSON><PERSON> lần thay đổi gói", "format": "int32", "nullable": true, "x-nullable": true}, "payments": {"type": "number", "description": "<PERSON><PERSON> tiền thanh to<PERSON> gói c<PERSON>c", "nullable": true, "x-nullable": true}, "postCheckDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "postCheckBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "Mã giới thiệu người dùng", "nullable": true, "x-nullable": true}, "indexShopByCif": {"type": "integer", "description": "Số thứ tự shop theo số CIF", "format": "int32", "nullable": true, "x-nullable": true}, "customerType": {"type": "string", "description": "Loại TK liên kết với shop", "nullable": true, "x-nullable": true}}}, "PageSupportGetListShopResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetListShopResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListShopNoPageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListShopNoPageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListShopNoPageResponse": {"type": "object", "properties": {"listShop": {"type": "array", "items": {"$ref": "#/components/schemas/ShopData"}, "nullable": true, "x-nullable": true}}}, "ShopData": {"type": "object", "properties": {"shopId": {"type": "string", "description": "<PERSON>d c<PERSON><PERSON> hàng", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "Số tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "status": {"type": "string", "description": "Trạng thái shop", "nullable": true, "x-nullable": true}, "postCheckStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "editStatus": {"type": "string", "description": "Trạng thái chỉnh sửa của shop", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng đăng ký", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "description": "Mã số Cif", "nullable": true, "x-nullable": true}, "createDate": {"type": "string", "description": "Ngày đ<PERSON>ng ký shop", "nullable": true, "x-nullable": true}, "packageName": {"type": "string", "description": "<PERSON><PERSON>n gói đ<PERSON>ng ký", "nullable": true, "x-nullable": true}, "packageId": {"type": "string", "description": "Mã gói đăng ký", "nullable": true, "x-nullable": true}, "postCheckDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "postCheckName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hậu ki<PERSON>m", "nullable": true, "x-nullable": true}, "statusTypeName": {"type": "string", "description": "Tên thái shop", "nullable": true, "x-nullable": true}, "postCheckBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> hậu k<PERSON>", "nullable": true, "x-nullable": true}, "registerPackageDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON> ký gói", "nullable": true, "x-nullable": true}, "changePackageTotal": {"type": "integer", "description": "<PERSON><PERSON> lần thay đổi gói", "format": "int32", "nullable": true, "x-nullable": true}, "payments": {"type": "number", "description": "<PERSON><PERSON> tiền thanh to<PERSON> gói c<PERSON>c", "nullable": true, "x-nullable": true}, "totalTrans": {"type": "integer", "description": "Tổng số giao dịch", "format": "int32", "nullable": true, "x-nullable": true}, "transRevenue": {"type": "number", "description": "<PERSON><PERSON><PERSON> số giao dịch", "nullable": true, "x-nullable": true}, "referralCode": {"type": "string", "description": "Mã giới thiệu người dùng", "nullable": true, "x-nullable": true}, "indexShopByCif": {"type": "integer", "description": "Số thứ tự shop theo số CIF", "format": "int32", "nullable": true, "x-nullable": true}, "customerType": {"type": "string", "description": "Loại TK liên kết với shop", "nullable": true, "x-nullable": true}, "targetKlb": {"type": "string", "description": "Trong vòng 45 ngày kể từ thời điểm kích hoạt tài khoản MyShop (từ thời điểm đăng ký mở shop) shop  có: Tối thiểu 10 giao dịch và tổng doanh số giao dịch của shop tối thiểu là 5.000.000 đồng (Năm triệu đồng) -> Thỏa quy đổi chỉ tiêu ", "nullable": true, "x-nullable": true}, "targetMyShop": {"type": "string", "description": "Trong vòng 45 ngày kể từ thời điểm kích hoạt shop phải phát sinh tối thiểu ba (03) giao dịch với tổng doanh số giao dịch của shop tối thiểu là 1.000.000 đồng (Một triệu đồng) -> Đạt chỉ tiêu", "nullable": true, "x-nullable": true}, "totalDateRegister": {"type": "integer", "description": "Số ngày đăng ký shop tính tới thời điểm xuất báo cáo", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetListShareInfoResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetListShareInfoResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListShareInfoResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "Id", "format": "uuid", "nullable": true, "x-nullable": true}, "shopName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "description": "Nickname shop c<PERSON><PERSON> hàng", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "description": "Số tài k<PERSON>n <PERSON>h toán", "nullable": true, "x-nullable": true}, "customerType": {"type": "string", "description": "Loại tài k<PERSON>n thanh toán liên kết với shop", "nullable": true, "x-nullable": true}, "branchId": {"type": "string", "description": "Mã chi nh<PERSON>h", "nullable": true, "x-nullable": true}, "shopOwnerPhoneNo": {"type": "string", "description": "SĐT chủ shop", "nullable": true, "x-nullable": true}, "shopStaffPhoneNo": {"type": "string", "description": "SĐT nhân viên shop", "nullable": true, "x-nullable": true}, "status": {"type": "string", "description": "Tr<PERSON>ng thái chia sẻ", "nullable": true, "x-nullable": true}, "statusTypeName": {"type": "string", "description": "<PERSON>ên lo<PERSON>i thái chia sẻ", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "createDateShop": {"type": "string", "description": "Mã shop", "nullable": true, "x-nullable": true}, "approveDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> hoặc từ chối yêu cầu vào shop", "nullable": true, "x-nullable": true}, "staffId": {"type": "string", "description": "Id c<PERSON>a nhân viên", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng đăng ký", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "description": "Mã số Cif", "nullable": true, "x-nullable": true}, "packageId": {"type": "string", "description": "Mã gói đăng ký", "nullable": true, "x-nullable": true}, "orderId": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "emailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "pushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "smsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "PageSupportGetListShareInfoResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetListShareInfoResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListShareInfoNoPageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListShareInfoNoPageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListShareInfoNoPageResponse": {"type": "object", "properties": {"listShareInfo": {"type": "array", "items": {"$ref": "#/components/schemas/GetListShareInfoResponse"}, "nullable": true, "x-nullable": true}}}, "BaseResponseCheckExistAccountNoLinkToShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckExistAccountNoLinkToShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckExistAccountNoLinkToShopResponse": {"type": "object", "properties": {"exist": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetShopsUserCreatedOrJoinedResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetShopsUserCreatedOrJoinedResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShopsUserCreatedOrJoinedResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "isOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "requestJoinShopStatus": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportGetShopsUserCreatedOrJoinedResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetShopsUserCreatedOrJoinedResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetShopsActivatedButNotLinkedToPayBoxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetShopsActivatedButNotLinkedToPayBoxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShopsActivatedButNotLinkedToPayBoxResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "PageSupportGetShopsActivatedButNotLinkedToPayBoxResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetShopsActivatedButNotLinkedToPayBoxResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetListUserResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetListUserResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListUserResponse": {"type": "object", "properties": {"userShopId": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}, "fullName": {"type": "string", "nullable": true, "x-nullable": true}, "phoneNumber": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["WAITING", "CANCEL_REQUEST", "REJECTED_REQUEST", "ACTIVE", "INACTIVE", "LEAVE"], "nullable": true, "x-nullable": true}}}, "PageSupportGetListUserResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetListUserResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListEmailOfShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListEmailOfShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListEmailOfShopResponse": {"type": "object", "properties": {"emailShops": {"type": "array", "items": {"$ref": "#/components/schemas/ShopEmailQueryInfo"}, "nullable": true, "x-nullable": true}}}, "ShopEmailQueryInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetListEmailShopPageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetListEmailShopPageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListEmailShopPageResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "nullable": true, "x-nullable": true}, "email": {"type": "string", "nullable": true, "x-nullable": true}}}, "PageSupportGetListEmailShopPageResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetListEmailShopPageResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "AccountPayment": {"type": "object", "properties": {"account": {"type": "string", "nullable": true, "x-nullable": true}, "accountName": {"type": "string", "nullable": true, "x-nullable": true}, "currency": {"type": "string", "nullable": true, "x-nullable": true}, "balance": {"type": "number", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "branchName": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetAccountsOrgLinkPersonResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetAccountsOrgLinkPersonResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetAccountsOrgLinkPersonResponse": {"type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/components/schemas/AccountPayment"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetShopDetailByShopIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetShopDetailByShopIdResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShopDetailByShopIdResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "isOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopStatus": {"type": "string", "nullable": true, "x-nullable": true}, "isShopNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNickNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "isFrontImageInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "allowRequest": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNotifyOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNotifyMember": {"type": "boolean", "nullable": true, "x-nullable": true}, "payBoxDetails": {"type": "array", "items": {"$ref": "#/components/schemas/PayBoxQueryInfo"}, "nullable": true, "x-nullable": true}, "industryId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "industryName": {"type": "string", "nullable": true, "x-nullable": true}}}, "PayBoxQueryInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "hardwareId": {"type": "string", "nullable": true, "x-nullable": true}, "displayName": {"type": "string", "nullable": true, "x-nullable": true}, "manToken": {"type": "string", "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "location": {"type": "string", "nullable": true, "x-nullable": true}, "version": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}, "online": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetShopDetailRecentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetShopDetailRecentResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetShopDetailRecentResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "shopCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "owner": {"type": "string", "nullable": true, "x-nullable": true}, "nickname": {"type": "string", "nullable": true, "x-nullable": true}, "isOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "address": {"type": "string", "nullable": true, "x-nullable": true}, "addressDetail": {"type": "string", "nullable": true, "x-nullable": true}, "latitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "longitudeAddress": {"type": "number", "format": "double", "nullable": true, "x-nullable": true}, "fontImage": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED", "LOCKED", "CANCEL", "BLOCKED_UNPAID"], "nullable": true, "x-nullable": true}, "postInspectionStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "WAITING"], "nullable": true, "x-nullable": true}, "reason": {"type": "string", "nullable": true, "x-nullable": true}, "isPushNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isSmsNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "isEmailNotify": {"type": "boolean", "nullable": true, "x-nullable": true}, "createdBy": {"type": "string", "nullable": true, "x-nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "lastModifiedBy": {"type": "string", "nullable": true, "x-nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "defaultTransferDesc": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopId": {"type": "string", "nullable": true, "x-nullable": true}, "requestJoinShopStatus": {"type": "string", "nullable": true, "x-nullable": true}, "isShopNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNickNameInvalid": {"type": "boolean", "nullable": true, "x-nullable": true}, "allowRequest": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNotifyOwner": {"type": "boolean", "nullable": true, "x-nullable": true}, "isNotifyMember": {"type": "boolean", "nullable": true, "x-nullable": true}, "payBoxDetails": {"type": "array", "items": {"$ref": "#/components/schemas/PayBoxQueryInfo"}, "nullable": true, "x-nullable": true}, "recentAccessDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckValidNicknameResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckValidNicknameResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckValidNicknameResponse": {"type": "object", "properties": {"successful": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckUserCreatedOrJoinShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckUserCreatedOrJoinShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckUserCreatedOrJoinShopResponse": {"type": "object", "properties": {"createdOrJoinShop": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckValidShopnameResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckValidShopnameResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckValidShopnameResponse": {"type": "object", "properties": {"successful": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckShopCodeExistResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckShopCodeExistResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckShopCodeExistResponse": {"type": "object", "properties": {"exist": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckLinkWithOrgResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckLinkWithOrgResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckLinkWithOrgResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseCheckCanCreateShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckCanCreateShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckCanCreateShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "detailPackage": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MyShopPackageData"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseGetListDevicePayboxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListDevicePayboxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListDevicePayboxResponse": {"type": "object", "properties": {"payboxTypeList": {"type": "array", "items": {"$ref": "#/components/schemas/PayboxTypeDto"}, "nullable": true, "x-nullable": true}}}, "PayboxTypeDto": {"type": "object", "properties": {"type": {"type": "string", "nullable": true, "x-nullable": true}, "title": {"type": "string", "nullable": true, "x-nullable": true}, "screen": {"type": "string", "nullable": true, "x-nullable": true}, "notification": {"type": "string", "nullable": true, "x-nullable": true}, "connection": {"type": "string", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListAllPayboxModelResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListAllPayboxModelResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListAllPayboxModelResponse": {"type": "object", "properties": {"dynamic": {"type": "array", "items": {"$ref": "#/components/schemas/PayboxModelData"}, "nullable": true, "x-nullable": true}, "static": {"type": "array", "items": {"$ref": "#/components/schemas/PayboxModelData"}, "nullable": true, "x-nullable": true}}}, "PayboxModelData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "vendor": {"type": "string", "nullable": true, "x-nullable": true}, "iconUrl": {"type": "string", "nullable": true, "x-nullable": true}, "packageRegister": {"type": "string", "nullable": true, "x-nullable": true}, "isQrDynamic": {"type": "boolean", "nullable": true, "x-nullable": true}, "isWifi": {"type": "boolean", "nullable": true, "x-nullable": true}, "isMobileData": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetAllPayBoxOfShopResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetAllPayBoxOfShopResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetAllPayBoxOfShopResponse": {"type": "object", "properties": {"payBoxs": {"type": "array", "items": {"$ref": "#/components/schemas/PayBoxQueryInfo"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetDevicePayBoxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetDevicePayBoxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetDevicePayBoxResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "hardwareId": {"type": "string", "nullable": true, "x-nullable": true}, "displayName": {"type": "string", "nullable": true, "x-nullable": true}, "manToken": {"type": "string", "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "location": {"type": "string", "nullable": true, "x-nullable": true}, "version": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}, "online": {"type": "boolean", "nullable": true, "x-nullable": true}, "isDevicePurchased": {"type": "boolean", "nullable": true, "x-nullable": true}, "isMagic": {"type": "boolean", "nullable": true, "x-nullable": true}, "magicPayboxUserPlanQueryList": {"type": "array", "items": {"$ref": "#/components/schemas/MagicPayboxUserPlanQuery"}, "nullable": true, "x-nullable": true}}}, "BaseResponseCheckDevicePurchasedResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/CheckDevicePurchasedResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "CheckDevicePurchasedResponse": {"type": "object", "properties": {"magicPayboxUserPlanQueryList": {"type": "array", "items": {"$ref": "#/components/schemas/MagicPayboxUserPlanQuery"}, "nullable": true, "x-nullable": true}, "devicePurchased": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "MagicPayboxUserPlanQuery": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "userPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "magicPayboxPackageId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "payment": {"type": "number", "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "priceNextYear": {"type": "number", "nullable": true, "x-nullable": true}, "vat": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "dayChargeServiceFrom": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "dayChargeServiceTo": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "dayStartService": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID", "CANCELED"], "nullable": true, "x-nullable": true}, "modelType": {"type": "string", "enum": ["DYNAMIC", "STATIC"], "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "planId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "BaseResponseGetMyShopPackageByIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetMyShopPackageByIdResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetMyShopPackageByIdResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "packageCode": {"type": "string", "nullable": true, "x-nullable": true}, "packageName": {"type": "string", "nullable": true, "x-nullable": true}, "maxMember": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "maxShop": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "maxTransaction": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "vat": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "price": {"type": "number", "nullable": true, "x-nullable": true}, "amountPaybox": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "memberType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "shopType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "transactionType": {"type": "string", "enum": ["LIMITED", "UNLIMITED"], "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ENABLE", "DISABLE"], "nullable": true, "x-nullable": true}, "iconUrl": {"type": "string", "nullable": true, "x-nullable": true}, "priceWithMagic": {"type": "number", "nullable": true, "x-nullable": true}, "default": {"type": "boolean", "nullable": true, "x-nullable": true}, "isDisplay": {"type": "boolean", "nullable": true, "x-nullable": true}, "isMagicPaybox": {"type": "boolean", "nullable": true, "x-nullable": true}, "payments": {"type": "number", "nullable": true, "x-nullable": true}, "trial": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetRegisterPackagesDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetRegisterPackagesDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetRegisterPackagesDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "expired": {"type": "string", "nullable": true, "x-nullable": true}, "trial": {"type": "boolean", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID"], "nullable": true, "x-nullable": true}, "planDetail": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MyShopPackageData"}, {"$ref": "#/components/schemas/NullType"}]}, "registerDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "countMagicPlanActive": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "registerMagicPlan": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListMyShopPackageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListMyShopPackageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListMyShopPackageResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/components/schemas/MyShopPackageData"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetListRegisterMagicPayboxResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListRegisterMagicPayboxResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListRegisterMagicPayboxResponse": {"type": "object", "properties": {"listRegisterMagic": {"type": "array", "items": {"$ref": "#/components/schemas/GetRegisterMagicPayboxDetailResponse"}, "nullable": true, "x-nullable": true}, "countMagicPaybox": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "GetRegisterMagicPayboxDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "dayStartService": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "dayChargeServiceTo": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "model": {"type": "string", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "payment": {"type": "number", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthFree": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "modelType": {"type": "string", "nullable": true, "x-nullable": true}, "packageRegister": {"type": "string", "nullable": true, "x-nullable": true}, "status": {"type": "string", "nullable": true, "x-nullable": true}, "hardwareId": {"type": "string", "nullable": true, "x-nullable": true}, "displayName": {"type": "string", "nullable": true, "x-nullable": true}, "payboxModelId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "magicPackageId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "deviceId": {"type": "string", "nullable": true, "x-nullable": true}, "shopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "BaseResponseGetListMagicPackageResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListMagicPackageResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListMagicPackageResponse": {"type": "object", "properties": {"magicPackages": {"type": "array", "items": {"$ref": "#/components/schemas/MagicPackageData"}, "nullable": true, "x-nullable": true}}}, "BaseResponseGetMagicPackageDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetMagicPackageDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetMagicPackageDetailResponse": {"type": "object", "properties": {"magicPackage": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/MagicPackageData"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseGetListIndustryResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetListIndustryResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetListIndustryResponse": {"type": "object", "properties": {"industries": {"type": "array", "items": {"$ref": "#/components/schemas/IndustryQuery"}, "nullable": true, "x-nullable": true}}}, "IndustryQuery": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "name": {"type": "string", "nullable": true, "x-nullable": true}, "displayOrder": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponseShowNotifyPaymentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/ShowNotifyPaymentResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "ShowNotifyPaymentResponse": {"type": "object", "properties": {"title": {"type": "string", "nullable": true, "x-nullable": true}, "content": {"type": "string", "nullable": true, "x-nullable": true}, "type": {"type": "string", "enum": ["PROCESSING", "SUCCESS", "ERROR"], "nullable": true, "x-nullable": true}}}, "BaseResponsePreviewInvoiceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PreviewInvoiceResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "PreviewInvoiceResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "planId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "planName": {"type": "string", "nullable": true, "x-nullable": true}, "planPrice": {"type": "number", "nullable": true, "x-nullable": true}, "payments": {"type": "number", "nullable": true, "x-nullable": true}, "vat": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthFee": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "dayOfUse": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalFee": {"type": "number", "nullable": true, "x-nullable": true}, "rtxnNo": {"type": "string", "nullable": true, "x-nullable": true}, "feeNoVat": {"type": "number", "nullable": true, "x-nullable": true}, "registerPackageMagic": {"type": "string", "nullable": true, "x-nullable": true}, "monthPeriod": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "chargeDateFrom": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "chargeDateTo": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "userPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "transDate": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportGetReportFeeResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportGetReportFeeResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "GetReportFeeResponse": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "phone": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "planId": {"type": "string", "nullable": true, "x-nullable": true}, "planRegisterDate": {"type": "string", "format": "date-time", "nullable": true, "x-nullable": true}, "totalFeePaid": {"type": "number", "nullable": true, "x-nullable": true}, "totalPaid": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalFeeUnPaid": {"type": "number", "nullable": true, "x-nullable": true}, "totalUnPaid": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "paymentStatus": {"type": "string", "enum": ["PAID", "UNPAID"], "nullable": true, "x-nullable": true}, "serviceStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID"], "nullable": true, "x-nullable": true}}}, "PageSupportGetReportFeeResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/GetReportFeeResponse"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "BaseResponseGetReportFeeDetailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/GetReportFeeDetailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "FeeHistoryData": {"type": "object", "properties": {"accountNo": {"type": "string", "description": "<PERSON><PERSON><PERSON>h toán", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "description": "Mã giao d<PERSON>ch", "nullable": true, "x-nullable": true}, "rtxnNo": {"type": "string", "description": "<PERSON><PERSON> hạch toán", "nullable": true, "x-nullable": true}, "planId": {"type": "string", "description": "id gói", "format": "uuid", "nullable": true, "x-nullable": true}, "planRegisterDate": {"type": "string", "description": "ng<PERSON>y đ<PERSON>ng ký gói", "format": "date-time", "nullable": true, "x-nullable": true}, "dayOfUse": {"type": "integer", "description": "<PERSON><PERSON> ngày sử dụng", "format": "int32", "nullable": true, "x-nullable": true}, "totalFee": {"type": "number", "description": "Số tiền TT", "nullable": true, "x-nullable": true}, "monthFee": {"type": "string", "description": "Kỳ TT", "format": "date", "nullable": true, "x-nullable": true}, "transactionDate": {"type": "string", "description": "thời gian TT", "format": "date-time", "nullable": true, "x-nullable": true}, "paymentStatus": {"type": "string", "description": "Trạng thái TT", "enum": ["PAID", "UNPAID"], "nullable": true, "x-nullable": true}}}, "GetReportFeeDetailResponse": {"type": "object", "properties": {"branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "phone": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "serviceStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCK_UNPAID"], "nullable": true, "x-nullable": true}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/FeeHistoryData"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}}}, "BaseResponsePageSupportInvoiceData": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/PageSupportInvoiceData"}, {"$ref": "#/components/schemas/NullType"}]}}}, "InvoiceData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "invoiceCode": {"type": "string", "nullable": true, "x-nullable": true}, "userId": {"type": "string", "nullable": true, "x-nullable": true}, "customerName": {"type": "string", "nullable": true, "x-nullable": true}, "cifNo": {"type": "string", "nullable": true, "x-nullable": true}, "branchCode": {"type": "string", "nullable": true, "x-nullable": true}, "accountNo": {"type": "string", "nullable": true, "x-nullable": true}, "phone": {"type": "string", "nullable": true, "x-nullable": true}, "totalFee": {"type": "number", "nullable": true, "x-nullable": true}, "vat": {"type": "number", "nullable": true, "x-nullable": true}, "rtxnNo": {"type": "string", "nullable": true, "x-nullable": true}, "transactionNo": {"type": "string", "nullable": true, "x-nullable": true}, "transactionFee": {"type": "string", "nullable": true, "x-nullable": true}, "userPlanId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}, "packageName": {"type": "string", "nullable": true, "x-nullable": true}, "packageCode": {"type": "string", "nullable": true, "x-nullable": true}, "dayOfUse": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "monthFee": {"type": "string", "format": "date", "nullable": true, "x-nullable": true}, "status": {"type": "string", "enum": ["PAID", "UNPAID"], "nullable": true, "x-nullable": true}, "transDate": {"type": "string", "nullable": true, "x-nullable": true}, "showNotify": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "PageSupportInvoiceData": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceData"}, "nullable": true, "x-nullable": true}, "pageNumber": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "pageSize": {"type": "integer", "format": "int32", "nullable": true, "x-nullable": true}, "totalElements": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}, "last": {"type": "boolean", "nullable": true, "x-nullable": true}, "first": {"type": "boolean", "nullable": true, "x-nullable": true}, "totalPages": {"type": "integer", "format": "int64", "nullable": true, "x-nullable": true}}}, "DeleteShopByIdRequest": {"type": "object", "properties": {"shopId": {"type": "string", "nullable": true, "x-nullable": true}, "verifySoftOtp": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/VerifySoftOtpRequest"}, {"$ref": "#/components/schemas/NullType"}]}}}, "BaseResponseDeleteShopByIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/DeleteShopByIdResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "DeleteShopByIdResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "DeleteShopEmailRequest": {"type": "object", "properties": {"emailShopId": {"type": "string", "format": "uuid", "nullable": true, "x-nullable": true}}}, "BaseResponseDeleteShopEmailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}, "code": {"type": "string", "nullable": true, "x-nullable": true}, "data": {"nullable": true, "x-nullable": true, "anyOf": [{"$ref": "#/components/schemas/DeleteShopEmailResponse"}, {"$ref": "#/components/schemas/NullType"}]}}}, "DeleteShopEmailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "nullable": true, "x-nullable": true}}}, "NullType": {"description": "for adding nullability to a ref", "enum": [null]}}, "securitySchemes": {"Authorization": {"type": "http", "description": "Basic key", "in": "header", "scheme": "basic", "bearerFormat": "Basic [key]"}}}}