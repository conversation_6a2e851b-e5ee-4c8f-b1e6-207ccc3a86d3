name: mobile_banking
description: Sunshine mobile banking

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.1.17+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  ksb_bloc:
    path: ./bloc
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5
  #  firebase_ml_vision:
  #    git:
  #      url: git://github.com/algirdasmac/flutterfire
  #      path: packages/firebase_ml_vision
  flutter_jailbreak_detection:
    path: ./flutter_jailbreak_detection
  common:
    path: ./modules/common
  ks_chat:
    path: ./modules/ks_chat
  jitsi_meet:
    path: ./modules/jitsi_meet
  open_api_umee:
    path: ./modules/open-api-umee
  ksb_common:
    path: ./modules/ksb_common
  umee_shop:
    path: ./modules/myshop
  ekyc:
    path: ./modules/ekyc
  passport_reader_plugin:
    path: ./modules/passport-reader-plugin
  face_liveness_detector:
    path: ./modules/face_liveness_detector

  camera: ^0.10.5+9
  dio: ^5.7.0
  logger: ^2.3.0
  uuid: ^4.4.0
  path_provider: ^2.0.11
  flutter_svg: ^2.0.7
  image: ^3.1.3
  flutter_simple_dependency_injection: ^2.0.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  shared_preferences: ^2.0.15
  animations: ^2.0.2
  percent_indicator: ^4.0.1
  video_player: ^2.4.5
  image_picker: ^1.1.2
  image_cropper: ^4.0.1
  qr_flutter: ^4.1.0
  keyboard_attachable: ^2.1.0
  google_maps_flutter: ^2.1.10
  share: ^2.0.4
  mobile_scanner: ^5.1.1
  lottie: ^2.3.2
  modal_bottom_sheet: ^3.0.0-pre
  flutter_pdfview: ^1.3.4
  url_launcher: ^6.3.0
  openapi_generator_annotations: ^6.1.0
#  flare_flutter: ^3.0.2
  flutter_markdown: ^0.7.3
  reorderables: ^0.6.0
  flutter_slidable: ^4.0.0
  flutter_echarts: ^2.3.0
  webview_flutter: ^4.10.0
  webview_flutter_wkwebview: ^3.18.4
  steps_indicator: ^1.3.0
  flutter_keyboard_visibility: ^5.3.0
  month_picker_dialog: ^4.0.0
  dotted_line: ^3.1.0
  device_info: ^2.0.3
  flutter_secure_storage: ^6.1.0
  otp: ^3.1.1
  local_auth: ^2.1.7
  flutter_staggered_grid_view: ^0.7.0
  tuple: ^2.0.0
  photo_view: ^0.15.0
  grouped_list: ^6.0.0
  pin_code_fields: ^8.0.1
  image_gallery_saver: ^2.0.3
  carousel_slider: ^5.0.0
  screenshot: ^3.0.0
  jiffy: ^6.3.1
  flutter_switch: ^0.3.2
  badges: ^3.1.2
  map_launcher: ^3.5.0
  http_certificate_pinning: ^2.1.3
  another_flushbar: ^1.12.30
  contacts_service: ^0.6.3
  marquee: ^2.2.1
  google_mlkit_face_detection: ^0.11.1
  loading_indicator: ^3.0.3
  file_picker: ^8.0.6
  flutter_downloader: ^1.11.8
  flutter_screenutil: ^5.9.0
  app_settings: 5.1.1
  smooth_star_rating_null_safety: ^1.0.4+2
  geolocator: ^9.0.2
  mime: ^1.0.2
  showcaseview: ^3.0.0
  version: ^3.0.2
  flutter_html: ^3.0.0-beta.2
  flutter_rating_bar: ^4.0.1
  flutter_svg_provider: ^1.0.4
  flutter_local_notifications: ^17.2.3
  dotted_border: ^2.0.0+1
  pull_to_refresh: ^2.0.0
  flutter_speed_dial: ^6.0.0
  step_progress_indicator: ^1.0.2
  timelines_plus: ^1.0.6
  table_calendar: ^3.0.6
  geocoding: ^2.0.4
  expandable_page_view: ^1.0.13
  substring_highlight: ^1.0.33
  multiple_localization: ^0.5.0
  file: ^7.0.0
  vibration: ^1.7.6
  collection: ^1.17.0
  rxdart: ^0.27.7
  permission_handler: ^11.3.1
  firebase_messaging: ^14.9.4
  onesignal_flutter: ^5.0.3
  translations_cleaner: ^0.0.5
  device_info_plus: ^10.1.0
  infinite_scroll_pagination: ^4.0.0
  shake: ^2.2.0
  package_info_plus: ^8.0.2
  firebase_crashlytics: ^3.5.7
  melos: ^6.0.0
  flare_flutter:
    git:
      url: https://github.com/mbfakourii/Flare-Flutter.git
      path: flare_flutter
      ref: remove_hashValues


dependency_overrides:
  win32: ^5.8.0
  archive: ^3.6.1
  sensors_plus: ^6.1.1
  http: ^1.2.2
  permission_handler_apple: 9.4.5

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.4.14
  #override for build_runner
  frontend_server_client: ^4.0.0
  openapi_generator: ^6.1.0
  flutter_lints: ^2.0.1
  # intl_utils version for plugin install when open this project
  intl_utils: ^2.4.1
  flat: ^0.5.0
  gato: ^0.0.5+1
  path: ^1.8.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/v2/images/
    - assets/v2/images/theme/
    - assets/v2/icon/
    - assets/map/
    - assets/icon/
    - assets/icon/home/
    - assets/markdown/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
