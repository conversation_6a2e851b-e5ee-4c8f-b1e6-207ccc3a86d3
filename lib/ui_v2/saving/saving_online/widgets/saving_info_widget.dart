import 'package:common/ks_common.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/model/base_model.dart';

class SavingInfoWidget extends StatefulWidget {
  final List<BaseModel>? data;
  final Color? backgroundColor;

  const SavingInfoWidget({
    Key? key,
    this.data,
    this.backgroundColor,
  }) : super(key: key);

  @override
  _SavingInfoWidgetState createState() => _SavingInfoWidgetState();
}

class _SavingInfoWidgetState extends State<SavingInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      itemCount: widget.data?.length ?? 0,
      itemBuilder: (context, index) {
        final model = widget.data?[index];
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                model?.title ?? '',
                style: StyleApp.bodyStyle(context),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    model?.icon?.isNotEmpty == true
                        ? ImageAssets.svgAssets(model?.icon ?? '',
                            width: 12.0, height: 10.0, color: model?.rightColor)
                        : Container(),
                    const SizedBox(width: 5.0),
                    Flexible(
                      child: Text(
                        model?.right ?? '',
                        style:
                            StyleApp.subtitle1(context, model?.isPrice == true)
                                ?.copyWith(
                          color: model?.rightColor,
                          backgroundColor: widget.backgroundColor ??
                              model?.rightColor?.withOpacity(0.1),
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        indent: 16,
        endIndent: 16,
      ),
    );
  }
}
