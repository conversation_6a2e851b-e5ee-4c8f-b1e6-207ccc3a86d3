import 'package:face_liveness_detector/face_liveness_detector.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class FaceLivenessDetectorWidget extends StatefulWidget {
  const FaceLivenessDetectorWidget({
    super.key,
    required this.sessionId,
    required this.region,
    required this.accessKeyId,
    this.secretAccessKey,
    this.sessionToken,
    this.onComplete,
    this.onError,
  });
  final String sessionId;
  final String region;
  final String? accessKeyId;
  final String? secretAccessKey;
  final String? sessionToken;
  final Function()? onComplete;
  final ValueChanged<String>? onError;

  @override
  State<FaceLivenessDetectorWidget> createState() =>
      _FaceLivenessDetectorWidgetState();
}

class _FaceLivenessDetectorWidgetState extends State<FaceLivenessDetectorWidget>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _startLoading();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _startLoading() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Remove default padding and ensure full screen
      extendBodyBehindAppBar: true,
      extendBody: true,
      backgroundColor: const Color(0xFF2E2D36),
      body: SafeArea(
        // Allow the view to extend beyond safe area
        top: false,
        bottom: false,
        left: false,
        right: false,
        child: Stack(
          children: [
            // Loading view
            if (_isLoading)
              Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFF2E2D36),
                child: Center(child: ring),
              ),

            // FaceLivenessDetector view with fade animation
            AnimatedBuilder(
              animation: _opacityAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _isLoading ? 0.0 : _opacityAnimation.value,
                  child: Container(
                    color: const Color(0xFF2E2D36),
                    width: double.infinity,
                    height: double.infinity,
                    child: FaceLivenessDetector(
                      sessionId: widget.sessionId,
                      region: widget.region,
                      accessKeyId: widget.accessKeyId,
                      secretAccessKey: widget.secretAccessKey,
                      sessionToken: widget.sessionToken,
                      onComplete: () {
                        if (mounted) Navigator.of(context).pop(true);
                        widget.onComplete?.call();
                      },
                      onError: (String error) {
                        // Add small delay to ensure smooth transition
                        if (mounted) Navigator.of(context).pop();

                        widget.onError?.call(error);
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget get ring => const SizedBox(
        width: 30.0,
        height: 30.0,
        child: Center(
          child: CupertinoActivityIndicator(
            color: Colors.white,
          ),
        ),
      );
}
